image: atlassian/default-image:4
definitions:
  services:
    docker:
      memory: 4096
  steps:
    - step: &build-image-push-harbor
        name: Build and Push to Harbor
        image: docker:24.0.6
        size: 2x
        services:
          - docker
        caches:
          - docker
        script:
          # Install dependencies
          - apk add --no-cache curl jq git
          
          # Force Docker to use the default driver
          - export DOCKER_BUILDKIT=0
          
          # Clean up any existing containers/images to free memory
          - docker system prune -f || true

          # Login to Harbor
          - echo $HARBOR_PASSWORD | docker login $HARBOR_URL -u $HARBOR_USERNAME --password-stdin

          # Build image with proper tags
          - git fetch --tags --unshallow || echo "Warning Could not fetch tags"
          - export IMAGE_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "v1.0.0")
          - export FULL_IMAGE_NAME=$HARBOR_URL/$HARBOR_PROJECT/$HARBOR_PROJECT
          - echo "Building with IMAGE_TAG $IMAGE_TAG"
          - echo "Full image name $FULL_IMAGE_NAME"

          # Verify Dockerfile exists
          - ls -la Dockerfile.dev

          # Build image with all environment variables as build args
          - >
            docker build
            --tag $FULL_IMAGE_NAME:$IMAGE_TAG
            --tag $FULL_IMAGE_NAME:JP
            --tag $FULL_IMAGE_NAME:latest
            --file Dockerfile.dev
            --memory=3g
            --memory-swap=3g
            --build-arg VITE_API_ENDPOINT="$VITE_API_ENDPOINT"
            --build-arg VITE_AWS_S3_ENDPOINT="$VITE_AWS_S3_ENDPOINT"
            --build-arg VITE_APP_NAME="$VITE_APP_NAME"
            --build-arg VITE_APP_FRONTEND="$VITE_APP_FRONTEND"
            --build-arg VITE_APP_VERSION="$VITE_APP_VERSION"
            --build-arg VITE_HOST="$VITE_HOST"
            --build-arg VITE_HOST_PORT="$VITE_HOST_PORT"
            --build-arg VITE_REVERB_APP_ID="$VITE_REVERB_APP_ID"
            --build-arg VITE_REVERB_APP_KEY="$VITE_REVERB_APP_KEY"
            --build-arg VITE_REVERB_APP_SECRET="$VITE_REVERB_APP_SECRET"
            --build-arg VITE_REVERB_HOST="$VITE_REVERB_HOST"
            --build-arg VITE_REVERB_WSPORT="$VITE_REVERB_WSPORT"
            --build-arg VITE_REVERB_WSSPORT="$VITE_REVERB_WSSPORT"
            --build-arg VITE_REVERB_SCHEME="$VITE_REVERB_SCHEME"
            .
          
          # Clean up build cache after build
          - docker builder prune -f || true

          # Push images
          - docker push $FULL_IMAGE_NAME:$IMAGE_TAG
          - docker push $FULL_IMAGE_NAME:JP
          - docker push $FULL_IMAGE_NAME:latest

          # Generate deployment artifacts
          - mkdir -p artifacts
          - echo "IMAGE_TAG=$IMAGE_TAG" > artifacts/build.env
          - echo "FULL_IMAGE_NAME=$FULL_IMAGE_NAME:$IMAGE_TAG" >> artifacts/build.env
          - echo "BUILD_NUMBER=$BITBUCKET_BUILD_NUMBER" >> artifacts/build.env
          - echo "COMMIT_SHA=$BITBUCKET_COMMIT" >> artifacts/build.env
        artifacts:
          - artifacts/**

pipelines:
  custom:
    build-image-push-harbor:
      - step: *build-image-push-harbor
# pipelines:
#   branches:
#     staging:
#       - step: *build-image

# main:
#   - step: *build-image
#   - step: *security-scan

# tags:
#    "*.*.*-beta":
#     - step: *build-image
#     - step: *security-scan
