import { ChangeEvent, FC, Fragment, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { TableColumn } from "react-data-table-component";
import { useUserManagementActions } from "@state/reducer/users-management";
import { RootState } from "@state/store";
import { FaUser, FaUserPlus } from "react-icons/fa";
import { CiEdit, CiTrash } from "react-icons/ci";
import { IActions } from "@interface/common.interface";
import { IUser } from "@interface/user.interface";
import CreateUserForm from "../Forms/CreateUserForm";
import { joinArray, pluck } from "@helpers/array";
import Tooltip from "@components/common/Tooltip";
import Table from "@components/common/Table";
import Modal from "@components/common/Modal";
import ActionButtons from "@components/common/ActionButtons";
import Badge from "@components/common/Badge";
import { confirmDelete } from "@helpers/prompt";
import EditUserForm from "../Forms/EditUserForm";
import Filter from "@components/common/Filter";
import Select from "@components/form/Select";
import { UsersManagementStatus } from "@enums/users-management";
import { useDebouncedCallback } from "use-debounce";
import Button from "@components/common/Button";
import { useTicketActions } from "@state/reducer/departmental-ticketing";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { useRolesActions } from "@state/reducer/roles";

const UsersTable: FC = () => {
  const [create, setCreate] = useState<boolean>(false);
  const [edit, setEdit] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>("");
  const users = useSelector((state: RootState) => state.usersManagement.users);
  const loading = useSelector((state: RootState) => state.usersManagement.getUsers?.loading);
  const { getUsers, destroyUsers, setSelectedUser } = useUserManagementActions();
  const { clearRole } = useRolesActions();
  const [statusFilter, setStatusFilter] = useState<string | undefined>("");
  const [resetCounter, setResetCounter] = useState(0);
  const commonSetting = {
    sortable: true,
    reorder: true,
  };
  const statusOptions = Object.values(UsersManagementStatus).map((value) => ({
    value,
    label: value,
    text: value,
  }));
  const { getDepartments } = useTicketActions();
  const { getAreas } = useAreaActions();
  const [filterArea] = useState("");

  const actionEvents: IActions<IUser>[] = [
    {
      name: "Edit",
      event: (row: IUser, index: number) => {
        // Set data to edit
        const ids = pluck(row.roles ?? [], "id");
        const roles = ids.map(String); // type cast to array of strings
        const data = {
          id: row.id,
          firstname: row.firstname,
          middlename: row.middlename,
          lastname: row.lastname,
          email: row.email,
          contactNumber: row.contactNumber,
          roles: roles,
          status: row.status,
          positionId: row.position?.id?.toString(),
          departmentId: row.departmentId,
          areaId: row.areaId,
          companyId: row.companyId,
          gender: row.gender,
          address: row.address,
        };

        setSelectedUser({ data: data, index: index });
        handleToggleEditModal();
      },
      icon: CiEdit,
      color: "primary",
    },
    {
      name: "Delete",
      event: (row: IUser, index: number) => {
        const action = confirmDelete("User");
        action.then((value) => {
          if (value.isConfirmed) {
            destroyUsers({ id: row.id, index: index });
          }
        });
      },
      icon: CiTrash,
      color: "danger",
    },
  ];

  const columns: TableColumn<IUser>[] = [
    {
      name: "Name",
      selector: (row) => `${row.firstname} ${row?.middlename ?? ""} ${row.lastname}`,
      ...commonSetting,
    },
    {
      name: "Email",
      selector: (row) => row.email,
      ...commonSetting,
    },
    {
      name: "Roles",
      cell: (row) => {
        if (!row?.roles || row.roles.length < 1) {
          return <div>No Roles Assigned</div>;
        }
        if (row?.roles.length > 0) {
          const result = pluck(row.roles, "name");
          const str = joinArray(result, ",");
          return (
            <Tooltip text={str} position="top" className="flex flex-row items-center">
              <FaUser size={12} className="mr-2" />
              {result.length}
            </Tooltip>
          );
        }
      },
    },
    {
      name: "Status",
      cell: (row) => (row.status.toLowerCase() === "active" ? <Badge text={row.status} variant="success" className="text-white" outline /> : <Badge text={row.status} outline />),
    },
    {
      name: "Action",
      cell: (row, rowIndex) => <ActionButtons data={row} rowIndex={rowIndex} actions={actionEvents} />,
    },
  ];

  const handleToggleCreateModal = () => {
    setCreate((prev) => !prev);
    clearRole();
  };

  const handleToggleEditModal = () => {
    setEdit((prev) => !prev);
    clearRole();
  };

  const handleStatusChange = (event: ChangeEvent<HTMLSelectElement>) => {
    const value = event.target.value;
    setStatusFilter(value);
  };
  const handleSearch = useDebouncedCallback((event: ChangeEvent<HTMLInputElement>) => {
    // Extract the current input value from the event
    const value = event.target.value;
    setSearchText(value);
  }, 500);

  const handleClearAll = () => {
    setSearchText("");
    setStatusFilter("");
    setResetCounter((prev) => prev + 1);
  };
  const handlePaginate = (_: any) => {};

  useEffect(() => {
    getUsers({ filter: searchText, statusFilter: statusFilter });
    getDepartments();
    getAreas({ filter: filterArea });
  }, [searchText, statusFilter]);

  return (
    <Fragment>
      <div className="max-w-20 mt-4 mb-4">
        <Filter search={searchText} onChange={handleSearch} placeholder={"Email"}>
          <div className="flex justify-end">
            <button className="text-primary text-xs btn-sm" type="button" onClick={handleClearAll}>
              Clear All
            </button>
          </div>
          <div>
            <div className="text-xs mb-1">Status</div>
            <Select key={`status-${resetCounter}`} size="sm" placeholder="Status" options={statusOptions} value={statusFilter} onChange={handleStatusChange} />
          </div>
        </Filter>
      </div>
      <div className="flex justify-end">
        <Button classNames="flex flex-row btn-sm items-center" variant="primary" onClick={handleToggleCreateModal}>
          Create User <FaUserPlus className="pl-2" size={20} />
        </Button>
      </div>

      <Table className="h-128" columns={columns} data={users} onCreate={handleToggleCreateModal} loading={loading} onPaginate={handlePaginate} multiSelect={false} />
      {create && (
        <Modal title="Create New User" modalContainerClassName="!max-w-2xl" titleClass="text-primary text-lg uppercase" isOpen={create} onClose={handleToggleCreateModal}>
          <CreateUserForm handleModal={handleToggleCreateModal} />
        </Modal>
      )}
      {edit && (
        <Modal title="Edit User" modalContainerClassName="!max-w-3xl" titleClass="text-primary text-lg uppercase" isOpen={edit} onClose={handleToggleEditModal}>
          <EditUserForm handleModal={handleToggleEditModal} />
        </Modal>
      )}
    </Fragment>
  );
};

export default UsersTable;
