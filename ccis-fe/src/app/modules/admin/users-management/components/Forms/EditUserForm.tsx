import { FC, useEffect } from "react";
import { Form, FormikProvider, useFormik } from "formik";
import { EditUserSchema } from "@services/users-management/users-management.schema";
import TextField from "@components/form/TextField";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import { useUserManagementActions } from "@state/reducer/users-management";
import { TUserPayload } from "@state/types/users-management";
import LoadingButton from "@components/common/LoadingButton";
import Select from "@components/form/Select";
import { formatSelectOptions } from "@helpers/array";
import { useRolesActions } from "@state/reducer/roles";
import { usePositionsManagementActions } from "@state/reducer/utilities-positions";
import Typography from "@components/common/Typography";
import CustomTextField from "@components/common/CustomTextFieldWithSearch";
import { IDepartment } from "@interface/departmental-ticketing-interface";
import { IAreaPayload } from "@state/types/utilities-areas";
import { genderOptions } from "./CreateUserForm";
import { useSelectOptions } from "@hooks/useSelectOptions";
import FlatList from "flatlist-react/lib";
type TEditFormProps = {
  handleModal?: () => void;
};

const EditUserForm: FC<TEditFormProps> = ({ handleModal = () => {} }) => {
  const statusOptions = [
    { text: "ACTIVE", value: "active" },
    { text: "INACTIVE", value: "inactive" },
    { text: "LOCKED", value: "locked" },
  ];

  const roles = useSelector((state: RootState) => state.roles.roles) ?? [];
  const positions = useSelector((state: RootState) => state.utilitiesPositions.positions) ?? [];
  const positionOptions = formatSelectOptions(positions, "positionName");
  const loading = useSelector((state: RootState) => state.usersManagement.putUsers?.loading);
  const selectedUser = useSelector((state: RootState) => state.usersManagement.selectedUser.data);
  const { putUsers } = useUserManagementActions();
  const { getRoles, getRole, clearRole } = useRolesActions();
  const { getPosition } = usePositionsManagementActions();
  const departments = useSelector((state: RootState) => state.departmentalTicketing.getDepartments?.data) ?? [];
  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const { role: roleData } = useSelector((state: RootState) => state.roles);

  const formik = useFormik({
    initialValues: selectedUser,
    validationSchema: EditUserSchema,
    enableReinitialize: true,
    onSubmit: async (values: TUserPayload) => {
      putUsers(values);
      handleModal();
      clearRole();
    },
  });
  useEffect(() => {
    getRoles({ filter: "" });
    getPosition({ filter: "" });
  }, []);
  useEffect(() => {
    if (formik.values.roles?.length) {
      getRole({ id: Number(formik.values.roles?.[0]) });
    }
  }, [formik.values.roles]);
  const roleNameOption = useSelectOptions({
    data: roles,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: ["name", "description"],
    separator: "  ==> ",
  });
  const hasSelectedRole = () => {
    return Array.isArray(roleData?.permissions) && roleData.permissions.length > 0;
  };

  const handleWhenEmpty = () => <div className="flex flex-row justify-center text-center text-danger">{!hasSelectedRole() ? "No Permission Available" : ""}</div>;
  const renderChecklist = (item: any) => (
    <div key={`${item.id}-${item.name}`} className="bg-white border border-zinc-200 rounded shadow-sm p-3 mb-1 flex flex-col items-start text-left">
      <div className="font-semibold text-primary text-sm mb-1 truncate overflow-hidden whitespace-nowrap w-full" title={item.name}>
        {item.name}
      </div>
      <div className="text-xs text-zinc-600">{item.description}</div>
    </div>
  );
  const permissionList = () => {
    return <FlatList list={roleData?.permissions ?? []} renderItem={(item: any) => renderChecklist(item)} renderWhenEmpty={handleWhenEmpty} />;
  };
  return (
    <FormikProvider value={formik}>
      <Form className="flex flex-1 flex-col justify-center">
        <div className="grid grid-cols-3 gap-3">
          <div className="flex flex-1 items-center">
            <Typography size="sm">First name</Typography>
          </div>
          <div className="col-span-2">
            <TextField
              type="text"
              size="sm"
              name="firstname"
              placeholder="Enter first name"
              value={formik.values.firstname}
              error={formik.touched.firstname && !!formik.errors.firstname}
              errorText={formik.errors.firstname}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              required
            />
          </div>
          <div className="flex flex-1 items-center">
            <Typography size="sm">Middle name</Typography>
          </div>
          <div className="col-span-2">
            <TextField
              type="text"
              size="sm"
              name="middlename"
              placeholder="Enter middle name"
              value={formik.values.middlename}
              error={formik.touched.middlename && !!formik.errors.middlename}
              errorText={formik.errors.middlename}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div className="flex flex-1 items-center">
            <Typography size="sm">Last name</Typography>
          </div>
          <div className="col-span-2">
            <TextField
              type="text"
              size="sm"
              name="lastname"
              placeholder="Enter last name"
              value={formik.values.lastname}
              error={formik.touched.lastname && !!formik.errors.lastname}
              errorText={formik.errors.lastname}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              required
            />
          </div>
          <div className="flex flex-1 items-center">
            <Typography size="sm">Email</Typography>
          </div>
          <div className="col-span-2">
            <TextField
              type="email"
              name="email"
              placeholder="Enter email"
              value={formik.values.email}
              error={formik.touched.email && !!formik.errors.email}
              errorText={formik.errors.email}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              readOnly
              required
            />
          </div>
          <div className="flex flex-1 items-center">
            <Typography size="sm">Phone number</Typography>
          </div>
          <div className="col-span-2">
            <TextField
              type="phone"
              size="sm"
              name="contactNumber"
              placeholder="Enter phone number"
              value={formik.values.contactNumber}
              error={formik.touched.contactNumber && !!formik.errors.contactNumber}
              errorText={formik.errors.contactNumber}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              required
            />
          </div>
          <div className="flex flex-1 items-center">
            <Typography size="sm">Position</Typography>
          </div>
          <div className="col-span-2">
            <Select
              options={positionOptions}
              size="sm"
              name="positionId"
              value={formik.values.positionId}
              onChange={formik.handleChange}
              error={formik.touched.positionId && !!formik.errors.positionId}
              errorText={formik.errors.positionId}
            />
          </div>
          <div className="flex flex-1 items-center">Status</div>
          <div className="col-span-2">
            <Select name="status" size="sm" options={statusOptions} value={formik.values.status} onChange={formik.handleChange} onBlur={formik.handleBlur} required />
          </div>
          <div className="flex flex-1 items-center">
            <Typography size="sm">Department</Typography>
          </div>
          <div className="col-span-2">
            <CustomTextField
              suggestionOptions={departments}
              getOptionLabel={(item: IDepartment) => item?.departmentName}
              getOptionValue={(item: IDepartment) => item?.id?.toString()}
              size="sm"
              name="departmentId"
              placeholder="Select department"
              value={formik.values.departmentId}
              onChange={formik.handleChange}
              error={formik.touched.departmentId && !!formik.errors.departmentId}
              errorText={formik.errors.departmentId}
            />
          </div>
          <div className="flex flex-1 items-center">
            <Typography size="sm">Area</Typography>
          </div>
          <div className="col-span-2">
            <CustomTextField
              suggestionOptions={area}
              getOptionLabel={(item: IAreaPayload) => item?.areaName}
              getOptionValue={(item: IAreaPayload) => item?.id?.toString()}
              size="sm"
              name="areaId"
              placeholder="Select area"
              value={formik.values.areaId}
              onChange={formik.handleChange}
              error={formik.touched.areaId && !!formik.errors.areaId}
              errorText={formik.errors.areaId}
            />
          </div>
          <div className="flex flex-1 items-center">
            <Typography size="sm">Company ID</Typography>
          </div>
          <div className="col-span-2">
            <TextField
              type="text"
              size="sm"
              name="companyId"
              placeholder="Enter company ID"
              value={formik.values.companyId}
              error={formik.touched.companyId && !!formik.errors.companyId}
              errorText={formik.errors.companyId}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div className="flex flex-1 items-center">
            <Typography size="sm">Gender</Typography>
          </div>
          <div className="col-span-2">
            <Select name="gender" size="sm" options={genderOptions} value={formik.values.gender} onChange={formik.handleChange} onBlur={formik.handleBlur} required />
          </div>
          <div className="flex flex-1 items-center">
            <Typography size="sm">Address</Typography>
          </div>
          <div className="col-span-2">
            <TextField
              type="text"
              size="sm"
              name="address"
              placeholder="Enter address"
              value={formik.values.address}
              error={formik.touched.address && !!formik.errors.address}
              errorText={formik.errors.address}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>
          <div className="flex flex-1 items-center">
            <Typography size="sm">Assign Role</Typography>
          </div>
          <div className="col-span-2">
            <Select
              name="roles"
              size="sm"
              options={roleNameOption}
              value={formik.values.roles?.[0] ?? ""}
              onChange={(e) => {
                // Always store as an array of numbers
                const value = e.target.value;
                getRole({ id: Number(value) || formik.values.roles?.map((role) => Number(role))?.[0] });
                formik.setFieldValue("roles", value ? [Number(value)] : []);
              }}
              onBlur={formik.handleBlur}
              required
            />
          </div>
        </div>
        <div className={`max-h-96 overflow-y-auto gap-4 mt-10 ${hasSelectedRole() ? "grid grid-cols-3" : "flex flex-row justify-center"}`}>{permissionList()}</div>
        <div className="divider"></div>
        <div className="flex flex-1 justify-center">
          <div className="w-60">
            <LoadingButton type="submit" variant="primary" className="text-md" isLoading={loading}>
              Save
            </LoadingButton>
          </div>
        </div>
      </Form>
    </FormikProvider>
  );
};

export default EditUserForm;
