import { FC, ReactNode } from "react";
import Tabs from "@components/common/Tabs";
import UsersTable from "./components/Tables/UsersTable";
import Roles from "../roles";

const Users: FC = () => {
  const headers: string[] = ["Roles", "Users"];
  const contents: ReactNode[] = [<Roles />, <UsersTable />];

  return (
    <div>
      <div className="text-xl font-semibold uppercase mb-4 mt-4">User Management</div>
      <Tabs headers={headers} contents={contents} size="md" headerClass="w-52" fullWidthHeader={false} />
    </div>
  );
};

export default Users;
