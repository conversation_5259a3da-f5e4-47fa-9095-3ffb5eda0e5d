import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import { FC } from "react";
import Typography from "@components/common/Typography";
import LoadingButton from "@components/common/LoadingButton";
import { toast } from "react-toastify";
import { useProductBenefitsManagementActions } from "@state/reducer/utilities-product-benefits";
import { useCommissionTypeActions } from "@state/reducer/commision-type";
import { IUtilitiesProductBenefits } from "@interface/utilities.interface";
import ActuaryEvaluationReport from "../AERDetails/AER";
import { FaRedo } from "react-icons/fa";
import { ProductCategoryType, ProposableTypes, ProposalTypes, ProposalUserType } from "@enums/enums";
import { TProductProposalPayload } from "@state/types/product-proposal";
import { ProposalStatus } from "@enums/proposal-status";
import { ROUTES } from "@constants/routes";
import { useNavigate } from "react-router-dom";
import { postProductProposalService, putProductProposalService } from "@services/product-proposal/product-proposal.service";
import { IProductProposal } from "@interface/product-proposal.interface";
import { setProposedProduct } from "@state/reducer/product-proposal";
import CLPPGuidelines from "../AERGuidelines/CLPPGuidelines";
import CLSPGuidelines from "../AERGuidelines/CLSPGuidelines";
import { useQuotationActions } from "@state/reducer/quotations";
import GYRTGuidelines from "../AERGuidelines/GYRTGuidelines";
import FIPGuidelines from "../AERGuidelines/FIPGuidelines";

type TProps = {
  setStep: () => void;
  handleChange: () => void;
  submitting?: boolean;
  onAERSelected?: (aerData: any) => void;
  mode?: "create" | "edit";
  proposalId?: number;
};

const SelectedAERDetails: FC<TProps> = ({ handleChange, mode, proposalId }) => {
  const approvedAERDetails: any = useSelector((state: RootState) => state.quotation.quotations);

  const commissionTypesData = useSelector((state: RootState) => state?.commissionType?.commissionTypes);
  const productBenefits: IUtilitiesProductBenefits[] = useSelector((state: RootState) => state.utilitiesProductBenefits.productBenefits);
  const customType = useSelector((state: RootState) => state.productProposal.customType);
  const { getProductBenefits } = useProductBenefitsManagementActions();
  const { getCommissionTypes } = useCommissionTypeActions();
  const { getQuotations } = useQuotationActions();
  const user = useSelector((state: RootState) => state.auth.user);
  const [submitting, setSubmitting] = useState<boolean>(false);

  const navigate = useNavigate();
  useEffect(() => {
    getProductBenefits({ filter: "" });
    getCommissionTypes();
    getQuotations({ params: { statusFilter: ProposalStatus.approved } });
  }, []);

  const handleSave = async () => {
    try {
      setSubmitting(true);

      if (customType && !approvedAERDetails) {
        toast.error("No approved Actuary Evaluation Report selected");
        return;
      }

      const draftPayload: TProductProposalPayload = {
        cooperativeId: approvedAERDetails?.quotation?.cooperative?.id,
        productId: approvedAERDetails?.product?.id ?? approvedAERDetails?.productId,
        managementPercentFee: approvedAERDetails?.quotation?.quotationCommissionDistribution?.[0]?.rate ?? 0,
        proposableId: approvedAERDetails?.id,
        proposableType: ProposableTypes.AER,
        proposalType: customType ? ProposalTypes.CUSTOMIZED : ProposalTypes.STANDARD,
        productStatus: ProposalStatus.draft,
        status: ProposalStatus.draft,
      };

      let resp;
      if (mode === "edit" && proposalId) {
        // UPDATE existing draft
        resp = await putProductProposalService(draftPayload, proposalId);
      } else {
        // CREATE new draft
        resp = await postProductProposalService(draftPayload);
      }

      if (resp?.data) {
        toast.success(`Draft ${mode === "edit" ? "updated" : "saved"} successfully`);
        navigate(ROUTES.SALES.productProposal.key);
      }
    } catch (error: any) {
      toast.error(error?.response?.data?.message ?? "Failed to save draft");
    } finally {
      setSubmitting(false);
    }
  };

  const handleSubmit = async () => {
    try {
      setSubmitting(true);

      if (customType && !approvedAERDetails) {
        toast.error("No approved Actuary Evaluation Report selected");
        return;
      }

      const fullPayload: TProductProposalPayload = {
        cooperativeId: approvedAERDetails?.quotation?.cooperative?.id,
        productId: approvedAERDetails?.product?.id,
        managementPercentFee: 0,
        proposableId: approvedAERDetails?.id,
        proposableType: ProposableTypes.AER,
        proposalType: customType ? ProposalTypes.CUSTOMIZED : ProposalTypes.STANDARD,
        productStatus: ProposalStatus.active,
        status: ProposalStatus.active,
      };

      let data;

      if (mode === "edit" && proposalId) {
        const response = await putProductProposalService(fullPayload, proposalId);
        data = response.data;
      } else {
        const response = await postProductProposalService(fullPayload);
        data = response.data;
      }

      if (data) {
        setProposedProduct(undefined as unknown as IProductProposal);
        toast.success(`Product proposal has been ${mode === "edit" ? "updated" : "created"} successfully`);
        navigate(ROUTES.SALES.productProposal.key);
      }
    } catch (error: any) {
      toast.error(error?.response?.data?.message ?? `Failed to ${mode === "edit" ? "update" : "create"} proposal`);
    } finally {
      setSubmitting(false);
    }
  };

  const getApprovedProductRevision = () => {
    const revisions = approvedAERDetails?.product?.productRevisions ?? [];
    const approvedRevisions = revisions.filter((rev: any) => rev.approvalStatus === ProposalStatus.approved && rev.type === ProposalUserType.RND).sort((a: any, b: any) => b.id - a.id); // Sort by id in descending order
    return approvedRevisions[0]; // latest approved based on id
  };

  const approvedProductRevision = getApprovedProductRevision();
  const productGuidelines = approvedProductRevision?.productGuidelines ?? [];

  const renderGuidelines = () => {
    const productCode = approvedAERDetails?.product?.productCode;

    if (!productGuidelines?.length) {
      return <p className="text-center text-gray-500">No approved product guidelines found.</p>;
    }

    switch (productCode) {
      case ProductCategoryType.CLPP:
        return <CLPPGuidelines approvedAERDetails={approvedAERDetails} productGuidelines={productGuidelines} />;
      case ProductCategoryType.CLSP:
        return <CLSPGuidelines approvedAERDetails={approvedAERDetails} productGuidelines={productGuidelines} />;
      case ProductCategoryType.GYRT:
        return <GYRTGuidelines approvedAERDetails={approvedAERDetails} productGuidelines={productGuidelines} />;
      case ProductCategoryType.FIP:
        return <FIPGuidelines approvedAERDetails={approvedAERDetails} productGuidelines={productGuidelines} />;
      default:
        return <p className="text-center text-gray-500">No specific guideline component available for this product.</p>;
    }
  };

  return (
    <div className="bg-white">
      <div className=" max-h-[500px] overflow-y-auto mt-20">
        <div className="flex flex-1 flex-row justify-end mt-2">
          <button className="btn bg-ghost text-accent btn-sm px-6" onClick={handleChange}>
            <FaRedo />
            Change AER
          </button>
        </div>
        <div className="flex flex-1 flex-col p-10 border-[1px] border-zinc-400 mt-2">
          <Typography className="flex flex-col space-y-10 !text-black text-justify">
            <p className="font-poppins-semibold">July 3, 2025</p>
            <div>
              <p className="mt-0 font-poppins-semibold underline">The Board of Directors</p>
              <p className="max-w-60 mt-0 font-poppins-semibold underline">{approvedAERDetails?.quotation?.cooperative?.coopName ?? "Name of Cooperative"}</p>
              <p className="mt-0 font-poppins-semibold underline">
                {approvedAERDetails?.quotation?.cooperative?.streetAddress ?? "Address of Cooperative"},{approvedAERDetails?.quotation?.cooperative?.city ?? "City of Cooperative"},
                {approvedAERDetails?.quotation?.cooperative?.province ?? "Province of Cooperative"}
              </p>
            </div>

            <span>Dear Valued Cooperator,</span>
            <span>Warm Cooperative Greetings!</span>
            <span>
              At CLIMBS Life and General Insurance Cooperative, we are more than just an insurance provider—we are a movement built on the principles of mutual protection, solidarity, and
              sustainability. As a cooperative insurer, we remain committed to empowering our members by providing innovative and accessible insurance solutions that secure their future, communities,
              and the environment.
            </span>
            <span>
              Founded in 1971 by the late Atty. Mordino Cua and Atty, Aquilino Pimentel, Sr., CLIMBS was created to be an alternative to traditional insurance—one that truly understands the needs of
              cooperatives and their members. Over the years, we have grown into a trusted name in microinsurance, with a strong network of over 4,000+ primary cooperatives across the Philippines.
              Today, we continue to uphold our mission by embracing Climate Insurance under our banner: “Insuring Where You Are!”
            </span>
            <span>
              Recognizing the increasing risks posed by climate change, we have developed climate-responsive insurance products designed to protect cooperatives and their members from financial,
              environmental, and social vulnerabilities. Our variety of life and non-life insurance solutions ensure security at an affordable premium, with streamlined claims handling and digital
              innovations that make protection more accessible than ever.
            </span>
            <span>
              As fellow cooperators, we believe in strengthening partnerships to build
              <span className="font-poppins-semibold"> {approvedAERDetails?.product?.name ?? "N/A"} </span>
              Proposal outlines the key features and benefits of the said plan. Should you have any inquiries or require further clarification, please feel free to contact us at{" "}
              <span className="font-poppins-semibold">{user?.data?.contactNumber ?? "[Contact Number]"}</span> and{" "}
              <span className="font-poppins-semibold">{user?.data?.email ?? "[Email Address]"}</span> or email address{" "}
            </span>
            <span>
              We would be delighted to present our products and services at one of your upcoming board or management meetings and discuss potential collaborations to enhance the financial security and
              sustainability of your cooperative and members.
            </span>
            <span>Yours in cooperation,</span>
            <div className="font-poppins-semibold">
              <p>
                {user?.data?.firstname ?? "[First Name]"} {user?.data?.middlename ?? "[Middle Name]"} {user?.data?.lastname ?? "[Last Name]"}
              </p>
              <p>CLIMBS Sales Person</p>
              <p>CLIMBS Life and General Insurance Cooperative</p>
            </div>
          </Typography>
        </div>
        <div className="flex flex-1 flex-col p-2 space-y-4">
          <div>
            <p className="font-poppins-semibold !text-black underline text-lg text-center">{approvedAERDetails?.product?.name ?? "No Product Name Set"}</p>
            <p className="font-poppins-semibold !text-black text-center">{approvedAERDetails?.product?.productCode}</p>
          </div>

          <div className="flex flex-1 flex-col justify-start mt-10">
            <Typography className="font-poppins-semibold !text-black">Product Concept</Typography>
            <Typography className="ml-4 mt-4">{approvedAERDetails?.product?.description ?? "No Product Description Set"}</Typography>
          </div>
          {/* GUIDELINES  */}
          <div className="flex flex-1 flex-col justify-start mt-4">{renderGuidelines()}</div>

          {/* Claims turn-around Time */}
          <div className="mt-2">
            <span className="uppercase font-poppins-semibold text-primary">Claims turn-around time </span>
            <ul className="list-disc ml-8">
              <li>
                15 working days upon receipt of complete claims documents by CLIMBS Claims-In-Charge Should this proposal be acceptable, please sign on all pages and on the conforme space provided
                below.
              </li>
            </ul>
          </div>
          <div className="w-full h-full">
            <ActuaryEvaluationReport selectedAER={approvedAERDetails} productBenefits={productBenefits} commissionTypesData={commissionTypesData} />
          </div>
        </div>
      </div>
      <div className="mt-8">
        <div className="flex justify-end">
          <div className="flex gap-2">
            <LoadingButton type="button" isLoading={submitting} onClick={handleSave} className="btn rounded-lg hover:bg-slate-200 bg-white !text-primary !w-32 mr-4 mt-4">
              Save as draft
            </LoadingButton>

            <LoadingButton type="button" isLoading={submitting} onClick={handleSubmit} className="btn rounded-lg bg-accent text-white !w-32 mr-4 mt-4">
              {mode === "edit" ? "Update Proposal" : "Create Proposal"}
            </LoadingButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SelectedAERDetails;
