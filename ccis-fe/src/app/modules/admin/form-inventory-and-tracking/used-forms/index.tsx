import Tabs from "@components/common/Tabs";
import { ReactNode } from "react";
import ForReceivingTab from "./components/tabs/for-receiving-tab";
import ReturnPadTab from "./components/tabs/return-pads-tab";
import Inventory from "../inventory";

const UsedForms = () => {
  const headers: string[] = ["For Receiving", "Returned", "Inventory"];
  const contents: ReactNode[] = [<ForReceivingTab />, <ReturnPadTab />, <Inventory />];

  return (
    <div>
      <div className=" my-4 py-4 border-b border-zinc-300 w-full text-zinc-500">
        Dashboard / <span className="text-primary font-poppins-semibold ">Used Forms</span>
      </div>
      <Tabs headers={headers} contents={contents} size="md" headerClass="w-52" fullWidthHeader={false} />
    </div>
  );
};

export default UsedForms;
