import { IUserPermission } from "@interface/permissions.interface";
import <PERSON><PERSON><PERSON> from "flatlist-react";
import React, { useEffect, useState } from "react";
import CheckBox from "@components/form/CheckBox";
import { ICreateRoleForm } from "@interface/roles.interface";

const RolesCard: React.FC<ICreateRoleForm> = (props) => {
  const { create, permission, onSelectPermission, onSelectPermissions } = props;
  const { id, name, description, permissions = [] } = permission;

  const [localPermissions, setLocalPermissions] = useState<IUserPermission[]>(permissions);

  const [selectAll, setSelectAll] = useState<boolean>(false);

  useEffect(() => {
    setLocalPermissions(permissions);
    setSelectAll(permissions.every((p: IUserPermission) => p.checked));
  }, [permissions]);

  const handleCheckBoxChange = (event: React.ChangeEvent<HTMLInputElement>, item: IUserPermission) => {
    const updatedPermissions = localPermissions.map((permission) => (permission.id === item.id ? { ...permission, checked: event.target.checked } : permission));

    setLocalPermissions(updatedPermissions);
    onSelectPermission({
      ...item,
      checked: event.target.checked,
      parentId: id,
    });
    setSelectAll(updatedPermissions.every((p: IUserPermission) => p.checked));
  };

  const handleSelectAllCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const checked = event.target.checked;
    setSelectAll(checked);

    const updatedPermissions = localPermissions.map((permission) => ({
      ...permission,
      checked,
    }));

    setLocalPermissions(updatedPermissions);
    onSelectPermissions({ id, checked });
  };
  const renderChecklist = (item: IUserPermission) => {
    return (
      <label key={`${item.id}-${item.name}`} className="flex items-center w-1/2 mb-2 text-xs">
        <CheckBox
          name="permission"
          value={item.id}
          rightLabel={item.description}
          onChange={(event) => handleCheckBoxChange(event, item)}
          checked={item.checked ?? false}
          labelStyle="justify-start text-xs"
        />
      </label>
    );
  };

  const handleWhenEmpty = () => <div>No permissions fetched!</div>;

  const renderChecklists = () => {
    return <FlatList list={localPermissions} renderItem={(item: IUserPermission) => renderChecklist(item)} renderWhenEmpty={handleWhenEmpty} />;
  };

  return (
    <div className="rounded-md px-4 overflow-hidden mt-1 text-md mr-2 bg-gray-100 border border-1 border-zinc-200 p-1 ">
      <div className="flex mb-2 border-b-2 border-zinc-100">
        <div className="w-1/2 mt-4 h-1">{create ? name : description}</div>
        <div className="w-1/2 mt-4 h-1 mr-10 m-10 ">
          <label className="flex items-center w-1/2 mb-2">
            <CheckBox name="localPermissions" value={id} checked={selectAll} onChange={handleSelectAllCheckboxChange} />
            <span className="text-sm">Select All</span>
          </label>
        </div>
      </div>
      <div className="flex flex-wrap text-xs mt-2">{renderChecklists()}</div>
    </div>
  );
};

export default RolesCard;
