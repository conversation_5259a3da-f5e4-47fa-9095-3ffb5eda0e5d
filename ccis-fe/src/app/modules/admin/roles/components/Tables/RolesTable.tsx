import { FC, Fragment, useEffect, useState, useRef } from "react";
import { useSelector } from "react-redux";
import { TableColumn } from "react-data-table-component";
import { RootState } from "@state/store";
import { CiEdit, CiTrash } from "react-icons/ci";
import { FaUser } from "react-icons/fa";
import { IActions } from "@interface/common.interface";
import Table from "@components/common/Table";
import Modal from "@components/common/Modal";
import ActionButtons from "@components/common/ActionButtons";
import { useRolesActions } from "@state/reducer/roles";
import { usePermissionsActions } from "@state/reducer/permissions";
import { IRole } from "@interface/roles.interface";
import Tooltip from "@components/common/Tooltip";
import CreateRoleForm from "../Forms/CreateRoleForm";
import Button from "@components/common/Button";
import { IPermissions, IUserPermission } from "@interface/permissions.interface";
import { IoChevronBack } from "react-icons/io5";
import { FormikProps } from "formik";
import { toast } from "react-toastify";

const RolesTable: FC = () => {
  const { roles: rolesData, role: roleData } = useSelector((state: RootState) => state.roles);
  const { permission: permission, permissions: permissions } = useSelector((state: RootState) => state.permissions);
  const { loading: loadingGetRoles, success: successGetRole } = useSelector((state: RootState) => state.roles.getRoles);
  const loadingGetPermissions = useSelector((state: RootState) => state.permissions.getPermissions.loading);
  const { loading: loadingPutRoles, success: successPutRoles } = useSelector((state: RootState) => state.roles.putRole);
  const { loading: loadingPostRoles, success: successPostRoles } = useSelector((state: RootState) => state.roles.postRole);
  const { loading: loadingDestroyRoles, success: successDestroyRoles } = useSelector((state: RootState) => state.roles.destroyRole);
  const loading = loadingGetRoles || loadingGetPermissions || loadingPutRoles || loadingPostRoles;
  const [create, setCreate] = useState<boolean>(false);
  const [createSubmit, setCreateSubmit] = useState<boolean>(false);
  const [edit, setEdit] = useState<boolean>(false);
  const [editRoleMsg, setEditRoleMsg] = useState<boolean>(false);
  const [remove, setRemove] = useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>("");
  const [stateRole, setStateRole] = useState<IRole>({} as IRole);
  const [message, setMessage] = useState<any>({});
  const [initialValues, setInitialValues] = useState<IRole>({} as IRole);
  const [formData, setFormData] = useState<IRole | null>(null);
  const formikRef = useRef<FormikProps<IRole>>(null);

  const handleSubmitData = async () => {
    if (!formikRef.current) return;

    const errors = await formikRef.current.validateForm();

    if (Object.keys(errors).length === 0) {
      formikRef.current.submitForm();
    } else {
      formikRef.current.submitForm();
      setCreateSubmit(false);
      setEditRoleMsg(false);
    }
  };

  const [formErrors, setFormErrors] = useState<any>({});
  const { getRoles, getRole, destroyRoles, destroyRolesReset, postRoles, putRoles } = useRolesActions();
  const { getPermissions } = usePermissionsActions();
  const showForm = (create || edit) && !loading && (edit ? Object.keys(stateRole)?.length > 0 : true);

  const handleUpdateRoleFields = (data: IRole) => {
    setFormData(data);
  };

  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const actionEvents: IActions<IRole>[] = [
    {
      name: "Edit",
      event: (row: IRole) => {
        (onEdit(row), setInitialValues(row));
      },

      icon: CiEdit,
      color: "primary",
    },
    {
      name: "Delete",
      event: (row: IRole) => onDelete(row),
      icon: CiTrash,
      color: "danger",
    },
  ];
  const columns: TableColumn<IRole>[] = [
    {
      name: "Role",
      selector: (row) => row.name ?? "",
      ...commonSetting,
    },
    {
      name: "Description",
      cell: (row) => row.description,
    },
    {
      name: "# of Users",
      cell: (row) => {
        const result = row?.usersCount;
        if (!result || result < 0) {
          return <div>No User Assigned</div>;
        }
        if (result > 0) {
          return (
            <Tooltip text={result} position="top" className="flex flex-row items-center">
              <FaUser size={12} className="mr-2" />
              {result} User/s
            </Tooltip>
          );
        }
      },
    },
    {
      name: "Action",
      cell: (row, rowIndex) => <ActionButtons data={row} rowIndex={rowIndex} actions={actionEvents} />,
    },
  ];

  const loadingRoles = loadingPostRoles || loadingPutRoles || loadingDestroyRoles;
  const handleCreateEditDelete = (create: boolean, edit: boolean, remove: boolean) => {
    create
      ? (setCreate(true), setEdit(false), setCreateSubmit(false), setRemove(false))
      : edit
        ? (setEdit(true), setCreate(false), setEditRoleMsg(false), setRemove(false))
        : remove
          ? (setRemove(false), setCreate(false), setEditRoleMsg(false), setEdit(false))
          : null;
  };

  const loadingMessage = () => {
    if (loadingRoles) {
      if (createSubmit) return "Progressing...";
      if (editRoleMsg) return "Progressing......";
      return "Progressing......";
    }
    if (createSubmit) return "Create";
    if (editRoleMsg) return "Update";
    return "Delete";
  };
  const messageToDisplay = loadingMessage;
  const handleSearch = (value: string) => {
    setSearchText(value);
  };
  // const handlePaginate = (_: any) => {}; (for future features)

  const onClear = () => {
    setCreate(false);
    setEditRoleMsg(false);
    setCreateSubmit(false);
    setEdit(false);
    setRemove(false);
    setMessage({});
    destroyRolesReset();
    setStateRole({} as IRole);
  };
  const toggleModalEditSuccess = () => setEditRoleMsg(true);
  const onCreate = () => {
    setCreate((prev) => !prev);
    setInitialValues({
      name: "",
      description: "",
    });
  };
  const onCreateSubmit = async () => {
    if (!formikRef.current) return;

    const errors = await formikRef.current.validateForm();

    if (Object.keys(errors).length === 0) {
      setCreateSubmit(true);
    } else {
      formikRef.current.submitForm();
      setCreateSubmit(false);
    }
  };

  const onCreatePost = () => {
    if (Object.keys(formErrors).length === 0) {
      const { permissions } = stateRole;

      const permissionArray = (): number[] => {
        const permissionAssignMarkChecked: number[] = [];
        permissions?.forEach((permissionGroup: any) => {
          permissionGroup.permissions?.forEach((permissionsCard: any) => {
            if (permissionsCard.checked) {
              permissionAssignMarkChecked.push(permissionsCard.id);
            }
          });
        });

        return permissionAssignMarkChecked;
      };

      const payload: IRole = {
        name: formData?.name,
        guardName: "api",
        description: formData?.description,
        permissionIds: permissionArray(),
      };

      postRoles(payload);
    } else {
      console.warn("Form has errors, cannot submit");
    }
  };

  const onEdit = (roleData: IRole) => {
    setEdit(true);
    getRole({ id: roleData.id });
  };
  const onEditSubmit = () => {
    const { id, permissions } = stateRole;
    const permissionArray = (): number[] => {
      const permissionAssignMarkChecked: number[] = [];
      permissions?.forEach((permissionGroup: any) => {
        permissionGroup.permissions.forEach((permissionsCard: any) => {
          if (permissionsCard.checked) permissionAssignMarkChecked.push(permissionsCard.id);
        });
      });
      return permissionAssignMarkChecked;
    };
    const payload: IRole = {
      id,
      name: formData?.name,
      guardName: "api",
      description: formData?.description,
      permissionIds: permissionArray(),
    };
    putRoles(payload);
  };
  const onDelete = (roleData: IRole) => {
    setStateRole(roleData);
    setRemove(true);
  };
  const onDeleteSubmit = () => {
    setRemove(false);
    destroyRoles({ id: stateRole?.id });
  };
  const onUpdateRole = (key: string, value: string) => {
    setStateRole({
      ...stateRole,
      [key]: value,
    });
  };
  const onSelectPermission = (item: any) => {
    if (!stateRole.permissions) {
      return;
    }
    const parentIndex = stateRole.permissions.findIndex((permission: IPermissions) => item.parentId === permission.id);
    if (parentIndex === -1) {
      return;
    }
    if (!stateRole.permissions[parentIndex]?.permissions) {
      return;
    }
    const childIndex = stateRole.permissions[parentIndex].permissions.findIndex((permission: IUserPermission) => item.id === permission.id);
    if (childIndex === -1) {
      return;
    }
    delete item.parentId;
    const stateRoleCopy = JSON.parse(JSON.stringify(stateRole));
    stateRoleCopy.permissions[parentIndex].permissions[childIndex] = item;
    setStateRole(stateRoleCopy);
  };
  const onSelectPermissions = (item: any) => {
    const parentIndex = stateRole.permissions?.findIndex((permission: IPermissions) => item?.id === permission.id);
    if (parentIndex !== undefined && parentIndex >= 0) {
      const stateRoleCopy = JSON.parse(JSON.stringify(stateRole));
      stateRoleCopy.permissions[parentIndex].permissions.forEach((permissionsList: IUserPermission) => {
        permissionsList.checked = item?.checked;
      });

      setStateRole(stateRoleCopy);
    } else {
      toast.error("Parent index not found or invalid.");
    }
  };

  useEffect(() => {
    getRoles({ filter: searchText });
  }, [searchText]);

  useEffect(() => {
    if (create === true) getPermissions();
  }, [create]);
  useEffect(() => {
    if (permission?.id) {
      const { id } = permission;
      setStateRole((prevState) => ({
        ...prevState,
        id,
        permissionIds: [],
      }));
    }
  }, [permission, setStateRole]);

  useEffect(() => {
    if (permissions?.length ?? 0 > 0) {
      const permissionsCopy = JSON.parse(JSON.stringify(permissions));
      if (edit === true && create === false) {
        const permissionAssigned = () => {
          const permissionAssignMarkChecked: number[] = [];
          stateRole.permissions?.forEach((permissionsList?: any) => {
            permissionAssignMarkChecked.push(permissionsList.id);
          });
          return permissionAssignMarkChecked;
        };
        permissionsCopy.forEach((permissionGroup?: any) => {
          permissionGroup.permissions.forEach((permissionsCard?: any) => {
            if (permissionAssigned().find((permissionsList) => permissionsList === permissionsCard.id)) permissionsCard.checked = true;
          });
        });
      }
      setStateRole({
        ...stateRole,
        permissions: permissionsCopy,
      });
    }
  }, [permissions]);
  useEffect(() => {
    if (successGetRole === true && roleData) {
      setStateRole(roleData);
      getPermissions();
    }
  }, [successGetRole]);
  useEffect(() => {
    if (successPostRoles === true) onClear();
  }, [successPostRoles]);

  useEffect(() => {
    if (successPutRoles === true) onClear();
  }, [successPutRoles]);

  useEffect(() => {
    if (successDestroyRoles === true) {
      setMessage({
        title: "Role Deletion Success",
        message: `Role "${stateRole?.name}" has been successfully deleted!`,
      });
    }
  }, [successDestroyRoles]);

  const handleFormDataChange = (data: IRole) => {
    setFormData(data);
  };

  const handleFormErrorsChange = (errors: any) => {
    setFormErrors(errors);
  };
  return (
    <Fragment>
      {/* table for roles */}
      {!showForm && (
        <Table
          className="h-128"
          columns={columns}
          data={rolesData}
          createLabel="Create Roles"
          onCreate={onCreate}
          loading={loading}
          onSearch={handleSearch}
          // onPaginate={handlePaginate} (for future uses)
          multiSelect={false}
          searchable
        />
      )}

      {/* when create/edit role button is toggled on */}
      {showForm && (
        <div className="flex gap-4">
          <Button onClick={onClear} classNames="border-none btn font-poppins-semibold">
            <IoChevronBack className="text-2xl text-slate-800" />
          </Button>
          <div className="flex justify-between mt-3">
            <div className="mt-1 text-xl font-bold ">
              <h1>{create ? "Add New" : "Update"} Role</h1>
            </div>
          </div>
        </div>
      )}

      {showForm && (
        <CreateRoleForm
          create={create}
          edit={edit}
          role={stateRole}
          onUpdateRole={onUpdateRole}
          onUpdateRoleFields={handleUpdateRoleFields}
          onSelectPermission={onSelectPermission}
          onSelectPermissions={onSelectPermissions}
          initialValues={initialValues}
          onSubmit={createSubmit ? onCreatePost : editRoleMsg ? onEditSubmit : onDeleteSubmit}
          setFormikInstance={(formik) => {
            (formikRef.current as any) = formik;
          }}
          onFormDataChange={handleFormDataChange}
          onFormErrorsChange={handleFormErrorsChange}
        />
      )}
      {showForm && (
        <div className=" mt-6 flex gap-2 flex-row justify-end">
          <button onClick={onClear} className="border-none text-slate-700 btn w-32 rounded-sm">
            Cancel
          </button>
          <Button variant="primary" onClick={create ? onCreateSubmit : edit ? toggleModalEditSuccess : onClear}>
            {create ? "Create" : "Update "} Role
          </Button>
        </div>
      )}

      {/* when delete, create, or edit role button is toggled on */}
      {(remove || createSubmit || editRoleMsg) && (
        <Modal
          title={`${createSubmit ? "Create " : editRoleMsg ? "Edit" : "Delete"} Role`}
          modalContainerClassName="max-w-3xl max-h-28 "
          className="absolute top-1/4"
          titleClass="text-primary text-lg uppercase"
          isOpen={remove || createSubmit || editRoleMsg}
          onClose={() => {
            handleCreateEditDelete(create, edit, remove);
          }}
        >
          <div>
            Do you wish to
            {createSubmit ? " create " : editRoleMsg ? " edit " : " remove "}
            role "{formData?.name || stateRole.name}"?
          </div>

          <div className="flex flex-row justify-end space-x-2">
            <div>
              <Button
                variant="default"
                classNames=" bg-zinc-400"
                onClick={() => {
                  handleCreateEditDelete(create, edit, remove);
                }}
                disabled={loading}
              >
                Cancel
              </Button>
            </div>
            <div>
              <Button type="submit" variant={createSubmit ? "primary" : editRoleMsg ? "primary" : "danger"} onClick={remove ? onDeleteSubmit : handleSubmitData} disabled={loadingRoles}>
                {messageToDisplay()}
              </Button>
            </div>
          </div>
        </Modal>
      )}
      {/* diplay message modal */}
      {successDestroyRoles && (
        <Modal title={message.title} modalContainerClassName="max-w-3xl" titleClass="text-primary text-lg uppercase" isOpen={successDestroyRoles} onClose={onClear}>
          <div>{message.message}</div>
          <div className="flex flex-row justify-end space-x-2">
            <Button variant="primary" onClick={onClear}>
              OK
            </Button>
          </div>
        </Modal>
      )}
    </Fragment>
  );
};

export default RolesTable;
