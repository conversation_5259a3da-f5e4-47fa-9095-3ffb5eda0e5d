import React, { useState, ChangeEvent, useEffect } from "react";
import TextField from "@components/form/TextField";
import RolesCard from "../RolesCard";
import { FaSearch } from "react-icons/fa";
import { MdOutlineStorage } from "react-icons/md";
import { IPermissions } from "@interface/permissions.interface";
import { ICreateRoleForm } from "@interface/roles.interface";
import Button from "@components/common/Button";
import { useDebouncedCallback } from "use-debounce";
import Modal from "@components/common/Modal";
import CheckBox from "@components/form/CheckBox";
import { CreateRoleSchema, EditRoleSchema } from "@services/roles/roles.schema.ts";
import { Form, FormikProvider, useFormik } from "formik";
const CreateRoleForm: React.FC<ICreateRoleForm> = (props) => {
  const { create, edit, role, onSearchChange, onSubmit, initialValues, onUpdateRoleFields, setFormikInstance, onFormD<PERSON><PERSON>hange, onFormErrorsChange } = props;
  const [searchText, setSearchText] = useState("");
  const [displayedPermissions, setDisplayedPermissions] = useState<IPermissions[]>([]);
  const [filterByModal, setFilterByModal] = useState<boolean>(false);
  const [modalPermissions, setModalPermissions] = useState<IPermissions[]>([]);
  const [checkedPermissions, setCheckedPermissions] = useState<Set<string>>(new Set());
  const toggleModalFilter = () => {
    setFilterByModal((prev) => !prev);
  };
  useEffect(() => {
    if (filterByModal && role?.permissions) {
      setModalPermissions(role.permissions);
    }
  }, [filterByModal, role?.permissions]);

  const handleCheckBoxChange = (event: ChangeEvent<HTMLInputElement>, permissionId: string) => {
    setCheckedPermissions((prevCheckedPermissions) => {
      const updatedCheckedPermissions = new Set(prevCheckedPermissions);
      if (event.target.checked) {
        updatedCheckedPermissions.add(permissionId);
      } else {
        updatedCheckedPermissions.delete(permissionId);
      }
      return updatedCheckedPermissions;
    });
  };

  const displayGroupPermissionModal = (permissions: IPermissions[]) => {
    return (
      <div className="grid grid-cols-2 gap-4 w-full">
        {permissions.map((permission) => {
          const permissionId = permission.id?.toString() || "";

          return (
            <CheckBox
              name="permission"
              key={permissionId}
              value={permissionId}
              rightLabel={permission.name || ""}
              onChange={(event) => handleCheckBoxChange(event, permissionId)}
              checked={checkedPermissions.has(permissionId)}
            />
          );
        })}
      </div>
    );
  };

  const handleInputChangeSearch = useDebouncedCallback((event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchText(value);

    if (onSearchChange) {
      onSearchChange(value);
    }

    const newMatches = role?.permissions?.filter((permission) => permission.name.toLowerCase().includes(value.toLowerCase())) || [];

    setDisplayedPermissions((prevPermissions) => {
      const newPermissions = newMatches.filter((newPermission) => !prevPermissions.some((displayedPermission) => displayedPermission.id === newPermission.id));
      return [...prevPermissions, ...newPermissions];
    });
  }, 1000);

  useEffect(() => {
    if (filterByModal === true) {
      const selectedPermissions = role?.permissions?.filter((permission) => checkedPermissions.has(permission.id?.toString() || ""));
      setDisplayedPermissions(selectedPermissions || []);
    } else {
      setDisplayedPermissions(role?.permissions || []);
    }
  }, [checkedPermissions, role?.permissions]);

  const renderPermissionCard = (permission: IPermissions) => {
    return (
      <div key={`${permission?.id}-${permission.name}`} className="w-full mt-2">
        <RolesCard permission={permission} {...props} />
      </div>
    );
  };

  const renderPermissionCards = () => {
    return <div className="grid grid-cols-2 gap-4">{displayedPermissions.map((permission: IPermissions) => renderPermissionCard(permission))}</div>;
  };
  const formik = useFormik({
    initialValues,
    enableReinitialize: true,
    validationSchema: edit ? EditRoleSchema : CreateRoleSchema,
    onSubmit: async (values, { resetForm }) => {
      await onSubmit(values);
      resetForm();
    },
  });
  React.useEffect(() => {
    if (setFormikInstance) {
      setFormikInstance(formik);
    }
  }, [setFormikInstance, formik]);

  useEffect(() => {
    if (onUpdateRoleFields) {
      onUpdateRoleFields(formik.values);
    }
  }, [formik.values, onUpdateRoleFields]);
  useEffect(() => {
    onFormDataChange(formik.values); // Pass form data to parent whenever it changes
  }, [formik.values]);
  useEffect(() => {
    onFormDataChange(formik.values); // Pass form data to parent whenever it changes
    onFormErrorsChange(formik.errors); // Pass form errors to parent whenever they change
  }, [formik.values, formik.errors]);

  return (
    <>
      <FormikProvider value={formik}>
        <Form className="min-w-full my-4">
          <div className="flex flex-wrap w flex-col mr-10 ml-10">
            <div className="flex justify-start mb-4">
              <div className="w-1/2 mt-4 h-12 mr-10 ">
                {create ? "New " : ""}Role {edit ? "Name" : ""}
                <TextField
                  name="name"
                  type="text"
                  className="w-60 h-18 mt-3 "
                  placeholder="Enter Role Name"
                  value={formik.values.name || ""}
                  error={formik.touched.name && !!formik.errors.name}
                  errorText={formik.errors.name}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  required
                />
              </div>
              <div className="w-1/2 mt-4 h-12 mr-10">
                Role Description
                <TextField
                  name="description"
                  type="text"
                  className="w-60 h-18 mt-3 "
                  placeholder="Enter Role Description"
                  value={formik.values.description || ""}
                  error={formik.touched.description && !!formik.errors.description}
                  errorText={formik.errors.description}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  required
                />
              </div>
            </div>
            <div className="flex justify-between mt-5">
              <div className="mt-10 text-SM font-bold  ">
                <h1>Assign Permissions</h1>
              </div>
            </div>

            <div className="flex justify-start mb-4">
              <div className="w-1/2 mt-4 h-12 mr-10 ">
                <TextField
                  rightIcon={<FaSearch className="text-accent" />}
                  placeholder={create ? "Search Permission" : "Press Space and del to display All Permissions"}
                  className="w-50 h-18 mt-3 "
                  variant="primary"
                  onChange={handleInputChangeSearch}
                  value={searchText}
                />
              </div>
              <div className="w-1/2 mt-4 h-12 mr-10">
                <Button variant="default" classNames="flex items-center justify-left mt-3 text-neutral-950" onClick={toggleModalFilter} outline={true}>
                  <MdOutlineStorage className="text-neutral-950 mr-2 size-7" />
                  FILTER
                </Button>
              </div>
            </div>

            <div className="flex justify-start mb-2 mt-1">{renderPermissionCards()}</div>
            {filterByModal && (
              <Modal title="GROUP PERMISSIONS FILTER" modalContainerClassName="max-w-3xl" titleClass="text-primary text-lg uppercase" isOpen={filterByModal} onClose={toggleModalFilter}>
                <>
                  <div className="flex justify-start mb-2 mt-1">{displayGroupPermissionModal(modalPermissions)}</div>
                </>
              </Modal>
            )}
          </div>
        </Form>
      </FormikProvider>
    </>
  );
};

export default CreateRoleForm;
