import Button from "@components/common/Button";
import { IContestability } from "@interface/contestability.interface";
import { RootState } from "@state/reducer";
import { useContestabilityActions } from "@state/reducer/contestability";
import { useEffect, useMemo } from "react";
import { useSelector } from "react-redux";
import { TOptions } from "@modules/sales/components/select";
import { condition } from "../template/condition";
import { ProductCode } from "@enums/product-code";
import { AGE_TYPE_ALL } from "./AgeInfo";

type TGyrtActionButtonProps = {
  calculated: boolean;
  selectedOptions: number[];
  membersCount: number;
  allIn: boolean;
  minimumAge: number;
  maximumAge: number;
  maxExitAge: number;
  contestabilityId: number;
  onSaveOption?: () => void;
  onCalculate?: () => void;
  emitConditions?: (conditions: string) => void;
  ageType?: string;
  hasDemographics?: boolean;
};

export const GyrtActionButton = ({
  calculated,
  selectedOptions,
  membersCount,
  allIn,
  minimumAge,
  maximumAge,
  maxExitAge,
  contestabilityId,
  onSaveOption,
  // onCalculate,
  emitConditions,
  ageType,
  // hasDemographics,
}: TGyrtActionButtonProps) => {
  const { getContestability } = useContestabilityActions();

  const contestabilityData = useSelector((state: RootState) => state.contestability.contestabilities);

  const contestabilitySelectItems = useMemo<TOptions[]>(() => {
    if (!contestabilityData) return [];
    return contestabilityData.map((item: IContestability) => ({
      text: item.label,
      value: String(item.id),
    }));
  }, [contestabilityData]);

  const contestabilityText = useMemo<string>(() => {
    return contestabilitySelectItems.find((c) => parseInt(c.value) == contestabilityId)?.text ?? "not found";
  }, [contestabilityId, contestabilitySelectItems]);

  const conditions = useMemo<string>(() => {
    return condition(selectedOptions.length, membersCount, allIn, minimumAge, maximumAge, maxExitAge, contestabilityText);
  }, [selectedOptions, membersCount, allIn, minimumAge, maximumAge, maxExitAge, contestabilityText]);

  const handleSaveOption = () => {
    onSaveOption?.();
  };

  // const handleCalculate = () => {
  //   onCalculate?.();
  // };

  useEffect(() => {
    getContestability({ filter: ProductCode.GYRT });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    emitConditions?.(conditions);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [conditions]);

  return (
    <div className="mb-3">
      {/* TO BE USED LATER */}
      {/* <h2 className="text-[20px] font-poppins-medium font-[600] text-primary">
        CONDITIONS
      </h2> */}
      {calculated && (
        <div className="mt-4">
          <pre className="text-wrap"></pre>
        </div>
      )}
      <div className="flex justify-end gap-4 mt-8">
        <Button
          classNames={`${!calculated ? "!bg-[#99999926]" : "!bg-info"} rounded-[5px] !px-12`}
          variant={!calculated ? "default" : undefined}
          onClick={handleSaveOption}
          disabled={!calculated && ageType === AGE_TYPE_ALL}
        >
          <div className="flex flex-row items-center gap-2">
            <span className={`${calculated ? "text-white" : "text-gray/50"} text-[14px] font-[400] font-poppins-medium`}>Save as Option</span>
          </div>
        </Button>
        {/*  */}
        {/* <Button
          classNames={`elevation-sm shadow-md rounded-[5px] !px-12 ${!hasDemographics && ageType === AGE_TYPE_ALL ? "!bg-opacity-50 cursor-not-allowed" : "!bg-success"}`}
          variant="success"
          onClick={handleCalculate}
          isSubmitting={!hasDemographics && ageType === AGE_TYPE_ALL}
        >
          <div className="flex flex-row items-center gap-2">
            <span className="text-[14px] font-[400] font-poppins-medium">Calculate</span>
          </div>
        </Button> */}
      </div>
    </div>
  );
};
