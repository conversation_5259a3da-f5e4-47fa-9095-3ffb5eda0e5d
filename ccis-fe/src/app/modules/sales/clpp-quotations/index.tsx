/* eslint-disable react-hooks/exhaustive-deps */
import _ from "lodash";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { toast } from "react-toastify";
import Button from "@components/common/Button";
import { ROUTES } from "@constants/routes";
import { RootState } from "@state/reducer";
import { useAppDispatch } from "@state/store";
import { useQuotationActions } from "@state/reducer/quotations";
import { TCreateClppQuotationWithAerPayload } from "@state/types/quotation";
import colorMode from "../utility/color";
import { parseNumber } from "../utility/number";
import { useModalController } from "../controller";
import UploadMasterListModal from "../modals/UploadMasterListModal";
import DemographicModal from "../modals/DemographicModal";
import { AlertError } from "../components/alert-error";
import { COMMISION_AGE_TYPES } from "./components/constants";
import { ClppBaseInfo, TClppBaseData } from "./components/BaseInfo";
import { ClppMemberAndAverageInfo, TClppMemberAndAverageInfo } from "./components/MemberAndAverageInfo";
import { ClppLoanPortfolio, TClppLoanPortfolio } from "./components/LoanPortfolio";
import { ClppClaimsExperienceInfo, TClppClaimsExperienceInfo } from "./components/ClaimsExperience";
import { ClppCommissionDistributionInfo, TClppCommissionDistribution } from "./components/CommisionDistribution";
import { ClppBenefitInfo, TClppBenefitInfo } from "./components/BenefitInfo";
import { ClppOptionsInfo, TClppOptions } from "./components/OptionsInfo";
import { PiCloudArrowUp } from "react-icons/pi";
import { getProductNameService } from "@services/products/products.service";
import { postDemographicDataService } from "@services/quotation/quotation.service";
import { useProductActions } from "@state/reducer/products";
import { useProductBenefitsManagementActions } from "@state/reducer/utilities-product-benefits";
import { useContestabilityActions } from "@state/reducer/contestability";
import { ProductCode } from "@enums/product-code";
import { confirmSaveOrEdit, showError } from "@helpers/prompt";
import { Statuses } from "@constants/global-constant-value";
import { putClppAER } from "@state/reducer/actuary-clpp-aer";
import { ProductStatus } from "@enums/product-status";

type TClppFormState = {
  baseInfo: TClppBaseData;
  memberAndAverageInfo: TClppMemberAndAverageInfo;
  loanPortfolio: TClppLoanPortfolio;
  benefits: TClppBenefitInfo[];
  status: String;
  claimsExperience: TClppClaimsExperienceInfo;
  commissionDistribution: TClppCommissionDistribution[];
};

const initialState = (): TClppFormState => ({
  baseInfo: {
    cooperativeId: 0,
    previousProvider: "",
    typeOfCoverage: "",
    contestabilityId: 0,
    productId: 0,
  },
  memberAndAverageInfo: {
    totalNumberOfMembers: 0,
  },
  loanPortfolio: {
    loanPortfolioYears: [],
    loanPortfolioAges: [],
  },
  claimsExperience: {
    years: [],
    ages: [],
    isAgeVisible: false,
  },
  status: Statuses.DRAFT,
  commissionDistribution: [],
  benefits: [
    {
      ageFrom: 18,
      ageTo: 0,
      maximumCoverage: 0,
      maximumTerm: 0,
      nml: 0,
      nel: 0,
      rate: 0,
    },
  ],
});

export default function CLPPQuotation() {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const { createClppQuotationWithAer, clearClppQuotation } = useQuotationActions();
  const { setSelectedProduct, clearSelectedProduct } = useProductActions();
  const { getContestability } = useContestabilityActions();
  const { getProductBenefits } = useProductBenefitsManagementActions();
  const productCode = localStorage.getItem("productCode");
  const [formState, setFormState] = useState<TClppFormState>(() => initialState());
  const [conditions, setConditions] = useState<string>("");
  const [savedOptions, setSavedOptions] = useState<TClppOptions[]>([]);
  const putClppAer = useSelector((state: RootState) => state.clppAER.putClppAER);
  const [selectedOptions, setSelectedOptions] = useState<number[]>([1]);
  const [alertError, setAlertError] = useState<string | undefined>(undefined);
  const uploadMasterListModalController = useModalController();
  const demographicModalController = useModalController();
  const [hasDemographics, setHasDemographics] = useState<boolean>(false);
  const [fileName, setFileName] = useState<string>("");
  const [demographicData, setDemographicData] = useState<any>(null);
  // const [hasRider, setHasRider] = useState<boolean>(false);
  const location = useLocation();
  const quotationData = location.state?.quotationData;
  const productBenefitsData = useSelector((state: RootState) => state.utilitiesProductBenefits.productBenefits);
  const didRequestEditRef = useRef(false);

  const aerData = useSelector((state: RootState) => state.quotation.createClppQuotationWithAer);
  const productData = useSelector((state: RootState) => state.products?.product);

  // Reset fields
  const reset = () => {
    dispatch(clearClppQuotation());
    setFormState(initialState());
    setSavedOptions([]);
    setSelectedOptions([]);
    setConditions("");
    setAlertError(undefined);
  };

  useEffect(() => {
    getProductBenefits({ filter: "" });
    getContestability({ filter: ProductCode.CLPP });
  }, []);

  useEffect(() => {
    if (!aerData?.data) return;
    navigate(ROUTES.SALES.quotationClppAerView.parse(aerData?.data?.quotationId ?? aerData?.data.id), { state: { demographicData } });
    toast.success("Quotation created successfully!");
    reset();
  }, [aerData]);

  useEffect(() => {
    if (!didRequestEditRef.current || !putClppAer?.data) return;
    didRequestEditRef.current = false;

    if (putClppAer.success) {
      navigate(ROUTES.SALES.quotationClppAerView.parse(putClppAer.data.quotationId ?? putClppAer.data.id ?? quotationData?.id), { state: { demographicData } });
    }

    toast.success("AER saved successfully!");
  }, [putClppAer]);

  const createAerPayload = (): TCreateClppQuotationWithAerPayload => {
    const lifeInsuranceBenefit = productBenefitsData.find((benefit) => benefit.benefitName === "Life Insurance");
    const selected = selectedOptions.length > 0 ? selectedOptions : [1];

    const defaultOption = selected[0];
    const benefitsPayload = (formState.benefits ?? []).map((b) => ({
      ageFrom: parseNumber(b.ageFrom?.toString() ?? "0"),
      ageTo: parseNumber(b.ageTo?.toString() ?? "0"),
      benefitId: Number(lifeInsuranceBenefit?.id ?? 0),
      // hasRider: !!hasRider,
      hasRider: false,
      maximumCoverage: parseNumber(b.maximumCoverage?.toString() ?? "0"),
      maximumTerm: parseNumber(b.maximumTerm?.toString() ?? "0"),
      nml: parseNumber(b.nml?.toString() ?? "0"),
      nel: parseNumber(b.nel?.toString() ?? "0"),
      option: defaultOption,
      rate: parseNumber(b.rate?.toString() ?? "0"),
    }));

    const premiumsPayload = savedOptions
      .filter((o) => selected.includes(o.option))
      .flatMap((o) => o.benefits.map((data: any) => ({ ...data, option: o.option })))
      .map((benefit) => ({
        netPremium: parseNumber((benefit.rate ?? benefit.rate ?? 0).toString()),
        grossPremium: parseNumber((benefit.grossRate ?? 0).toString()),
        option: parseInt(benefit.option?.toString() ?? "0"),
      }));

    return {
      quotations: {
        coopId: parseNumber(formState.baseInfo.cooperativeId ?? "0"),
        previousProvider: formState.baseInfo.previousProvider,
        contestability: parseNumber(formState.baseInfo.contestabilityId ?? "0"),
        totalNumberOfMembers: parseNumber(formState.memberAndAverageInfo.totalNumberOfMembers),
        coverageTypeId: parseNumber(formState.baseInfo.typeOfCoverage ?? "1"),
      },
      status: Statuses.DRAFT,
      quotationCondition: { condition: `${conditions}` },
      claimsExperienceYears: formState.claimsExperience.years.map((item) => ({
        year: item.year?.toString(),
        numberOfDeaths: parseNumber(item.numberOfDeaths),
        totalClaimAmount: parseNumber(item.totalClaimAmount),
      })),
      claimsExperienceAges: formState.claimsExperience.isAgeVisible
        ? formState.claimsExperience.ages.map((item) => ({
            ageFrom: parseInt(item.ageFrom?.toString() ?? "0"),
            ageTo: parseInt(item.ageTo?.toString() ?? "0"),
            numberOfDeaths: parseInt(item.numberOfDeaths?.toString() ?? "0"),
            totalClaimAmount: parseNumber(item.totalClaimAmount),
          }))
        : [],
      commissionDistributions: formState.commissionDistribution.map((data) => ({
        commissionTypeId: data.commissionTypeId,
        commissionAgeType: data.ageTypeId,
        ...(data.ageTypeId != COMMISION_AGE_TYPES.STANDARD ? { ageFrom: data.ageFrom } : {}),
        ...(data.ageTypeId != COMMISION_AGE_TYPES.STANDARD ? { ageTo: data.ageTo } : {}),
        rate: parseNumber(data?.rate),
      })),
      options: {
        clppBenefits: benefitsPayload,
        quotationPremiums: premiumsPayload,
        aerOptions: JSON.stringify(selected),
      },
      loanPortfolioAges: formState.loanPortfolio.loanPortfolioAges.map((item) => ({
        ageFrom: parseNumber(item.ageFrom),
        ageTo: parseNumber(item.ageTo),
        totalNumberOfMembers: parseNumber(item.numberOfBorrowers),
        totalLoanPortfolioAmount: parseNumber(item.totalLoanPortfolio),
      })),
      loanPortfolioYears: formState.loanPortfolio.loanPortfolioYears.map((item) => ({
        year: item.year,
        minimumAge: parseNumber(item.minimumAge),
        maximumAge: parseNumber(item.maximumAge),
        minimumLoanAmount: parseNumber(item.minimumLoanAmount),
        maximumLoanAmount: parseNumber(item.maximumLoanAmount),
        totalNumberOfBorrowers: parseNumber(item.numberOfBorrowers),
        totalLoanPortfolio: parseNumber(item.totalLoanPortfolio),
        averageAge: parseNumber(item.averageAge),
      })),
    };
  };

  const needsDemographicsForSeniors = (benefits: TClppBenefitInfo[], loanPortfolio: TClppLoanPortfolio) => {
    const hasExistingLoanPortfolioData = loanPortfolio.loanPortfolioYears.length > 0 || loanPortfolio.loanPortfolioAges.length > 0;
    if (hasExistingLoanPortfolioData) return false;
    return (benefits ?? []).some((b) => Number(b.ageFrom ?? 0) >= 66);
  };

  const handleUploadMasterList = () => uploadMasterListModalController.openFn();
  const handleShowDemographic = () => demographicModalController.openFn();

  useEffect(() => {
    if (!quotationData) return;
    const quotation = quotationData?.quotation ?? quotationData ?? {};

    const benefits = Array.isArray(quotation.clppBenefits) ? quotation.clppBenefits : [];
    let allBenefits = benefits;

    if (benefits.length > 0 && benefits[0].hasOwnProperty("option")) {
      const groupedBenefits = _.groupBy(benefits, "option");
      const firstOptionKey = Object.keys(groupedBenefits)[0];
      allBenefits = firstOptionKey ? groupedBenefits[firstOptionKey] : [];
    }

    const mappedBenefits = allBenefits.map((benefit: any) => ({
      ageFrom: Number(benefit.ageFrom || benefit.age_from || 18),
      ageTo: Number(benefit.ageTo || benefit.age_to || 0),
      maximumCoverage: Number(benefit.maximumCoverage || benefit.maximum_coverage || 0),
      maximumTerm: Number(benefit.maximumTerm || benefit.maximum_term || 0),
      nml: Number(benefit.nml || 0),
      nel: Number(benefit.nel || 0),
      rate: Number(benefit.rate || benefit.netRate || benefit.net_rate || 0),
    }));

    const loanPortfolioYears = Array.isArray(quotation.clppLoanPortfolioYears)
      ? quotation.clppLoanPortfolioYears.map((item: any) => ({
          year: item.year || new Date().getFullYear().toString(),
          minimumAge: item.minimumAge || 0,
          maximumAge: item.maximumAge || 0,
          minimumLoanAmount: item.minimumLoanAmount || 0,
          maximumLoanAmount: item.maximumLoanAmount || 0,
          totalLoanPortfolio: parseFloat((item.totalLoanPortfolio || "0").toString().replace(/,/g, "")),
          numberOfBorrowers: item.totalNumberOfBorrowers || 0,
          averageAge: item.averageAge || 0,
        }))
      : Array.isArray(quotation.loanPortfolioYears)
        ? quotation.loanPortfolioYears.map((item: any) => ({
            year: item.year || new Date().getFullYear().toString(),
            minimumAge: item.minimumAge || 0,
            maximumAge: item.maximumAge || 0,
            minimumLoanAmount: item.minimumLoanAmount || 0,
            maximumLoanAmount: item.maximumLoanAmount || 0,
            totalLoanPortfolio: parseFloat((item.totalLoanPortfolio || "0").toString().replace(/,/g, "")),
            numberOfBorrowers: item.numberOfBorrowers || item.totalNumberOfBorrowers || 0,
          }))
        : [];

    const loanPortfolioAges = Array.isArray(quotation.clppLoanPortfolioAges)
      ? quotation.clppLoanPortfolioAges.map((item: any) => ({
          ageFrom: item.ageFrom || 0,
          ageTo: item.ageTo || 0,
          totalLoanPortfolio: parseFloat((item.totalLoanPortfolioAmount || "0").toString().replace(/,/g, "")),
          numberOfBorrowers: item.totalNumberOfMembers || 0,
        }))
      : Array.isArray(quotation.loanPortfolioAges)
        ? quotation.loanPortfolioAges.map((item: any) => ({
            ageFrom: item.ageFrom || 0,
            ageTo: item.ageTo || 0,
            totalLoanPortfolio: parseFloat((item.totalLoanPortfolio || "0").toString().replace(/,/g, "")),
            numberOfBorrowers: item.numberOfBorrowers || 0,
          }))
        : [];

    const claimsExperienceYears = Array.isArray(quotation.quotationClaimsExperienceYear)
      ? quotation.quotationClaimsExperienceYear.map((item: { year: string; numberOfDeaths: number; totalClaimAmount: number }) => ({
          year: item.year || new Date().getFullYear().toString(),
          numberOfDeaths: item.numberOfDeaths || 0,
          totalClaimAmount: parseFloat((item.totalClaimAmount || "0").toString()),
        }))
      : Array.isArray(quotation.claimsExperienceYears)
        ? quotation.claimsExperienceYears
        : [];

    const claimsExperienceAges = Array.isArray(quotation.quotationClaimsExperienceAge)
      ? quotation.quotationClaimsExperienceAge.map((item: { ageFrom: number; ageTo: number; numberOfDeaths: number; totalClaimAmount: number }) => ({
          ageFrom: item.ageFrom || 0,
          ageTo: item.ageTo || 0,
          numberOfDeaths: item.numberOfDeaths || 0,
          totalClaimAmount: parseFloat((item.totalClaimAmount || "0").toString()),
        }))
      : Array.isArray(quotation.claimsExperienceAges)
        ? quotation.claimsExperienceAges
        : [];

    const commissionDistribution = Array.isArray(quotation.quotationCommissionDistribution)
      ? quotation.quotationCommissionDistribution.map((item: any) => ({
          commissionTypeId: Number(item.commissionTypeId || item.commissionType || 0),
          ageTypeId: Number(item.commissionAgeType || item.ageTypeId || item.commissionAgeTypeId || 1),
          ageFrom: item.ageFrom ? Number(item.ageFrom) : undefined,
          ageTo: item.ageTo ? Number(item.ageTo) : undefined,
          rate: Number(item.rate || 0),
        }))
      : Array.isArray(quotation.commissionDistribution)
        ? quotation.commissionDistribution.map((item: any) => ({
            commissionTypeId: Number(item.commissionTypeId || 0),
            ageTypeId: Number(item.ageTypeId || 1),
            ageFrom: item.ageFrom ? Number(item.ageFrom) : undefined,
            ageTo: item.ageTo ? Number(item.ageTo) : undefined,
            rate: Number(item.rate || 0),
          }))
        : [];

    const conditionsText = quotation.quotationCondition?.condition || "";
    setConditions(conditionsText);
    setSelectedOptions([1]);
    setFormState({
      status: Statuses.DRAFT,
      baseInfo: {
        cooperativeId: quotation.coopId || 0,
        previousProvider: quotation.previousProvider || "",
        typeOfCoverage: quotation.coverageTypeId?.toString() || "",
        contestabilityId: quotation.contestability || 0,
        productId: quotation.productId || 0,
      },
      memberAndAverageInfo: { totalNumberOfMembers: quotation.totalNumberOfMembers || 0 },
      loanPortfolio: { loanPortfolioYears, loanPortfolioAges },
      claimsExperience: {
        years: claimsExperienceYears,
        ages: claimsExperienceAges,
        isAgeVisible: claimsExperienceAges.length > 0,
      },
      commissionDistribution,
      benefits:
        mappedBenefits.length > 0
          ? mappedBenefits
          : [
              {
                ageFrom: 18,
                ageTo: 0,
                maximumCoverage: 0,
                maximumTerm: 0,
                nml: 0,
                nel: 0,
                rate: 0,
              },
            ],
    });

    // if (benefits.length > 0 && benefits[0].hasOwnProperty("hasRider")) {
    //   setHasRider(benefits[0].hasRider || false);
    // }

    if (quotation.fileName) {
      setFileName(quotation.fileName);
      setHasDemographics(true);
    }
  }, [quotationData]);

  useEffect(() => {
    if (demographicData) return;
    const borrowers = latestYearBorrowers(formState.loanPortfolio.loanPortfolioYears);
    if (borrowers > 0 && borrowers !== formState.memberAndAverageInfo.totalNumberOfMembers) {
      setFormState((prev) => ({
        ...prev,
        memberAndAverageInfo: { ...prev.memberAndAverageInfo, totalNumberOfMembers: borrowers },
      }));
    }
  }, [demographicData, formState.loanPortfolio.loanPortfolioYears]);

  const checkBenefits = useCallback(() => {
    const errors: string[] = [];
    if (formState.benefits.length < 1) {
      errors.push("At least 1 benefit is required");
    } else {
      const seenRanges: { ageFrom: number; ageTo: number }[] = [];
      for (let i = 0; i < formState.benefits.length; i++) {
        const item = formState.benefits[i];
        if (item.ageFrom == null || item.ageFrom === 0) errors.push(`Age From is required for benefit #${i + 1}`);
        if (item.ageTo == null || item.ageTo === 0) errors.push(`Age To is required for benefit #${i + 1}`);
        if (item.maximumTerm == null || item.maximumTerm === 0) errors.push(`Maximum Term is required for benefit #${i + 1}`);
        if (item.nml == null || item.nml === 0) errors.push(`NML is required for benefit #${i + 1}`);
        for (const range of seenRanges) {
          if (
            (item.ageFrom >= range.ageFrom && item.ageFrom <= range.ageTo) ||
            (item.ageTo >= range.ageFrom && item.ageTo <= range.ageTo) ||
            (item.ageFrom <= range.ageFrom && item.ageTo >= range.ageTo)
          ) {
            errors.push(`Age range for benefit #${i + 1} overlaps with another benefit.`);
            break;
          }
        }
        seenRanges.push({ ageFrom: item.ageFrom, ageTo: item.ageTo });
      }
    }
    return errors.reverse();
  }, [formState]);

  const checkForm = useCallback(() => {
    const errors = [];
    if (!formState.baseInfo.cooperativeId) errors.push("Cooperative is required");
    if (!formState.baseInfo.typeOfCoverage) errors.push("Type of coverage is required");
    if (!formState.baseInfo.contestabilityId) errors.push("Contestability is required");
    if (!formState.memberAndAverageInfo.totalNumberOfMembers) errors.push("Total number of members is required");
    if (formState.commissionDistribution.reduce((sum, c) => sum + Number(c.rate || 0), 0) > 35) {
      errors.push("Total commission distribution cannot exceed 35%.");
    }

    const totalBorrowers = formState.loanPortfolio.loanPortfolioYears.reduce((sum, item) => sum + Number(item.numberOfBorrowers || 0), 0);
    if (totalBorrowers !== formState.memberAndAverageInfo.totalNumberOfMembers) {
      errors.push("Total borrowers in portfolio should be equivalent to total number of members.");
    }

    formState.loanPortfolio.loanPortfolioYears.forEach((item, index) => {
      if (item.totalLoanPortfolio < item.maximumLoanAmount) {
        errors.push(`Total Loan Portfolio should not be less than Maximum Loan Amount for year #${index + 1}`);
      }
    });

    errors.push(...checkBenefits());

    const totalLoanPortfolio = formState.loanPortfolio.loanPortfolioYears.reduce((sum, item) => sum + item.totalLoanPortfolio, 0);
    const averageNEL = totalLoanPortfolio / formState.memberAndAverageInfo.totalNumberOfMembers;

    formState.benefits.forEach((benefit, index) => {
      if (benefit.nel > averageNEL) {
        errors.push(`NEL for benefit #${index + 1} should not be greater than expected NEL: ${averageNEL.toFixed(2)}`);
      }
    });

    return errors.reverse();
  }, [formState, checkBenefits]);

  const submitErrors = useMemo(() => {
    const errs: string[] = [];
    if (needsDemographicsForSeniors(formState.benefits, formState.loanPortfolio) && !hasDemographics) {
      errs.push("Upload demographics is required because a benefit age bracket starts at age 66 or above.");
    }
    return errs;
  }, [formState.benefits, formState.loanPortfolio, hasDemographics]);

  const validateLoanPortfolio = () => {
    const errors: string[] = [];

    const toNum = (v: unknown) => {
      if (typeof v === "number") return v;
      if (typeof v === "string") {
        const n = Number(v.replace(/,/g, "").trim());
        return Number.isFinite(n) ? n : NaN;
      }
      return NaN;
    };

    formState.loanPortfolio.loanPortfolioYears.forEach((item, index) => {
      const total = toNum(item.totalLoanPortfolio);
      const maxAmt = toNum(item.maximumLoanAmount);

      if (!Number.isFinite(total) || !Number.isFinite(maxAmt)) {
        errors.push(`Invalid number format in year #${index + 1}.`);
        return;
      }

      // enforce equality (use a tolerance if you want to allow rounding)
      const EPS = 0; // or e.g. 0.5 if you allow rounding
      if (Math.abs(total - maxAmt) > EPS) {
        errors.push(`Total Loan Portfolio must be equal to Maximum Loan Amount for year #${index + 1} (got ${total.toLocaleString()} vs ${maxAmt.toLocaleString()}).`);
      }
    });

    return errors;
  };
  const canSubmit = submitErrors.length === 0;

  const handleSelectOption = (optionId: number) => {
    setSelectedOptions((prev) => (prev.includes(optionId) ? prev.filter((x) => x !== optionId) : [...prev, optionId]));
  };

  const handleSaveEditAer = async () => {
    // A) Loan portfolio-specific checks
    const lpErrors = validateLoanPortfolio();
    if (lpErrors.length > 0) {
      setAlertError(lpErrors.pop() ?? "Please correct the loan portfolio errors before saving.");
      toast.error("Please correct the errors shown in the message above.");
      return;
    }

    const errors = checkForm();
    if (errors.length > 0) {
      setAlertError(errors.pop() ?? "Please correct the form errors before saving.");
      toast.error("Please correct the errors shown in the message above.");
      return;
    } else {
      setAlertError(undefined);
    }

    // (optional) If you also want the same senior-demographics guard when saving:
    if (needsDemographicsForSeniors(formState.benefits, formState.loanPortfolio) && !hasDemographics) {
      setAlertError("Upload demographics is required because a benefit age bracket starts at age 66 or above.");
      toast.error("Upload demographics is required when a benefit age bracket starts at age 66 or above.");
      return;
    }

    const payload = { ...createAerPayload(), status: ProductStatus.DRAFT };
    const isConfirmed = await confirmSaveOrEdit("Do you want to save the AER?");
    if (!isConfirmed) return;

    didRequestEditRef.current = true;
    try {
      dispatch(putClppAER({ ...payload, id: quotationData?.id as any }));
    } catch {
      toast.error("Failed to save AER");
    }
  };

  const handleCreateAer = async () => {
    const requireDemographics = needsDemographicsForSeniors(formState.benefits, formState.loanPortfolio);
    if (requireDemographics && !hasDemographics) {
      toast.error("Upload demographics is required when a benefit age bracket starts at age 66 or above.");
      return;
    }

    const validateLoanPortfolio = () => {
      const errors: string[] = [];
      formState.loanPortfolio.loanPortfolioYears.forEach((item, index) => {
        if (item.totalLoanPortfolio < item.maximumLoanAmount) {
          errors.push(`Total Loan Portfolio should not be less than Maximum Loan Amount for year #${index + 1}`);
        }
      });
      return errors;
    };

    const loanPortfolioErrors = validateLoanPortfolio();
    if (loanPortfolioErrors.length > 0) {
      setAlertError(loanPortfolioErrors?.pop() ?? "Please correct the loan portfolio errors before proceeding.");
      showError("Validation Error", "Please correct the errors shown in the message above.");
      return;
    }

    const errors = checkForm();
    if (errors.length > 0) {
      setAlertError(errors?.pop() ?? "Please correct the form errors before proceeding.");
      showError("Validation Error", "Please correct the errors shown in the message above.");
      return;
    } else {
      setAlertError(undefined);
    }

    const optionId = savedOptions.length + 1;
    const lifeInsuranceBenefit = productBenefitsData.find((benefit) => benefit.benefitName === "Life Insurance");

    const option: TClppOptions = {
      option: 0,
      benefits: formState.benefits.map((item) => ({
        ageFrom: item.ageFrom,
        ageTo: item.ageTo,
        benefitId: lifeInsuranceBenefit?.id ?? 0,
        // hasRider: hasRider ?? false,
        hasRider: false,
        maximumCoverage: item.maximumCoverage,
        maximumTerm: item.maximumTerm,
        nml: item.nml,
        nel: item.nel,
        rate: item.rate,
      })),
      conditions: conditions,
    };
    setSavedOptions((prev) => [...prev, option]);

    setSelectedOptions((prev) => (prev.includes(optionId) ? prev : [...prev, optionId]));
  };

  useEffect(() => {
    if (savedOptions.length > 0) {
      createClppQuotationWithAer(createAerPayload());
      clearSelectedProduct();
    }
  }, [savedOptions]);

  const handleSetFileName = (fileName: string) => setFileName(fileName);

  const latestYearBorrowers = (rows: TClppLoanPortfolio["loanPortfolioYears"]) => {
    if (!rows?.length) return 0;
    const latest = rows.reduce((acc, r) => {
      const y = parseInt((r.year ?? "").toString(), 10);
      const ay = parseInt((acc.year ?? "").toString(), 10);
      return (Number.isFinite(y) ? y : -Infinity) >= (Number.isFinite(ay) ? ay : -Infinity) ? r : acc;
    }, rows[0]);
    return Number(latest?.numberOfBorrowers || 0);
  };

  // Weighted average using age counts from demographics
  const computeAverageAgeFromDemographics = (demo: any): number => {
    const toNum = (v: unknown): number => {
      if (typeof v === "number") return v;
      if (typeof v === "string") {
        const n = Number(v.replace(/,/g, "").trim());
        return Number.isFinite(n) ? n : 0;
      }
      return 0;
    };

    const rows = Array.isArray(demo?.ageAndYearCounts?.age) ? demo.ageAndYearCounts.age : [];
    if (!rows.length) return 0;

    const getAge = (r: any) => toNum(r?.age ?? r?.Age ?? r?.ageValue);
    const getCount = (r: any) => toNum(r?.count ?? r?.totalCount ?? r?.members ?? r?.totalMembers ?? r?.totalNumberOfMembers);

    let wsum = 0;
    let total = 0;
    for (const r of rows) {
      const a = getAge(r);
      const c = getCount(r);
      if (a > 0 && c > 0) {
        wsum += a * c;
        total += c;
      }
    }
    if (total === 0) return 0;

    return Number((wsum / total).toFixed(1));
  };

  const fetchProduct = async () => {
    const { data } = await getProductNameService(productCode || "");
    if (!data) return;
    setSelectedProduct(data[0]);
  };

  useEffect(() => {
    if (productData?.id && (!formState.baseInfo.productId || formState.baseInfo.productId === 0)) {
      setFormState((prev) => ({
        ...prev,
        baseInfo: {
          ...prev.baseInfo,
          productId: Number(productData.id),
        },
      }));
    }
  }, [productData]);

  useEffect(() => {
    if (productData) return;
    fetchProduct();
  }, []);

  const fetchData = async () => {
    try {
      const { data } = await postDemographicDataService({
        fileName,
        productType: productCode as string,
        ageFrom: 0,
        ageTo: 200,
      });

      if (!data) {
        showError("Validation Error", "No demographics returned.");
        return;
      }

      setDemographicData(data);

      const toNum = (v: unknown): number => {
        if (typeof v === "number") return v;
        if (typeof v === "string") {
          const n = Number(v.replace(/,/g, "").trim());
          return Number.isFinite(n) ? n : 0;
        }
        return 0;
      };

      const totalMembers = toNum((data as any).totalNumberOfMembers ?? (data as any).totalMembers);
      const minAgeTop = toNum((data as any).minimumAge);
      const maxAgeTop = toNum((data as any).maximumAge);
      const minCoverageAmt = toNum((data as any).minimumCoverageAmount);
      const maxCoverageAmt = toNum((data as any).maximumCoverageAmount);
      const totalCoverageAmt = toNum((data as any).totalCoverageAmount);

      const agesArr = Array.isArray((data as any)?.ageAndYearCounts?.age) ? (data as any).ageAndYearCounts.age : [];
      const agesSorted = [...agesArr].sort((a: any, b: any) => toNum(a?.age) - toNum(b?.age));
      const firstAge = minAgeTop || (agesSorted.length ? toNum(agesSorted[0]?.age) : 0);
      const lastAge = maxAgeTop || (agesSorted.length ? toNum(agesSorted[agesSorted.length - 1]?.age) : 0);

      const currentYear = new Date().getFullYear().toString();
      const averageAge = computeAverageAgeFromDemographics(data);

      setFormState((prev) => ({
        ...prev,
        memberAndAverageInfo: { totalNumberOfMembers: totalMembers },
        loanPortfolio: {
          ...prev.loanPortfolio,
          loanPortfolioYears: [
            {
              year: currentYear,
              minimumAge: firstAge,
              minimumLoanAmount: minCoverageAmt,
              maximumAge: lastAge,
              maximumLoanAmount: maxCoverageAmt,
              totalLoanPortfolio: totalCoverageAmt,
              numberOfBorrowers: totalMembers,
              averageAge,
            },
          ],
        },
      }));
    } catch (error) {
      showError("Validation Error", "Error fetching product proposal");
    }
  };

  useEffect(() => {
    if (fileName) {
      fetchData();
    }
  }, [fileName]);

  // const handleRider = () => setHasRider((prev) => !prev);

  return (
    <section>
      <UploadMasterListModal
        controller={uploadMasterListModalController}
        onUpload={(files: any[], _: string) => {
          setHasDemographics(files.length > 0);
          return Promise.resolve();
        }}
        data={formState}
        setFileName={handleSetFileName}
      />
      {demographicData && <DemographicModal controller={demographicModalController} hasDemographics={hasDemographics} data={demographicData} />}
      <div className="grid grid-cols-12 gap-2 px-4">
        <div className="block col-span-12">
          <h3
            className={colorMode({
              classLight: "text-2xl font-[600] mb-3 text-primary",
              classDark: "text-2xl font-[600] mb-3 text-white/60",
            })}
          >
            CLPP QUOTATION
          </h3>
          <p
            className={colorMode({
              classLight: "text-gray text-base text-[12px]",
              classDark: "text-white/60 text-base text-[12px]",
            })}
          >
            Create a customized Coop Loan Protection Plan quotation based on cooperative-specific data.
          </p>
        </div>

        <div className="col-span-12 space-y-10">
          {alertError != undefined && <AlertError message={alertError} />}

          <ClppBaseInfo
            value={formState.baseInfo}
            onChange={(value) => {
              setFormState((prev) => ({
                ...prev,
                baseInfo: {
                  ...value,
                  productId: Number(productData?.id) || 0,
                },
              }));
            }}
          />

          <div className="mt-6 flex flex-wrap gap-4">
            <Button classNames="bg-info elevation-sm shadow-md rounded-[5px]" variant="default" onClick={handleUploadMasterList}>
              <div className="flex flex-row items-center gap-2">
                <PiCloudArrowUp size={25} />
                <span className="text-[14px] font-[400] font-poppins-medium">Upload Masterlist</span>
              </div>
            </Button>

            <Button classNames="!bg-[#99999926] rounded-[5px]" variant="default" onClick={handleShowDemographic}>
              <div className="flex flex-row items-center gap-2">
                <span
                  className={colorMode({
                    classLight: "text-gray/50 text-[14px] font-[400] font-poppins-medium",
                    classDark: "text-white/60 text-[14px] font-[400] font-poppins-medium",
                  })}
                >
                  Show Demographic
                </span>
              </div>
            </Button>
          </div>

          <hr className="my-10 border-gray/10" />

          <ClppMemberAndAverageInfo value={formState.memberAndAverageInfo} onChange={(value) => setFormState((prev) => ({ ...prev, memberAndAverageInfo: value }))} />

          <ClppLoanPortfolio
            value={formState.loanPortfolio}
            onChange={(v) => setFormState((prev) => ({ ...prev, loanPortfolio: v }))}
            lockYearFields={!!demographicData}
            hasDemographics={!!demographicData}
            demographics={demographicData}
          />

          <ClppClaimsExperienceInfo value={formState.claimsExperience} onChange={(value) => setFormState((prev) => ({ ...prev, claimsExperience: value }))} />

          <ClppBenefitInfo
            // hasRider={hasRider}
            // handleRider={handleRider}
            value={formState.benefits}
            onChange={(next) => setFormState((prev) => (_.isEqual(prev.benefits, next) ? prev : { ...prev, benefits: next }))}
          />

          <ClppCommissionDistributionInfo
            quotationData={quotationData}
            value={formState.commissionDistribution}
            onChange={(value) =>
              setFormState((prev) => ({
                ...prev,
                commissionDistribution: value,
              }))
            }
          />

          <ClppOptionsInfo
            contestabilityId={formState.baseInfo.contestabilityId}
            portfolioInfo={formState.loanPortfolio}
            emitConditions={setConditions}
            show={true}
            selectedOptions={selectedOptions}
            options={savedOptions}
            onSelectOption={handleSelectOption}
            onCreateAer={handleCreateAer}
            handleSaveEditAer={handleSaveEditAer}
            canSubmit={canSubmit}
            submitErrors={submitErrors}
            status={quotationData?.status}
          />
        </div>
      </div>
    </section>
  );
}
