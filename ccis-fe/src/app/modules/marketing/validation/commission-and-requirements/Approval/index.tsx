import { FC, ReactNode, useEffect, useState } from "react";
import Tabs from "@components/common/Tabs";
import ProductProposal from "./ProductProposal";
import CommissionStructure from "./CommissionStructure";
import Requirements from "./Requirements";
import Button from "@components/common/Button";
import { IoChevronBack } from "react-icons/io5";
import { useLocation, useParams } from "react-router-dom";
import { getProductProposalByIDService } from "@services/proposal/proposal.service";
import Loader from "@components/Loader";
import { IProductProposal } from "@interface/product-proposal.interface";
import { useProductProposalActions } from "@state/reducer/product-proposal";
import { IDefaultParams } from "@interface/common.interface";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { ICommissionDetails } from "@interface/commission-structure.interface";
import { useQuotationActions } from "@state/reducer/quotations";
import { navigateBack } from "@helpers/navigatorHelper";
const Approval: FC = () => {
  const params = useParams();
  const location = useLocation();
  const { proposal } = location.state || {}; // Access the passed state
  const { getProposal, clearProposalCommission, clearProposal } = useProductProposalActions();
  const [data, setData] = useState<IProductProposal>();
  const [loading, setLoading] = useState<boolean>(true);
  const { clearAerSignatoriesStatus } = useQuotationActions();
  const proposalCommissionStructure = useSelector((state: RootState) => {
    const data = state?.productProposal?.getProposal?.data;
    if (!Array.isArray(data)) return [];
    return data.length > 1 && data?.[1]?.commissionStructure
      ? data?.[1]?.commissionStructure?.commissionDetails
          ?.filter((item: ICommissionDetails) => item?.commissionType?.id && item?.commissionType?.commissionName && item?.rate !== null)
          .map((item: ICommissionDetails) => ({
            id: item?.commissionType!.id,
            commissionName: item?.commissionType!.commissionName,
            commissionPercentage: Number(item.commissionPercentage ?? 0.0),
          }))
      : // Assuming you want the second item if multiple exist
        data?.[0]?.commissionStructure?.commissionDetails
          ?.filter((item: ICommissionDetails) => item?.commissionType?.id && item?.commissionType?.commissionName && item?.rate !== null)
          .map((item: ICommissionDetails) => ({
            id: item?.commissionType!.id,
            commissionName: item?.commissionType!.commissionName,
            commissionPercentage: Number(item.commissionPercentage ?? 0.0),
          })); // Assuming you want the first item if multiple exist
  });

  const fetchData = async () => {
    try {
      const { data } = await getProductProposalByIDService(params.id ?? "");
      setData(data);
      setLoading(false);
    } catch (error) {
      console.error("Error fetching product proposal:", error);
    }
  };
  // getProposal
  const fetchProposal = async () => {
    const payload = {
      id: proposal?.cooperativeId,
      nameFilter: proposal?.product?.productCode,
      type: "standard",
    } as IDefaultParams;
    getProposal(payload);
  };

  useEffect(() => {
    fetchData();
    fetchProposal();
  }, []);
  const headers: string[] = ["Product Proposal", "Requirements", "Commission Structure"];
  const contents: ReactNode[] = [
    <ProductProposal data={data} />,
    <Requirements data={data} onSuccess={fetchData} />,
    <CommissionStructure data={data} onSuccess={fetchData} existingData={proposalCommissionStructure} />,
  ];

  const handleBack = () => {
    clearAerSignatoriesStatus();
    clearProposalCommission();
    navigateBack();
    clearProposal();
  };

  return (
    <div className="w-full">
      {" "}
      {loading && (
        <div>
          <Loader />
        </div>
      )}
      {!loading && (
        <>
          <div className=" w-full mb-2">
            <Button classNames="flex items-center justify-center border-none" type="button" variant="primary" outline onClick={handleBack}>
              <IoChevronBack />
              Back
            </Button>
          </div>
          <div className="w-full">
            <Tabs
              headers={headers}
              contents={contents}
              size="sm"
              headerClass="min-h-14"
              activeTabClassName="bg-primary text-white"
              inActiveTabClassName="bg-sky-50"
              contentClass=" min-h-[48rem] "
              className="w-full"
            />
          </div>
        </>
      )}
    </div>
  );
};

export default Approval;
