import { FC, useState } from "react";

import { FaFileAlt } from "react-icons/fa";
import { IProductProposal } from "@interface/product-proposal.interface";
import { formatDateToMonthYear } from "@helpers/date";
import Overlay from "@components/common/Overlay";
import { getTextStatusColor } from "@helpers/text";
import { AttachmentStatus } from "@enums/proposal-status";
import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import TextArea from "@components/form/TextArea";
import { Document, Page } from "react-pdf";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import { IRequirements } from "@interface/compliance";

import { pdfjs } from "react-pdf";
import { updateRequirementableRequirementService } from "@services/shared/shared.service";
import { toast } from "react-toastify";
import { showConfirmation } from "@helpers/prompt";
import CheckBox from "@components/form/CheckBox";
import CoopCodeModal from "./coop-code.modal";

pdfjs.GlobalWorkerOptions.workerSrc = new URL("pdfjs-dist/build/pdf.worker.min.mjs", import.meta.url).toString();

type Props = {
  data?: IProductProposal;
  onSuccess?: () => void;
};

const API_BASE_URL = import.meta.env.VITE_AWS_S3_ENDPOINT;

const Requirements: FC<Props> = ({ data, onSuccess }) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [remarks, setRemarks] = useState<string>("");
  const [isBlurry, setIsBlurry] = useState<boolean>(false);
  const [isInvalidDocument, setIsInvalidDocument] = useState<boolean>(false);
  const [viewInvalidOptions, setViewInvalidOptions] = useState<boolean>(false);
  const [validateModal, setValidateModal] = useState<boolean>(false);
  const [selectedRequirement, setSelectedRequirement] = useState<IRequirements>();

  const [numPages, setNumPages] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [fileError, setFileError] = useState<string>("");

  const [modalVisible, setModalVisible] = useState<boolean>(false);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setFileError("");
  };

  const getFileType = (filename: string) => {
    const extension = filename?.toLowerCase().split(".").pop();
    if (["jpg", "jpeg", "png", "gif"].includes(extension ?? "")) return "image";
    if (extension === "pdf") return "pdf";
    return "unknown";
  };

  const getFullUrl = (filepath: string) => {
    const cleanPath = filepath.replace(/^\.\//, "");
    return `${API_BASE_URL}/${cleanPath}`;
  };

  const renderFilePreview = () => {
    const attachments = selectedRequirement?.attachments || [];

    if (attachments.length === 0) {
      return <div className="text-center text-gray-500">No files available</div>;
    }

    return (
      <div className="flex flex-col gap-6">
        {attachments.map((file, index) => {
          if (!file?.filepath) return null;

          const fileUrl = getFullUrl(file?.filepath);
          const fileType = getFileType(file?.filename ?? "");

          return (
            <div key={file.id || index} className="border-b pb-6 last:border-b-0">
              <div className="font-medium mb-2">
                File {index + 1}: {file.filename}
              </div>
              {fileType === "image" ? (
                <div className="flex justify-center">
                  <img src={fileUrl} alt={file.filename} className="max-w-full max-h-[70vh] object-contain" onError={() => setFileError(`Failed to load image: ${file.filename}`)} />
                </div>
              ) : fileType === "pdf" ? (
                <>
                  <Document file={fileUrl} onLoadSuccess={onDocumentLoadSuccess} onLoadError={() => setFileError(`Failed to load PDF: ${file.filename}`)} className="w-full">
                    <Page pageNumber={pageNumber} className="w-full flex justify-center" scale={1.5} />
                  </Document>
                  {numPages > 1 && (
                    <div className="flex justify-center gap-4 mt-4">
                      <Button variant="primary" outline disabled={pageNumber <= 1} onClick={() => setPageNumber(pageNumber - 1)}>
                        Previous
                      </Button>
                      <span className="self-center">
                        Page {pageNumber} of {numPages}
                      </span>
                      <Button variant="primary" outline disabled={pageNumber >= numPages} onClick={() => setPageNumber(pageNumber + 1)}>
                        Next
                      </Button>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center text-gray-500">Unsupported file type</div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  const handleValid = () => {
    setIsInvalidDocument(false);
    setValidateModal(false);
    handleSubmit("VALID");
  };

  const handleInvalid = () => {
    handleSubmit("INVALID");
  };

  const handleSubmit = async (status: string) => {
    setIsLoading(true);
    let result;
    if (status === "INVALID") {
      result = await showConfirmation("Requirements", "Are you sure you want to reject this requirement?");
    }

    if (status === "VALID") {
      result = await showConfirmation("Requirements", "Are you sure you want to approve this requirement?");
    }

    if (!selectedRequirement) return;
    if (result?.isConfirmed) {
      try {
        const payload = {
          requirementableId: selectedRequirement?.requirementableId,
          id: selectedRequirement?.id,
          status: status,
          isInvalidDocument: isInvalidDocument,
          isBlurryFile: isBlurry,
          remarks: remarks,
        };
        const { data } = await updateRequirementableRequirementService(payload);
        if (data) {
          toast.success("Requirement updated successfully");
          setValidateModal(false);
          setRemarks("");
          setIsInvalidDocument(false);
          setIsBlurry(false);
          setSelectedRequirement(undefined);
          setViewInvalidOptions(false);
          onSuccess?.();
        }
      } catch (error) {
        console.log(error);
      } finally {
        setIsLoading(false);
      }
    } else {
      setIsLoading(false);
    }
  };

  /**
   * TODO: Submit status with remarks if invalid
   * 1. Submit requirement status not requirementable status
   */

  const handleValidateModal = (requirement: IRequirements) => {
    setRemarks("");
    setIsInvalidDocument(false);
    setIsBlurry(false);
    setPageNumber(1);
    setNumPages(0);
    setFileError("");

    if (!requirement?.requirementableId) return;
    setSelectedRequirement(requirement);
    setValidateModal(true);
  };

  return (
    <div className="w-full overflow-auto">
      {validateModal && (
        <Modal
          title="Validate Requirement"
          modalContainerClassName="max-w-5xl "
          titleClass="text-primary text-lg uppercase"
          isOpen={validateModal}
          onClose={() => {
            setValidateModal(false);
            setRemarks("");
            setIsInvalidDocument(false);
            setIsBlurry(false);
            setSelectedRequirement(undefined);
            setViewInvalidOptions(false);
            setIsLoading(false);
          }}
        >
          <>
            <div className="w-full flex flex-col min-h-[500px] h-full my-4 gap-2">
              <div className="w-full flex flex-col gap-4 p-4 border border-zinc-200 rounded-lg max-h-[70vh] overflow-y-auto">
                {fileError ? <div className="text-center text-red-500">{fileError}</div> : renderFilePreview()}
              </div>
              {viewInvalidOptions && (
                <div className="w-full p-4 border border-zinc-200 rounded-lg flex flex-col space-y-4">
                  <div className="font-poppins font-semibold text-center">Reason for Invalid</div>
                  <div className="flex flex-col !mb-5">
                    <CheckBox rightLabel="Invalid Document" checked={isInvalidDocument} onChange={(e) => setIsInvalidDocument(e.target.checked)} />
                    <CheckBox rightLabel="Blurry File" checked={isBlurry} onChange={(e) => setIsBlurry(e.target.checked)} />
                  </div>
                  <TextArea label="Remarks" placeholder="Reason" className="w-full mt-10" value={remarks} onChange={(e) => setRemarks(e.target.value)} />
                </div>
              )}
            </div>
            <div className="w-full flex justify-between ">
              <div>
                <Button
                  type="button"
                  variant="primary"
                  outline
                  onClick={() => {
                    setValidateModal(false);
                    setViewInvalidOptions(false);
                    setRemarks("");
                    setIsInvalidDocument(false);
                    setIsBlurry(false);
                    setSelectedRequirement(undefined);
                  }}
                >
                  {" "}
                  Cancel
                </Button>
              </div>
              {!viewInvalidOptions ? (
                <div className=" gap-2 flex">
                  <Button type="button" classNames="w-48" variant="danger" onClick={() => setViewInvalidOptions(true)}>
                    {" "}
                    Invalid
                  </Button>
                  <Button type="button" classNames="w-48" variant="success" onClick={handleValid}>
                    {" "}
                    Valid
                  </Button>
                </div>
              ) : (
                <div className=" gap-2 flex">
                  <Button type="button" classNames="w-48" variant="primary" onClick={handleInvalid}>
                    {" "}
                    Submit
                  </Button>
                </div>
              )}
            </div>
          </>
        </Modal>
      )}
      {isLoading && <Overlay header="Processing..." bodyText="We are processing your request." />}
      <div className="p-4">
        <div className="w-full flex flex-col gap-4 mb-4">
          <div className="flex items-center justify-center">
            {" "}
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Product Proposed</div>
              <div className="w-2/3 text-black"> {data?.product?.name} </div>
            </div>
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400"> Creation Date</div>
              <div className="w-2/3 text-black">{formatDateToMonthYear(data?.createdAt) ?? ""}</div>
            </div>
          </div>
          <div className="flex items-center justify-center">
            {" "}
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Cooperative</div>
              <div className="w-2/3 text-black">{data?.cooperative?.coopName}</div>
            </div>
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Status</div>
              <div className="w-2/3">
                <span className={`${getTextStatusColor(data?.requirementable?.status ?? "")}`}>{data?.requirementable?.status}</span>
              </div>
            </div>
          </div>
          <div className="flex items-start justify-start">
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Coop Code</div>
              <div className="w-2/3 text-black">{data?.cooperative?.coopCode}</div>
            </div>
          </div>
        </div>
      </div>

      <div className="px-10">
        <div className="w-full min-h-60 ">
          {/* {data?.requirementable?.status === "FOR_APPROVAL" && ( */}
          <div>
            <div className="flex justify-between gap-2 mt-14 border-b border-zinc-200 pb-8">
              <div>
                <div className="font-poppins-semibold">Submit Requirements</div>
                <div className="text-xs text-zinc-400">Please scan and upload requirements.</div>
              </div>
            </div>

            <div>
              {data?.requirementable?.requirements?.map((requirement) => (
                <div key={requirement.id}>
                  <div className="py-8 border-b border-zinc-200 flex justify-between">
                    <div className="w-1/2 space-y-8">
                      <div className="flex flex-col">
                        <div className="flex items-start gap-8">
                          <span className="text-sm flex-1">{requirement.name}</span>
                          <span className={`${getTextStatusColor(requirement?.status ?? "")}`}>{requirement.status ?? ""}</span>
                        </div>
                      </div>
                      <div>
                        {requirement?.attachments?.length &&
                          requirement?.attachments?.map((attachment: any) => (
                            <div key={attachment.id} className="border-slate-900 flex w-full p-2  mb-5">
                              <div className="flex items-center justify-between w-full">
                                <div className="flex align-center justify-center">
                                  <FaFileAlt size={30} className="mr-2" />
                                  <p className="mt-1 cursor-pointer hover:underline">
                                    <a href="#" download target="_blank">
                                      {attachment?.filename}
                                    </a>
                                  </p>
                                </div>
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                    <div className="w-1/12"></div>
                    <div className="w-1/3 flex items-center">
                      {requirement?.status !== AttachmentStatus.valid && (
                        <div className="mt-2">
                          {requirement?.status === AttachmentStatus.invalid && (
                            <div className="text-xs">
                              <p className="text-sm font-poppins-semibold">Remarks: </p>
                              <ul className="list-inside">
                                {requirement?.isInvalidDocument ? <li>Invalid Document</li> : null}
                                {requirement?.isBlurryFile ? <li>Blurry File</li> : null}
                                {requirement?.remarks && <li>Others: {requirement?.remarks}</li>}
                              </ul>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                    <div className="flex justify-end items-center">
                      {!["VALID", "INVALID"].includes(requirement?.status ?? "") && (
                        <Button variant="primary" classNames="btn btn-sm border-0 w-32" onClick={() => handleValidateModal(requirement)}>
                          Validate
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          {/* )} */}
          {/* {data?.requirementable?.status !== "FOR_APPROVAL" && (
            <div>
              <h3 className="text-lg font-poppins-semibold text-primary2">
                No Requirements Found
              </h3>
              <p className="text-zinc-400">
                This proposal has no requirements or attachments.
              </p>
            </div>
          )} */}
          <Button classNames="block bg-accent text-white my-2 ms-auto" onClick={() => setModalVisible(true)}>
            Generate Coop Code
          </Button>

          {/*  */}
          <CoopCodeModal isOpen={modalVisible} coopId={Number(data?.cooperative?.id ?? 0)} onClose={() => setModalVisible(false)} />
        </div>
      </div>
    </div>
  );
};

export default Requirements;
