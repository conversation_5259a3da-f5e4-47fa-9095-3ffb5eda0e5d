import { FC, useState, useEffect, useMemo } from "react";
import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import { GrValidate } from "react-icons/gr";
import TextField from "@components/form/TextField";
import { VscPercentage } from "react-icons/vsc";
import { IProductProposal, IProductProposalCommissionApproval } from "@interface/product-proposal.interface";
import { ICommissionDetails } from "@interface/commission-structure.interface";
import { toast } from "react-toastify";
import { formatDateToMonthYear } from "@helpers/date";
import { capitalizeFirstLetterWords, formatStringAtoZ0to9, getTextStatusColor } from "@helpers/text";
import TextArea from "@components/form/TextArea";
import { confirmSaveOrEdit, showConfirmation } from "@helpers/prompt";
import { postProductProposalCommissionService } from "@services/product-proposal/product-proposal.service";
import ApproverForm from "../../Modal/set-approver";
import ApproverStatusForm from "../../Modal/approver-status";
import { useSignatoryTemplatesManagementActions } from "@state/reducer/utilities-signatory-template";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { IDefaultParams, ISelectOptions } from "@interface/common.interface";
import { IUtilitiesSignatoryTemplates } from "@interface/utilities.interface";
import { APPROVABLE_TYPE, Statuses } from "@constants/global-constant-value";
import { useProductProposalActions } from "@state/reducer/product-proposal";
import Tabs from "@components/common/Tabs";
import { FaUsers } from "react-icons/fa";
import Typography from "@components/common/Typography";
import Signatories2 from "@modules/users/approvals/review-revisions/Tabs/Signatories2";
import ApprovalHistory from "@modules/admin/products/components/Common/ApprovalHistory";
import { ISignatories } from "@interface/products.interface";
import ApproverRemarksDynamic from "@modules/admin/approval-aer/components/ApproverRemarksDynamic";
import { useQuotationActions } from "@state/reducer/quotations";
import LoadingButton from "@components/common/LoadingButton";
import { useUserId, useUserRoleName } from "@helpers/data";
import { UserRoles } from "@interface/routes.interface";
import CoopCodeModal from "../Requirements/coop-code.modal";
type Props = {
  data?: IProductProposal;
  onSuccess?: () => void;
  existingData?: ProductProposalCommission[];
};

type ProductProposalCommission = {
  id?: number;
  commissionName?: string;
  commissionPercentage?: number;
};

const CommissionStructure: FC<Props> = ({ data, onSuccess, existingData }) => {
  const [validateModal, setValidateModal] = useState<boolean>(false);
  const [commissionTypes, setCommissionTypes] = useState<ProductProposalCommission[]>([]);
  const [totalCommission, setTotalCommission] = useState<number>(0);
  const [isForRevision, setIsForRevision] = useState<boolean>(false);
  const [validationRemarks, setValidationRemarks] = useState<string>("");
  const [hasCommissionStructure, setHasCommissionStructure] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedTemplateId, setSelectedTemplateId] = useState<number | undefined>(undefined);
  const [approverModal, setApproverModal] = useState<boolean>(false);
  const [initialValues, setInitialValues] = useState<IProductProposalCommissionApproval>({} as IProductProposalCommissionApproval);
  const { getSignatoryTemplate, getSignatoryTemplateID, clearSelectedSignatoryTemplate } = useSignatoryTemplatesManagementActions();
  const { getAerSignatoriesLogs, putAerSignatoriesStatus, clearAerSignatoriesStatus } = useQuotationActions();
  const { postProposalCommission, getProposal, clearProposalCommission } = useProductProposalActions();
  const [dataCurrentSignatory, setDataCurrentSignatory] = useState<ISignatories>({} as ISignatories);
  const [remarksModal, setRemarksModal] = useState<boolean>(false);
  const [isApproved, setIsApproved] = useState<boolean>(false);
  const [isOpenApproverStatus, setIsOpenApproverStatus] = useState<boolean>(false);
  const [processing, setProcessing] = useState<boolean>(false);
  const toggleRemarksModal = () => setRemarksModal((prev) => !prev);
  const handleDataCurrentSignatory = (data: ISignatories) => {
    setDataCurrentSignatory(data);
  };
  const userId = useUserId();
  const userRoleName = useUserRoleName();
  const signatoryTemplate = useSelector((state: RootState) => state.utilitiesSignatoryTemplate.signatoryTemplates);
  const commissionStructureData = useSelector((state: RootState) => state?.productProposal?.getProposal?.data?.[0]) || {};
  const { success: postProposalCommissionSuccess, loading: isSubmitting } = useSelector((state: RootState) => state?.productProposal?.postProposalCommission);
  const signatoryLogs = useSelector((state: RootState) => state?.quotation?.getAerSignatoriesLogs?.data?.data);
  const { success: putSignatoriesSuccess, error: putSignatoriesError } = useSelector((state: RootState) => state?.quotation?.putAerSignatoriesStatus);
  const { signatories: signatoriesList, status: approvalStatus } = commissionStructureData?.commissionStructure?.approval || [];
  const [modalVisible, setModalVisible] = useState<boolean>(false);

  const handleApproveStatusModal = () => {
    setIsOpenApproverStatus((prev) => !prev);
    setIsApproved(true);
  };
  const handleDisapproveStatusModal = () => {
    setIsOpenApproverStatus((prev) => !prev);
    setIsApproved(false);
  };
  useEffect(() => {
    if (data?.commissionStructure && data?.commissionStructure?.commissionDetails) {
      const newCommissionTypes = data?.commissionStructure?.commissionDetails
        .filter((item: ICommissionDetails) => item?.commissionType?.id && item?.commissionType?.commissionName && item?.rate !== null)
        .map((item: ICommissionDetails) => ({
          id: item?.commissionType!.id,
          commissionName: item?.commissionType!.commissionName,
          commissionPercentage: Number(item.commissionPercentage ?? 0.0),
        }));
      setCommissionTypes(newCommissionTypes);
      setTotalCommission(parseFloat(newCommissionTypes.reduce((acc, curr) => acc + (curr.commissionPercentage ?? 0), 0).toFixed(4)));
      setHasCommissionStructure(true);
    } else {
      setHasCommissionStructure(false);
    }
  }, [data?.productRevision?.commission, data?.commissionStructure]);
  useEffect(() => {
    getSignatoryTemplate({ filter: "" });
  }, []);
  useEffect(() => {
    if (!selectedTemplateId) return;
    getSignatoryTemplateID({ id: Number(selectedTemplateId) });
    setInitialValues({
      signatoryTemplateId: Number(selectedTemplateId),
      approvableType: APPROVABLE_TYPE.PRODUCT_PROPOSAL_COMMISSION_STRUCTURE,
      approvableId: Number(commissionStructureData?.commissionStructure?.id),
      status: Statuses.FOR_SIGNATORY,
    });
  }, [selectedTemplateId]);
  useEffect(() => {
    if (!commissionStructureData?.commissionStructure?.approval?.id) return;
    getAerSignatoriesLogs({ id: commissionStructureData?.commissionStructure?.approval?.id });
  }, [commissionStructureData?.commissionStructure?.approval?.id]);
  const fetchProposal = async () => {
    const payload = {
      id: commissionStructureData?.cooperativeId,
      nameFilter: commissionStructureData?.product?.productCode,
      type: "standard",
    } as IDefaultParams;
    getProposal(payload);
  };
  useEffect(() => {
    if (postProposalCommissionSuccess) {
      fetchProposal();
    } else {
      return;
    }
  }, [postProposalCommissionSuccess]);
  const signatoryTemplateOption = useMemo<ISelectOptions[]>(() => {
    return signatoryTemplate.map((item: IUtilitiesSignatoryTemplates) => ({
      text: item.templateName,
      value: item?.id.toString(),
    }));
  }, [signatoryTemplate]);

  useEffect(() => {
    clearProposalCommission();
  }, []);
  const handleTemplateChange = (template: any) => {
    setSelectedTemplateId(template);
  };
  const handleValidateModal = () => {
    setValidateModal((prev) => !prev);
    setIsForRevision(false);
    setValidationRemarks("");
  };
  const handleApproverModal = () => {
    setApproverModal((prev) => !prev);
  };
  const handleSubmitValidationRemarks = () => {
    handleSubmit("FOR_REVISION", validationRemarks);
  };

  const handleApprove = () => {
    handleSubmit("APPROVED");
  };

  const handleSubmit = async (status: string, remarks?: string) => {
    let result;
    if (status === "APPROVED") {
      result = await showConfirmation("Commission Structure", "Are you sure you want to approve this commission structure?");
    }

    if (status === "FOR_REVISION") {
      result = await showConfirmation("Commission Structure", "Are you sure you want to request revision for this commission structure?");
    }

    if (result?.isConfirmed) {
      setIsLoading(true);
      if (!data?.id) return;

      const payload = {
        productProposalId: data.id,
        status: status,
        commissionDetails: commissionTypes
          .filter((item) => item.id != null && item.commissionPercentage != null)
          .map((item) => ({
            commissionTypeId: item.id!,
            commissionPercentage: item.commissionPercentage!,
          })),
        totalCommission: totalCommission,
        remarks: remarks,
      };
      try {
        const { data } = await postProductProposalCommissionService(payload);
        if (data) {
          setValidateModal(false);
          toast.success("Commission structure updated successfully");
          setValidationRemarks("");
          onSuccess?.();
        }
        setIsLoading(false);
      } catch (error) {
        setValidateModal(false);
        setValidationRemarks("");

        toast.error("Failed to update commission structure");
        setIsLoading(false);
      }
    }
  };
  const combinedData = [...commissionTypes, ...((existingData ?? []) as ProductProposalCommission[])];
  const finalData = combinedData.reduce(
    (acc: any, item: ProductProposalCommission) => {
      const existing = acc.commissionDetails.find((detail: any) => detail.id === item.id);

      if (existing) {
        if (commissionTypes.some((d) => d.id === item.id)) {
          existing.data1commissionPercentage = commissionTypes.find((d) => d.id === item.id)?.commissionPercentage || 0;
        }
        if (existingData && existingData.some((d) => d.id === item.id)) {
          existing.data2commissionPercentage = existingData.find((d) => d.id === item.id)?.commissionPercentage || 0;
        }
      } else {
        acc.commissionDetails.push({
          id: item.id,
          commissionName: item.commissionName,
          data1commissionPercentage: commissionTypes.find((d) => d.id === item.id)?.commissionPercentage || 0,
          data2commissionPercentage: (existingData && existingData.find((d) => d.id === item.id)?.commissionPercentage) || 0,
        });
      }

      return acc;
    },
    { commissionDetails: [] }
  );

  const { totalData1, totalData2 } = finalData?.commissionDetails?.reduce(
    (totals: any, item: any) => {
      totals.totalData1 += item.data1commissionPercentage;
      totals.totalData2 += item.data2commissionPercentage;
      return totals;
    },
    { totalData1: 0, totalData2: 0 }
  );

  const handleSubmitCommissionApproval = async (initialValues: IProductProposalCommissionApproval) => {
    try {
      const isConfirmed = await confirmSaveOrEdit(`Are you sure about your selected approver?`);
      if (isConfirmed) {
        postProposalCommission(initialValues);
        handleApproverModal();
        clearSelectedSignatoryTemplate();
      }
    } catch (error) {
      toast.error(`Error submitting commission approval: ${(error as any).message}`);
    }
  };
  const isSignatory = signatoriesList?.some((signatory: ISignatories) => signatory?.userId === userId && signatory?.status === Statuses.PENDING);
  const signatoryId = signatoriesList?.find((signatory: ISignatories) => signatory?.userId === userId)?.id;
  const submitSignatory = () => {
    setProcessing(true);
    try {
      if (signatoryId) {
        const payload = {
          approvalSignatoryId: signatoryId,
          status: isApproved ? Statuses.APPROVED : Statuses.REJECTED,
        };
        putAerSignatoriesStatus(payload);
      } else {
        toast.error("No signatory found for this user.");
      }
    } catch (error) {
      toast.error("Failed to update status."); // Optional: Add user feedback
    }
  };
  useEffect(() => {
    if (putSignatoriesSuccess) {
      setProcessing(false);
      setIsOpenApproverStatus(false);
      fetchProposal();
      clearAerSignatoriesStatus();
    } else {
      setProcessing(false);
      setIsOpenApproverStatus(false);
      clearAerSignatoriesStatus();
    }
  }, [putSignatoriesSuccess, putSignatoriesError]);

  const isCommissionDraft = () => {
    return data?.commissionStatus === Statuses?.DRAFT;
  };
  return (
    <div className="w-full">
      {validateModal && (
        <Modal title="Verify Commission Structure" modalContainerClassName="max-w-5xl " titleClass="text-primary text-lg uppercase" isOpen={validateModal} onClose={handleValidateModal}>
          <>
            <div className="w-full flex flex-col min-h-[500px] h-full my-4 gap-2 ">
              <div className="w-full flex flex-col gap-4 p-4 border border-zinc-200 rounded-lg">
                {commissionTypes.map((item: any) => (
                  <div key={item.id} className="flex  items-center justify-between w-full">
                    <div> {item.commissionName}</div>
                    <div>
                      {" "}
                      <TextField type="number" className="py-6 w-12" size="sm" rightIcon={<VscPercentage />} value={Number(item.commissionPercentage) ?? 0} disabled={true} />
                    </div>
                  </div>
                ))}
                {commissionTypes.length !== 0 && (
                  <div className="flex  items-center justify-between w-full border-y border-zinc-200 py-4">
                    <div> Total</div>
                    <div>
                      {" "}
                      <TextField placeholder="Total Commission" disabled type="number" className="py-6 w-12" size="sm" rightIcon={<VscPercentage />} value={totalCommission} />
                    </div>
                  </div>
                )}
              </div>
              {isForRevision && (
                <div className="w-full p-4 border border-zinc-200 rounded-lg flex flex-col space-y-4">
                  <div className="font-poppins font-semibold text-center">Remarks for Validation</div>
                  <TextArea label="Remarks" placeholder="Reason" className="w-full" value={validationRemarks} onChange={(e) => setValidationRemarks(e.target.value)} />
                </div>
              )}
            </div>
            <div className="w-full flex justify-between ">
              <div>
                <Button type="button" variant="danger" outline onClick={handleValidateModal}>
                  {" "}
                  Cancel
                </Button>
              </div>
              {!isForRevision ? (
                <div className=" gap-2 flex">
                  <Button type="button" classNames="w-48" variant="primary" onClick={() => setIsForRevision(true)} disabled={isLoading}>
                    {" "}
                    For Revision
                  </Button>
                  <Button type="button" classNames="w-48" variant="success" onClick={handleApprove}>
                    {" "}
                    Approve
                  </Button>
                </div>
              ) : (
                <div className=" gap-2 flex">
                  <Button type="button" classNames="w-48" variant="primary" onClick={handleSubmitValidationRemarks} disabled={isLoading}>
                    Submit
                  </Button>
                </div>
              )}
            </div>
          </>
        </Modal>
      )}
      <ApproverForm
        isOpen={approverModal}
        onClose={handleApproverModal}
        signatoryTemplateOption={signatoryTemplateOption}
        onTemplateChange={handleTemplateChange}
        initialValues={initialValues}
        onSubmit={handleSubmitCommissionApproval}
      />
      <div className="p-4">
        <div className="w-full flex flex-col gap-4 mb-4">
          <div className="flex items-center justify-center">
            {" "}
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Product Proposed</div>
              <div className="w-2/3 text-black"> {data?.product?.name} </div>
            </div>
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400"> Creation Date</div>
              <div className="w-2/3 text-black">{formatDateToMonthYear(data?.createdAt) ?? ""}</div>
            </div>
          </div>
          <div className="flex items-center justify-center">
            {" "}
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Cooperative</div>
              <div className="w-2/3 text-black">{data?.cooperative?.coopName}</div>
            </div>
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Status</div>
              <div className="w-2/3 text-black">
                {formatStringAtoZ0to9(userRoleName) === formatStringAtoZ0to9(UserRoles.marketing) ? (
                  <span className={`${getTextStatusColor(data?.commissionStatus ?? "")}`}>{capitalizeFirstLetterWords(data?.commissionStatus ?? "", "_")}</span>
                ) : (
                  <span className={`${getTextStatusColor(approvalStatus ?? "")}`}>{approvalStatus ?? ""}</span>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-start justify-start">
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Coop Code</div>
              <div className="w-2/3 text-black">{data?.cooperative?.coopCode}</div>
            </div>
          </div>
        </div>
      </div>

      {hasCommissionStructure && !isCommissionDraft() && (
        <div className="flex flex-row  px-10 w-full ">
          <div className="flex flex-row w-full min-h-60  ">
            <div className="grid grid-cols-1 mt-8 sm:grid-cols-3 gap-4 w-full border-4 border-zinc-100 rounded p-6 ">
              <div className="text-left grid grid-cols-1 gap-4  w-full truncate ">
                <div className="w-full font-poppins-semibold truncate text-center ">COMMISSION SCHEME</div>
                {finalData?.commissionDetails?.map((item: any) => (
                  <div key={item.id} className="flex items-left justify-between w-full mb-2">
                    <div className=" flex-wrap justify-between mt-4 py-2 w-full text-center uppercase truncate text-[18px]  ">{item.commissionName}</div>
                  </div>
                ))}

                <div className=" flex-wrap justify-between mt-4 py-2 w-full text-center truncate uppercase">TOTAL</div>
              </div>
              <div className="text-center grid grid-cols-1 gap-4 w-full justify-center ">
                <div className="w-full font-poppins-semibold">EXISTING</div>
                {finalData?.commissionDetails?.map((item: any) => (
                  <div key={item.id} className="flex items-left justify-between w-full">
                    <div className="relative flex flex-wrap justify-between mt-4 py-2 w-full text-left">
                      <TextField
                        placeholder="Enter Commission"
                        type="number"
                        className="w-full min-w-0 overflow-hidden py-6 pr-12"
                        size="md"
                        value={Number(item.data1commissionPercentage) ?? 0}
                        disabled={true}
                        rightIcon={<VscPercentage className="absolute right-4 top-1/2 -translate-y-1/2 text-base sm:text-lg md:text-xl" />}
                      />
                    </div>
                  </div>
                ))}
                <div className="relative flex flex-wrap justify-between mt-4 py-2 w-full text-left">
                  <TextField
                    placeholder="Enter Commission"
                    type="number"
                    className="w-full min-w-0 overflow-hidden py-6 pr-12"
                    size="md"
                    value={Number(totalData1) ?? 0}
                    disabled={true}
                    rightIcon={<VscPercentage className="absolute right-4 top-1/2 -translate-y-1/2 text-base sm:text-lg md:text-xl" />}
                  />
                </div>
              </div>
              <div className="text-left grid grid-cols-1 gap-4 w-full  ">
                <div className="w-full font-poppins-semibold truncate text-center">NEW PROPOSAL</div>
                {finalData?.commissionDetails?.map((item: any) => (
                  <div key={item.id} className="flex items-left justify-between w-full">
                    <div className="relative flex flex-wrap justify-between mt-4 py-2 w-full text-left">
                      <TextField
                        placeholder="Enter Commission"
                        type="text"
                        className="w-full min-w-0 overflow-hidden py-6 pr-12"
                        size="md"
                        value={Number(item.data2commissionPercentage) ?? 0}
                        disabled={true}
                        rightIcon={<VscPercentage className="absolute right-4 top-1/2 -translate-y-1/2 text-base sm:text-lg md:text-xl" />}
                      />
                    </div>
                  </div>
                ))}
                <div className="relative flex flex-wrap justify-between mt-4 py-2 w-full text-left">
                  <TextField
                    placeholder="Enter Commission"
                    type="number"
                    className="w-full min-w-0 overflow-hidden py-6 pr-12"
                    size="md"
                    value={Number(totalData2) ?? 0}
                    disabled={true}
                    rightIcon={<VscPercentage className="absolute right-4 top-1/2 -translate-y-1/2 text-base sm:text-lg md:text-xl" />}
                  />
                </div>
              </div>
              {commissionStructureData?.commissionStructure && commissionStructureData?.commissionStructure?.approval === null && !postProposalCommissionSuccess ? (
                <div className="text-left grid grid-cols-1 gap-4 w-full justify-between sm:col-end-4">
                  <LoadingButton type="button" variant="primary" isLoading={isSubmitting} onClick={handleApproverModal}>
                    <GrValidate size={20} />
                    SET APPROVER
                  </LoadingButton>
                </div>
              ) : null}
              {isSignatory && (
                <div className="text-left grid grid-cols-1 gap-4 w-full col-span-2 sm:col-end-4 ">
                  <div className="flex flex-col justify-end sm:flex-row w-full">
                    <div className="w-full flex flex-row p-2  sm:w-1/2 text-xs overflow-auto md:w-1/2 lg:w-1/4 xl:w-1/4">
                      <LoadingButton type="button" variant="danger" isLoading={isSubmitting} onClick={handleDisapproveStatusModal}>
                        REJECT
                      </LoadingButton>
                    </div>
                    <div className="w-full flex flex-row p-2  sm:w-1/2 text-xs overflow-auto md:w-1/2 lg:w-1/4 xl:w-1/4">
                      <LoadingButton type="button" variant="primary" isLoading={isSubmitting} onClick={handleApproveStatusModal}>
                        APPROVED
                      </LoadingButton>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {data?.commissionStructure?.remarks && (
            <div className="w-1/4 flex flex-col gap-2 mt-10 ml-4  ">
              <h4 className="font-poppins-semibold uppercase">Remarks</h4>
              <p className="text-zinc-400">{data?.commissionStructure?.remarks}</p>
            </div>
          )}
          {commissionStructureData?.commissionStructure && commissionStructureData?.commissionStructure?.approval && (
            <>
              <div className="flex flex-row w-1/3 justify-center rounded-md ml-6 border border-zinc-100  ">
                <div className="col-span-12 w-1/2   md:col-span-2 justify-center  ">
                  <div className="flex flex-row  w-full justify-center items-center ">
                    <FaUsers size={20} className="mr-2" />
                    <Typography size="lg">SIGNEES</Typography>
                  </div>
                  <Tabs
                    className="flex flex-end justify-center mt-4"
                    contentClass="p-6 border-0 bg-sky-50"
                    headerClass="text-xs rounded-t-lg h-10"
                    activeTabClassName="bg-primary !text-white text-xs"
                    headers={["Ongoing Approvals", "Approval History"]}
                    contents={[
                      <Signatories2
                        signatories={commissionStructureData?.commissionStructure?.approval?.signatories ?? []}
                        toggleRemarksModal={toggleRemarksModal}
                        selectedSignatory={handleDataCurrentSignatory}
                      />,
                      <ApprovalHistory historyData={signatoryLogs} />,
                    ]}
                  />
                </div>
              </div>
            </>
          )}
        </div>
      )}
      {!isCommissionDraft() && data?.cooperative?.coopCode === "" && (
        <div className="flex flex-row justify-end w-full mt-10 px-16  ">
          <Button variant="primary" onClick={() => setModalVisible(true)}>
            Generate Coop Code
          </Button>
        </div>
      )}

      {!hasCommissionStructure && (
        <div className="w-full flex mt-10 justify-center flex-col items-center">
          <div className="font-poppins-semibold text-primary2 text-lg">No Commission Structure Found</div>
          <p className="text-zinc-400">It's either the proposal has no commission structure or the commission structure has not been submitted yet.</p>
        </div>
      )}
      {remarksModal && (
        <Modal title="Remarks" titleClass="text-md md:text-lg" modalContainerClassName="!max-w-2xl" isOpen={remarksModal} onClose={toggleRemarksModal}>
          <ApproverRemarksDynamic currentSignatory={dataCurrentSignatory} />
        </Modal>
      )}
      {isOpenApproverStatus && <ApproverStatusForm isOpen={isOpenApproverStatus} status={isApproved} onClose={handleApproveStatusModal} onSubmit={submitSignatory} isSubmitting={processing} />}
      <CoopCodeModal isOpen={modalVisible} coopId={Number(data?.cooperative?.id ?? 0)} onClose={() => setModalVisible(false)} />
    </div>
  );
};

export default CommissionStructure;
