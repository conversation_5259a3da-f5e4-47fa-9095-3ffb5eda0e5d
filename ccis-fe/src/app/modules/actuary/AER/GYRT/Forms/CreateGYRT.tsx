import React, { useEffect, useState, useMemo } from "react";
import { FormikProvider, Form, useFormik } from "formik";
import { IoChevronBack } from "react-icons/io5";
import Button from "@components/common/Button";
import { useNavigate } from "react-router-dom";
import { ROUTES } from "@constants/routes";
import Header from "../../components/Header";
import Table from "../../components/Table";
// import TableWithHeaderButton from "../../components/TableWithHeaderButton";

import Radio from "@components/form/Radio";
import Modal from "@components/common/Modal";
import Select from "@components/form/Select";
import TextField from "@components/form/TextField";
import { RATE_PROJECT_COMMISSION_ALLOCATION } from "@constants/product-proposal";
import { columns, columnsSimple } from "./data";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import { formatSelectOptions } from "@helpers/array";
import { useProductBenefitsManagementActions } from "@state/reducer/utilities-product-benefits";
import { useProductCommissionManagementActions } from "@state/reducer/utilities-product-commission";
import { useLocation } from "react-router-dom";
import httpClient from "@clients/httpClient";
import * as Yup from "yup";
import { showSuccess } from "@helpers/prompt";
import { removeProperties } from "@helpers/objects";
import UploadMasterListModal from "@modules/actuary/modals/UploadMasterListModal";
import DemographicModal from "@modules/actuary/modals/DemographicModa";
import { useModalController } from "@modules/sales/controller";
import { toast } from "react-toastify";
import { ProductCode, ProductName } from "@enums/product-code";
import BasisModal from "@modules/actuary/modals/BasisModal";
import { useProductActions } from "@state/reducer/products";
// import SelectModal from "@modules/actuary/modals/SelectModal";
import Select1 from "@components/form/Combo-box";
import { BiMinusCircle } from "react-icons/bi";
// import ConditionsList from "../template/condition";
import { removeTimestamps } from "@helpers/array";
import Wysiwyg from "@components/common/Wysiwyg";
import FormattedNumberInput from "@components/common/InputFormattedNumber";
import { useContestabilityActions } from "@state/reducer/contestability";
import { ProductStatus } from "@enums/product-status";

import { IUtilitiesProductBenefits } from "@interface/utilities.interface";
import { TOptions } from "@modules/sales/components/select";
import { LIFE_BENEFITS } from "@modules/sales/gyrt-quotations/components/constants";

type TRemarks = {
  AllIn: boolean;
  WaivedContestability: boolean;
  MembersAged65to69: boolean;
  Remarks: string;
};

const GYRT: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [benefitsModal, setBenefitsModal] = useState<boolean>(false);
  const [commissionLoadingModal, setCommissionLoadingModal] = useState<boolean>(false);
  const [ratingModal, setRatingModal] = useState<boolean>(false);
  const { getProductBenefits } = useProductBenefitsManagementActions();
  const { getProductCommission } = useProductCommissionManagementActions();
  const { getContestability } = useContestabilityActions();
  const benefits = useSelector((state: RootState) => state.utilitiesProductBenefits.productBenefits);
  const commissionTypes = useSelector((state: RootState) => state.utilitiesProductCommission.productCommission);
  const [_remarks, _setRemarks] = useState<TRemarks>({
    AllIn: false,
    WaivedContestability: false,
    MembersAged65to69: false,
    Remarks: "",
  });

  const [optionCount, setOptionCount] = useState<number>(0);
  const [options, setOptions] = useState<any[]>([]);
  const [optionGyrtBenefits, setOptionGyrtBenefits] = useState<any[]>([]); //option for BENEFITS
  const [optionQuotationPremiums, setOptionQuotationPremiums] = useState<any[]>([]); //option for NET and GROSS PREMIUM
  const [optionAerOptions, setOptionAerOptions] = useState<string>("");
  const [viewOption, setViewOption] = useState<number>(0); // view option for details
  const [condition, setCondition] = useState<string>("");

  const [_fileName, setFileName] = useState<string>("");
  const [demographicData, setDemographicData] = useState<any>(null);
  const [hasDemographics, setHasDemographics] = useState<boolean>(false);

  const mainProducts = useSelector((state: RootState) => state.products?.getMainProducts?.data?.data || []);

  const subProducts = useSelector((state: RootState) => state.products?.getSubProducts?.data?.data || []);

  const products = useSelector((state: RootState) => state.products?.getProducts?.data?.data || []);

  // Modal controllers
  const uploadMasterListModalController = useModalController();
  const demographicModalController = useModalController();
  const { getProducts } = useProductActions();

  const handleRadio = (value: string) => {
    formik.setFieldValue("rateProjectionCommissionAllocation", value);
  };

  // const handleSelectDetail = (detail: string | null) => {
  //   setSelectedDetail(detail ?? "");
  // };

  useEffect(() => {
    getProductBenefits({ filter: "" });
    getProductCommission({ filter: "" });
    getProducts({ params: { filter: "", statusFilter: ProductStatus.APPROVED, page: 1, pageSize: 9999 } });
    getContestability({ filter: ProductCode.GYRT });
  }, []);

  const formik = useFormik({
    initialValues: {
      selectedCoop: {},
      cooperativeId: 0,
      // branch: "",
      fileName: "",
      cooperative: "",
      previousProvider: "",
      contestability: "",
      premiumBudget: 0,
      totalNoOfMembers: 1464,
      udd: 0,
      lossRatio: "",
      rateBasedFrom: "",
      averageAge: 55,
      averageClaims: 0,
      benefits: [],
      totalNetPremium: "",
      totalGrossPremium: "",
      experienceAndDataAnalysis: "",
      rateProjectionCommissionAllocation: "",
      projection: {
        totalPremiumNetRate: 0,
        totalPremiumGrossRate: 0,
        numberOfClaims: 0,
        amountOfClaims: 0,
        claimsRatio: 0,
      },

      rating: [],
      commissionLoading: [],
      oldRating: [],
      oldCommissionLoading: [],
      productType: ProductCode.GYRT,
      condition: "",
    },
    validationSchema: Yup.object({
      contestability: Yup.number().required("Contestability is required"),
      cooperative: Yup.string().required("Cooperative is required"),
      totalNoOfMembers: Yup.number().required("Total number of members is required"),
      averageAge: Yup.number().required("Average age is required"),
      averageClaims: Yup.number().required("Average claims is required"),
      options: Yup.array().min(1, "At least one option is required"),
    }),
    onSubmit: () => {},
  });

  const handleBenefitsModal = () => {
    setBenefitsModal((prev) => !prev);
  };

  const formikBenefits = useFormik({
    initialValues: {
      benefitId: "",
      benefitName: "",
      coverage: 0,
      memberType: "PRINCIPAL",
      expectedClaims: 0,
      premiumDueToClimbsFromDataInput: 0,
    },
    validationSchema: Yup.object({
      benefitId: Yup.string().required("Benefit is required").notOneOf([""], "Benefit is required"),
      coverage: Yup.number().required("Coverage is required").min(1, "Coverage must be greater than 0"),
    }),
    onSubmit: (values) => {
      const selectedBenefit = benefits.find((benefit: any) => benefit.id.toString() === values.benefitId);
      if (selectedBenefit) {
        values.benefitName = selectedBenefit.benefitName;
        values.benefitId = selectedBenefit.id.toString();
      }

      formik.setFieldValue("benefits", [...formik.values.benefits, values]);
      formik.setFieldValue("rating", [...formik.values.benefits, values]);

      handleBenefitsModal();
    },
  });

  const handleCommissionLoadingModal = () => {
    setCommissionLoadingModal((prev) => !prev);
  };
  const handleRatingModal = () => {
    setRatingModal((prev) => !prev);
  };

  const formikRating = useFormik({
    initialValues: {
      rate: 0,
      benefitName: "",
      benefitId: "",
      defaultRate: "",
    },
    onSubmit: (values, { resetForm }) => {
      const selectedBenefit = benefits.find((item: any) => item.id.toString() === values.benefitId);
      if (selectedBenefit) {
        values.benefitName = selectedBenefit.benefitName;
        values.benefitId = selectedBenefit.id.toString();
      }

      const updatedRating = [...formik.values.rating, values];
      formik.setFieldValue("rating", updatedRating);
      handleRatingModal();
      resetForm();
    },
  });

  const formikCommissionLoading = useFormik({
    initialValues: {
      commissionTypeId: "",
      commissionTypeName: "",
      rate: 0,
    },
    onSubmit: (values, { resetForm }) => {
      const selectedCommission = commissionTypes.find((item: any) => item.id.toString() === values.commissionTypeId);
      if (selectedCommission) {
        values.commissionTypeName = selectedCommission.commissionName;
        values.commissionTypeId = selectedCommission.id.toString();
      }

      const updatedCommissionLoading = [...formik.values.commissionLoading, values];
      formik.setFieldValue("commissionLoading", updatedCommissionLoading);
      handleCommissionLoadingModal();
      resetForm();
    },
  });

  useEffect(() => {
    if (location?.state?.selectedQuotation) {
      formik.setFieldValue("selectedCoop", location.state.selectedQuotation.quotation.cooperative);
      formik.setFieldValue("cooperative", location.state.selectedQuotation.quotation.cooperative.coopName);
      // formik.setFieldValue("branch", location.state.selectedQuotation.quotation.branch);
      formik.setFieldValue("previousProvider", location.state.selectedQuotation.quotation.previousProvider);
      formik.setFieldValue("contestability", location.state.selectedQuotation.quotation.contestabilityPeriod.id);
      formik.setFieldValue("totalNoOfMembers", location.state.selectedQuotation.quotation.totalNumberOfMembers);
      formik.setFieldValue("premiumBudget", parseFloat(location.state?.selectedQuotation?.quotation?.gyrtQuotations?.premiumBudget).toFixed(2));
      formik.setFieldValue("averageAge", location.state.selectedQuotation.quotation.gyrtQuotations.averageAge);
      formik.setFieldValue("averageClaims", location.state.selectedQuotation.quotation.gyrtQuotations.averageClaims);
      formik.setFieldValue("udd", location.state.selectedQuotation.quotation.gyrtQuotations.udd);
      formik.setFieldValue("lossRatio", location.state.selectedQuotation.quotation.gyrtQuotations.lossRatio);
      formik.setFieldValue("rateBasedFrom", location.state.selectedQuotation.quotation.rateBasedFrom);
      formik.setFieldValue("rateProjectionCommissionAllocation", location.state.selectedQuotation.quotation.rateProjectionCommissionAllocation);
      formik.setFieldValue("experienceAndDataAnalysis", location.state.selectedQuotation.quotation.experienceAndDataAnalysis);
      formik.setFieldValue("fileName", location.state.selectedQuotation.quotation.fileName ?? "");

      setCondition(location.state.selectedQuotation.quotation.quotationCondition.condition);

      setOptionGyrtBenefits(removeTimestamps(location?.state?.selectedQuotation?.quotation?.gyrtBenefits) ?? []);
      setOptionQuotationPremiums(removeTimestamps(location?.state?.selectedQuotation?.quotation?.quotationPremium) ?? []);

      // formik.setFieldValue("rating", location?.state?.selectedQuotation?.quotation?.rating ?? []);
      //formik.setFieldValue("commissionLoading", location.state.selectedQuotation.quotation.quotationCommissionDistribution);

      basis.setFieldValue("fileName", location.state.selectedQuotation.quotation.fileName ?? "");
      setOptions(JSON.parse(location.state.selectedQuotation.options));
      setOptionCount(JSON.parse(location.state.selectedQuotation.options).length);
      setOptionAerOptions(location.state.selectedQuotation.options);
    }
    localStorage.setItem("productCode", ProductCode.GYRT);
  }, []);

  const handleCalculate = async () => {
    if (formik.values.benefits.length === 0) {
      toast.error("Please add some benefits..");
      return;
    }

    if (formik.values.commissionLoading.length === 0) {
      toast.error("Please add some commission..");
      return;
    }
    try {
      // Convert commissionLoading rates to percentage
      const commissionLoadingWithPercentage = formik.values.commissionLoading.map((item: any) => ({
        ...item,
        rate: item.rate,
      }));

      const response = await httpClient.post("/gyrt-calculation/getGyrtPremium", {
        contestabilityId: formik.values.contestability,
        basisForCalculation: formik.values.rateProjectionCommissionAllocation,
        ageType: location.state.selectedQuotation.quotation?.gyrtAge?.[0]?.ageType,
        averageAge: formik.values.averageAge,
        averageClaims: formik.values.averageClaims,
        groupSize: formik.values.totalNoOfMembers,
        gyrtBenefits: formik.values.benefits,
        rating: formik.values.rating,
        quotationCommissionDistribution: removeProperties(commissionLoadingWithPercentage),
        lossRatio: formik.values.lossRatio,
        // udd: formik.values.udd,
      });
      if (response) {
        formik.setFieldValue("totalNetPremium", response.data?.netPremium.toFixed(2));
        formik.setFieldValue("totalGrossPremium", response.data?.grossPremium.toFixed(2));
        formik.setFieldValue("benefits", response.data?.calculationResults);
        formik.setFieldValue("projection.totalPremiumNetRate", response.data?.netPremium);
        formik.setFieldValue("projection.totalPremiumGrossRate", response.data?.grossPremium);
        formik.setFieldValue("projection.numberOfClaims", response.data?.expectedNumberOfDeaths);
        formik.setFieldValue("projection.amountOfClaims", response.data?.expectedClaimsAmount);
        formik.setFieldValue("projection.claimsRatio", response.data?.lossRatio);
        toast.success("Premiums calculated successfully.");
      }
    } catch (error) {
      toast.error("An error occurred while calculating premiums. Please try again.");
    }
  };

  const hasDuplicateBenefit = (benefits: Array<{ benefitId: number; coverage: string }>, optionGyrtBenefits: Array<{ benefitId: number; coverage: string; option: string }>): boolean => {
    return benefits.some((benefit) => optionGyrtBenefits.some((gyrt) => gyrt.benefitId === benefit.benefitId && gyrt.coverage === benefit.coverage));
  };

  const handleSaveOption = () => {
    if (!formik.values.totalGrossPremium || !formik.values.totalNetPremium) {
      toast.error("Please calculate the premiums before saving the option.");
      return;
    }

    const result = hasDuplicateBenefit(formik.values.benefits, optionGyrtBenefits);

    if (result) {
      toast.error("Duplicate benefit found!");
      return;
    }

    const opt = optionCount + 1;
    if (options.length !== 0) {
      setOptionCount(opt);
      setOptions([...options, opt]);
    } else {
      setOptionCount((prev) => prev + 1);
      setOptions([...options, opt]);
    }
    const quotationPremiums = {
      grossPremium: formik.values.totalGrossPremium,
      netPremium: formik.values.totalNetPremium,
      option: opt,
    };

    setOptionQuotationPremiums([...optionQuotationPremiums, quotationPremiums]);
    setOptionGyrtBenefits([
      ...optionGyrtBenefits,
      ...formik.values.benefits.map((benefit: any) => ({
        ...benefit,
        option: opt,
      })),
    ]);

    if (location?.state?.selectedQuotation) {
      setOptionAerOptions(JSON.stringify([...JSON.parse(optionAerOptions), opt]));
    } else {
      setOptionAerOptions(JSON.stringify(Array.from({ length: optionCount }, (_, i) => i + 1)));
    }

    formik.setFieldValue("benefits", []);
    formik.setFieldValue("totalNetPremium", "");
    formik.setFieldValue("totalGrossPremium", "");
    toast.success("Option saved successfully.");
  };

  const handleDeleteOption = (option: number) => {
    const aerOptions = JSON.parse(optionAerOptions);
    const updatedOptions = aerOptions.filter((opt: any) => opt !== option);
    setOptionAerOptions(JSON.stringify(updatedOptions));

    setOptions(options.filter((opt) => opt !== option));
    setOptionGyrtBenefits(optionGyrtBenefits.filter((benefit) => benefit.option !== option));
    setOptionQuotationPremiums(optionQuotationPremiums.filter((premium) => premium.option !== option));
    // Remove the deleted option from aerOptions as well

    toast.success("Option deleted successfully.");
    setViewOption(0);
  };

  //on observation if dili mo work ang newly implemented functions, maybe used later
  // useEffect(() => {
  //   setOptionAerOptions(JSON.stringify(Array.from({ length: optionCount }, (_, i) => i + 1)));
  // }, [optionQuotationPremiums, optionGyrtBenefits]);

  const handleCreateAER = async () => {
    if (formik.values.selectedCoop === undefined || formik.values.selectedCoop === null) {
      toast.error("Please select a cooperative.");
      return;
    }

    if (!formik.values.averageAge) {
      toast.error("Please input a average age.");
      return;
    }

    if (!formik.values.totalNoOfMembers) {
      toast.error("Please input a total number of members.");
      return;
    }

    if (!formik.values.contestability) {
      toast.error("Please input constestability.");
      return;
    }

    if (!condition) {
      toast.error("Please input condition.");
      return;
    }

    const productId = localStorage.getItem("productId");

    const payload = {
      premiumBudget: formik.values.premiumBudget,
      averageAge: formik.values.averageAge,
      averageClaims: formik.values.averageClaims,
      status: "FOR_SIGNATORY",
      quotations: {
        productId: parseInt(String(productId ?? 0)),
        coopId: formik.values.cooperativeId ?? 0,
        previousProvider: formik.values.previousProvider ?? "",
        // branch: formik.values.branch ?? "",
        contestability: formik.values.contestability,
        totalNumberOfMembers: formik.values.totalNoOfMembers,
        parentId: localStorage.getItem("parentId") ?? 0,
        fileName: formik.values.fileName ?? "",
      },
      quotationCondition: {
        condition: condition ?? "",
      },
      options: {
        gyrtBenefits: optionGyrtBenefits,
        quotationPremiums: optionQuotationPremiums,
        aerOptions: optionAerOptions,
      },
      projection: [
        {
          totalPremiumNetRate: formik.values.projection.totalPremiumNetRate,
          totalPremiumGrossRate: formik.values.projection.totalPremiumGrossRate,
          numberOfClaims: formik.values.projection.numberOfClaims,
          amountOfClaims: formik.values.projection.amountOfClaims,
          claimsRatio: formik.values.projection.claimsRatio,
        },
      ],
    };

    if (payload.quotations.coopId === 0 || payload.quotations.coopId === undefined) {
      toast.error("Please select a cooperative.");
      return;
    }

    try {
      const response = await httpClient.post("/gyrt/createGyrtWithAer", payload);
      if (response) {
        showSuccess("Actuary Evaluation Report Created Successfully").then(() => navigate(ROUTES.ACTUARY.signatoryGYRT.key, { state: { data: response.data.quotations.actuaryEvaluationReport.id } }));
      }
    } catch (error) {
      toast.error("An error occurred while creating the Actuary Evaluation Report. Please try again.");
    }
  };

  const handleUpdateStatus = (values: any) => {
    formik.setFieldValue("cooperative", values.selectedCoop.coopName);
    formik.setFieldValue("selectedCoop", values.selectedCoop);
    // formik.setFieldValue("branch", values.branch);
    formik.setFieldValue("previousProvider", values.previousProvider);
    formik.setFieldValue("contestability", values.contestability);
    formik.setFieldValue("totalNoOfMembers", values.totalNoOfMembers);
    formik.setFieldValue("premiumBudget", values.premiumBudget);
    formik.setFieldValue("averageAge", values.averageAge);
    formik.setFieldValue("averageClaims", values.averageClaims);
    formik.setFieldValue("cooperativeId", values.selectedCoop.id);
    // formik.setFieldValue("udd", values.udd);
    formik.setFieldValue("lossRatio", values.lossRatio);
  };

  const handleSetFileName = (fileName: string) => {
    setFileName(fileName);
  };

  // Event handlers
  const handleUploadMasterList = () => {
    if (uploadMasterListModalController.isOpen) {
      uploadMasterListModalController.closeFn();
    } else {
      uploadMasterListModalController.openFn();
    }
  };

  const handleShowDemographic = () => {
    if (hasDemographics) {
      if (demographicModalController.isOpen) {
        handleSelectBasisModal();
      } else {
        demographicModalController.openFn();
      }
    } else {
      handleSelectBasisModal();
    }
  };

  useEffect(() => {
    if (formik.values.rateProjectionCommissionAllocation) {
      basis.setFieldValue("basisForCalculation", formik.values.rateProjectionCommissionAllocation);
    }
  }, [formik.values.rateProjectionCommissionAllocation]);

  useEffect(() => {
    basis.setFieldValue("cooperativeId", (formik.values.selectedCoop as any).id ?? 0);
  }, [formik.values.selectedCoop]);

  const basis = useFormik({
    initialValues: {
      productId: "",
      // subProductId: "",
      basisForCalculation: "",
      baseFrom: "",
      periodType: "",
      periodFrom: "01-01-2023",
      periodTo: "31-12-2024",
      ageBasis: "",
      age: [
        {
          ageFrom: 18,
          ageTo: 49,
        },
        {
          ageFrom: 50,
          ageTo: 65,
        },
        {
          ageFrom: 66,
          ageTo: 69,
        },
        {
          ageFrom: 70,
          ageTo: 999,
        },
      ],
      productType: ProductCode.GYRT,
      cooperativeId: formik.values.cooperativeId ?? 0,
      fileName: formik.values.fileName ?? "",
    },
    onSubmit: () => {
      handleSelectBasisModal();
    },
  });

  const [selectBasisModal, setSelectBasisModal] = useState<boolean>(false);
  const handleSelectBasisModal = () => {
    setSelectBasisModal((prev) => !prev);
  };

  const handleUpdateBasis = (data: any) => {
    basis.setValues(data);
  };

  const handleDeleteCommissionLoading = (index: number) => {
    const updatedCommissionLoading = formik.values.commissionLoading.filter((_, i) => i !== index);
    formik.setFieldValue("commissionLoading", updatedCommissionLoading);
  };

  const handleDeleteRating = (index: number) => {
    const updatedRating = formik.values.rating.filter((_, i) => i !== index);
    formik.setFieldValue("rating", updatedRating);
  };

  const handleGenerate = async () => {
    if (basis.values.cooperativeId === 0) {
      toast.error("Please select a cooperative first.");
      return;
    }

    if (basis.values.age.length === 0) {
      toast.error("Please add at least one age bracket.");
      return;
    }

    const ranges = basis.values.age
      .filter((item: any) => item.ageFrom !== undefined && item.ageTo !== undefined)
      .map((item: any, idx) => ({ idx, minimum: item.ageFrom, maximum: item.ageTo }))
      .sort((a, b) => a.minimum - b.minimum);

    // Check for minimum greater than maximum
    const invalidRange = ranges.find((range) => range.minimum > range.maximum);
    if (invalidRange) {
      toast.error(`In age bracket #${invalidRange.idx + 1}, minimum (${invalidRange.minimum}) should not be greater than maximum (${invalidRange.maximum}).`);
      return;
    }

    // Check for overlapping/duplicate ranges
    let duplicateRange: { minimum: number; maximum: number } | null = null;
    for (let i = 1; i < ranges.length; i++) {
      if (ranges[i].minimum <= ranges[i - 1].maximum) {
        duplicateRange = ranges[i];
        break;
      }
    }

    if (duplicateRange) {
      toast.error(`Age bracket ${duplicateRange.minimum}–${duplicateRange.maximum} has already been entered or overlaps with another. Duplicate or overlapping age ranges are not allowed.`);
    }

    const formattedDate = {
      periodFrom: basis.values.periodFrom.split("-").reverse().join("-"),
      periodTo: basis.values.periodTo.split("-").reverse().join("-"),
    };
    const { age, periodFrom, periodTo, ...rest } = basis.values;
    const data = {
      ...rest,
      ...formattedDate,
    };

    try {
      const response = await httpClient.post("/quotations/actuaryDemographicData", data);
      if (response) {
        showSuccess("Demographic data generated successfully.").then(() => {
          // handleSelectBasisModal();
          demographicModalController.openFn();
          setHasDemographics(true);
          setDemographicData(response.data);
        });
      }
    } catch (error) {
      toast.error("An error occurred while generating the basis. Please try again.");
    }
  };

  const handleDeleteBenefit = (rowIndex?: number) => {
    if (typeof rowIndex === "number") {
      const updatedBenefits = [...formik.values.benefits];
      updatedBenefits.splice(rowIndex, 1);
      formik.setFieldValue("benefits", updatedBenefits);
    }
  };

  const loadOptions = () => {
    // Set formik.benefits from location.state.selectedQuotation.quotation.gyrtBenefits if available
    if (location?.state?.selectedQuotation?.quotation?.gyrtBenefits) {
      // Only set benefits where option matches the current viewOption
      const filteredBenefits = removeTimestamps(location.state.selectedQuotation.quotation.gyrtBenefits).filter((item: any) => Number(item.option) === viewOption);
      formik.setFieldValue("benefits", filteredBenefits);
    }

    if (location?.state?.selectedQuotation?.quotation?.rating) {
      const filteredRating = removeTimestamps(location.state.selectedQuotation.quotation.rating).filter((item: any) => Number(item.option) === viewOption);
      formik.setFieldValue("rating", filteredRating);
    }

    if (location?.state?.selectedQuotation?.quotation?.quotationPremium) {
      // Only set quotation premiums where option matches the current viewOption
      const filteredPremiums = removeTimestamps(location.state.selectedQuotation.quotation.quotationPremium).filter((item: any) => Number(item.option) === viewOption);
      formik.setFieldValue("totalNetPremium", filteredPremiums[0]?.netPremium || "");
      formik.setFieldValue("totalGrossPremium", filteredPremiums[0]?.grossPremium || "");
    }

    // Map commissionLoadingData by option
    const commissionLoadingData = location?.state?.selectedQuotation?.quotation?.quotationCommissionDistribution;

    if (commissionLoadingData) {
      // Only set commission loading where option matches the current viewOption
      const filteredCommissionLoading = commissionLoadingData
        .map((item: any) => removeProperties(item, ["createdAt", "updatedAt"]))
        .filter((item: any) => Number(item.option) === viewOption || item.option === null);

      formik.setFieldValue("commissionLoading", filteredCommissionLoading);
      formik.setFieldValue("oldCommissionLoading", filteredCommissionLoading);
    }

    if (location?.state?.selectedQuotation?.quotation?.projection) {
      const filteredProjection = removeTimestamps(location.state.selectedQuotation.quotation.projection).find((item: any) => Number(item.option) === viewOption);
      if (filteredProjection) {
        formik.setFieldValue("projection", filteredProjection);
      }
    }
  };

  const lifeProductBenefitsData = useSelector((state: RootState) => state.utilitiesProductBenefits.lifeProductBenefits);
  const nonLifeProductBenefitsData = useSelector((state: RootState) => state.utilitiesProductBenefits.nonLifeProductBenefits);

  // Filter lifeProductBenefits to only include those whose id is in LIFE_BENEFITS
  const filteredLifeBenefitIds = useMemo<TOptions[]>(() => {
    if (!lifeProductBenefitsData) return [];
    return lifeProductBenefitsData
      .filter((benefit: IUtilitiesProductBenefits) => LIFE_BENEFITS.includes(benefit.id))
      .map((benefit: IUtilitiesProductBenefits) => ({
        text: benefit.benefitName,
        value: benefit.id.toString(),
      }));
  }, [lifeProductBenefitsData]);

  const filteredNonLifeBenefitIds = useMemo<TOptions[]>(() => {
    if (!nonLifeProductBenefitsData) return [];
    return nonLifeProductBenefitsData
      .filter((benefit: IUtilitiesProductBenefits) => !LIFE_BENEFITS.includes(benefit.id))
      .map((benefit: IUtilitiesProductBenefits) => ({
        text: benefit.benefitName,
        value: benefit.id.toString(),
        disabled: formik.values.benefits?.some((item: any) => item.benefitId === benefit.id), // Disable if already selected
      }));
  }, [nonLifeProductBenefitsData]);

  return (
    <>
      <UploadMasterListModal
        controller={uploadMasterListModalController}
        // TODO: Change for the actual logic!
        onUpload={(files: any[], _: string) => {
          setHasDemographics(files.length > 0);
          return Promise.resolve();
        }}
        data={formik.values}
        setFileName={handleSetFileName}
      />

      {selectBasisModal && (
        <BasisModal
          isOpen={selectBasisModal}
          onClose={handleSelectBasisModal}
          data={basis.values}
          onUpdate={handleUpdateBasis}
          onGenerate={handleGenerate}
          mainProducts={mainProducts}
          subProducts={subProducts}
          products={products}
        />
      )}
      {demographicData && (
        <DemographicModal controller={demographicModalController} basisValues={basis.values} hasDemographics={hasDemographics} demographicData={demographicData} productName={ProductName.GYRT} />
      )}
      <Button onClick={() => navigate(ROUTES.ACTUARY.GYRT.key)} classNames="bg-white border-0 flex items-center justify-center" outline variant="primary">
        <IoChevronBack />
        Back
      </Button>
      <Header initialValues={formik.initialValues} onSubmit={formik.handleSubmit} updateStatus={handleUpdateStatus} onClickUploadMasterlist={handleUploadMasterList} />
      <Modal isOpen={ratingModal} onClose={handleRatingModal} modalContainerClassName="max-w-xl" title="Add Rating" titleClass="text-3xl font-poppins-semibold">
        <FormikProvider value={formikRating}>
          <Form className="flex flex-col gap-2">
            <div className="flex flex-col gap-2">
              <div>Benefit</div>
              <Select
                required
                options={formatSelectOptions(benefits, "benefitName")}
                name="benefitId"
                value={formikRating.values.benefitId}
                onChange={formikRating.handleChange}
                onBlur={formikRating.handleBlur}
                placeholder="Select Benefit"
              />
            </div>

            <div className="flex flex-col gap-2">
              <div>Rate</div>
              <TextField required type="number" name="rate" value={formikRating.values.rate ?? ""} onChange={formikRating.handleChange} onBlur={formikRating.handleBlur} placeholder="Enter Rate" />
            </div>

            <div className="flex justify-end">
              {" "}
              <Button type="submit" classNames="w-24 text-sm py-3 rounded-md" variant="primary">
                Add
              </Button>
            </div>
          </Form>
        </FormikProvider>
      </Modal>
      <Modal isOpen={commissionLoadingModal} onClose={handleCommissionLoadingModal} modalContainerClassName="max-w-xl" title="Add Commission Loading" titleClass="text-3xl font-poppins-semibold">
        <FormikProvider value={formikCommissionLoading}>
          <Form className="flex flex-col gap-2">
            <div className="flex flex-col gap-2">
              <div>Name</div>
              <Select
                required
                options={formatSelectOptions(commissionTypes, "commissionName")}
                name="commissionTypeId"
                value={formikCommissionLoading.values.commissionTypeId}
                onChange={formikCommissionLoading.handleChange}
                onBlur={formikCommissionLoading.handleBlur}
                placeholder="Select Commission Loading"
              />
            </div>

            <div className="flex flex-col gap-2">
              <div>Rate</div>
              <TextField
                required
                name="rate"
                type="number"
                value={formikCommissionLoading.values.rate ?? 0}
                onChange={formikCommissionLoading.handleChange}
                onBlur={formikCommissionLoading.handleBlur}
                placeholder="Enter Value"
              />
            </div>

            <div className="flex justify-end">
              {" "}
              <Button type="submit" classNames="w-24 text-sm py-3 rounded-md" variant="primary">
                Add
              </Button>
            </div>
          </Form>
        </FormikProvider>
      </Modal>
      <Modal isOpen={benefitsModal} onClose={handleBenefitsModal} modalContainerClassName={"max-w-3xl"} title="Add Benefits">
        <FormikProvider value={formikBenefits}>
          <Form>
            <div className="flex flex-col gap-2 mb-4">
              <div>Benefits</div>
              <Select
                required
                options={formik.values.benefits.length === 0 ? filteredLifeBenefitIds : filteredNonLifeBenefitIds}
                name="benefitId"
                placeholder="Select Benefits"
                value={formikBenefits.values.benefitId}
                onChange={formikBenefits.handleChange}
                onBlur={formikBenefits.handleBlur}
                className="border-zinc-300"
              />
            </div>

            <div className="flex flex-col gap-2 mb-4">
              <div>Coverage</div>
              <FormattedNumberInput
                required
                number
                name="coverage"
                value={formikBenefits.values.coverage}
                onChange={(e) => formikBenefits.setFieldValue("coverage", e.target.value)}
                onBlur={formikBenefits.handleBlur}
                className="border-zinc-300 p-3 border-2 rounded-md"
                placeholder="Enter Coverage"
              />
            </div>
            <div className="flex flex-col gap-2 mb-4">
              <div>Expected Claims</div>
              <TextField
                name="expectedClaims"
                type="number"
                disabled
                value={formikBenefits.values.expectedClaims}
                onChange={formikBenefits.handleChange}
                onBlur={formikBenefits.handleBlur}
                className="border-zinc-300"
                placeholder="Calculated Input"
              />
            </div>
            <div className="flex flex-col gap-2  mb-4">
              <div>Premium Due to CLIMBS</div>
              <TextField
                name="premiumDueToCLIMBS"
                type="number"
                disabled
                value={formikBenefits.values.premiumDueToClimbsFromDataInput}
                onChange={formikBenefits.handleChange}
                onBlur={formikBenefits.handleBlur}
                className="border-zinc-300"
                placeholder="Calculated Input"
              />
            </div>
            <div className="flex justify-end">
              {" "}
              <Button type="submit" classNames="w-24 text-sm py-3 rounded-md" variant="primary">
                Add
              </Button>
            </div>
          </Form>
        </FormikProvider>
      </Modal>

      <div className="px-6 mb-40 h-max  ">
        {/* BENEFITS */}
        <div className="flex w-full h-full mb-10 mt-4 gap-4">
          <div className="w-3/4  h-full">
            <div className="w-full flex justify-between mb-2">
              <div className="flex justify-between">
                <div className="text-3xl text-zinc-700 font-poppins-semibold ">BENEFITS</div>
              </div>
              <div>
                <Button classNames="text-xs" variant="primary" outline onClick={handleBenefitsModal}>
                  Add
                </Button>
              </div>
            </div>{" "}
            <Table onClickedRow={handleDeleteBenefit} columns={columns} data={formik.values.benefits} showTotal tableMaxHeight="max-h-96" headerClassNames={"bg-primary text-white"} />
          </div>

          <div className="w-1/4 h-full flex flex-col pt-10">
            <div className="h-full ">
              <p className="text-zinc-400 text-xl font-poppins-medium mb-2">Total Net Premium</p>
              {formik.values.totalNetPremium && <p className="text-primary text-7xl font-poppins-semibold  ">{parseFloat(formik.values.totalNetPremium).toLocaleString() ?? ""}</p>}
            </div>
            <div className="h-full pt-20">
              <p className="text-zinc-400 text-xl font-poppins-medium mb-2">Total Gross Premium</p>
              {formik.values.totalGrossPremium && <p className="text-primary  text-7xl font-poppins-semibold  ">{parseFloat(formik.values.totalGrossPremium).toLocaleString() ?? ""}</p>}
            </div>
          </div>
        </div>

        {/* EXPERIENCE DATA AND ANALYSIS */}
        <div className="w-full p-4 border border-zinc-300 rounded-md items-center  h-max gap-2 flex">
          <div className="w-full flex items-center justify-between gap-2">
            <div className="text-sm text-primary flex items-center justify-center font-poppins-semibold">Demographic Data</div>

            <div className="flex gap-2">
              {hasDemographics && (
                <Button classNames="text-sm !text-sky-400 rounded-md border border-sky-400  " onClick={handleSelectBasisModal}>
                  View Another
                </Button>
              )}
              <Button classNames="text-sm text-yellow-500 bg-yellow-100 rounded-md  " onClick={handleShowDemographic}>
                Show Demographic
              </Button>
            </div>
          </div>
        </div>

        <div className="h-80  mt-8 ">
          <div className="text-2xl font-poppins-semibold">Rate Project & Commission Allocation</div>
          <div className="text-zinc-500 mt-1">Select the basis for calculation.</div>
          <div className="flex gap-4 text-xl mt-4">
            <Radio
              key="1"
              value={RATE_PROJECT_COMMISSION_ALLOCATION.NO_DATA.key}
              label="No Data"
              name="RateProjectionCommissionAllocation"
              checked={formik.values.rateProjectionCommissionAllocation === RATE_PROJECT_COMMISSION_ALLOCATION.NO_DATA.key}
              onChange={handleRadio}
              required
            />
            <Radio
              key="1"
              value={RATE_PROJECT_COMMISSION_ALLOCATION.MASTERLIST.key}
              label="Masterlist"
              name="RateProjectionCommissionAllocation"
              checked={formik.values.rateProjectionCommissionAllocation === RATE_PROJECT_COMMISSION_ALLOCATION.MASTERLIST.key}
              onChange={handleRadio}
              required
            />
            <Radio
              key={"2"}
              value={RATE_PROJECT_COMMISSION_ALLOCATION.COOP_EXISTING_PRODUCT.key}
              label={"Coop Existing Product"}
              name="RateProjectionCommissionAllocation"
              checked={formik.values.rateProjectionCommissionAllocation === RATE_PROJECT_COMMISSION_ALLOCATION.COOP_EXISTING_PRODUCT.key}
              onChange={handleRadio}
              required
            />
          </div>
          <div className="flex gap-4 my-6 ">
            <div className=" flex items-center ">Rate Based From</div>
            <div className=" w-80">
              <Select1
                className=""
                placeholder="Select Rate Based From"
                name="rateBasedFrom"
                value={formik.values.rateBasedFrom ?? ""}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                options={[
                  {
                    text: "CLIMBS Mortality Rate 1",
                    value: "CLIMBS Mortality Rate 1",
                  },
                ]}
                error={formik.touched.rateBasedFrom && !!formik.errors.rateBasedFrom}
              />
            </div>
          </div>
          <div className="h-96  w-full mt-2 flex gap-3">
            {/* MODIFIED FOR RATING */}
            <div className="w-[25%] h-full border border-zinc-300 rounded-md">
              <div className="bg-primary py-4  text-white font-poppins-semibold flex">
                <div className="w-1/3 pl-4">PROJECTION</div>
              </div>

              <div className="w-full flex items-center justify-center">
                <div className="w-1/3 p-2 pl-4 text-sm">Total Net Premium</div>
                <div className="w-2/3 p-2">
                  <FormattedNumberInput
                    name="projection.totalPremiumNetRate"
                    value={formik?.values?.projection?.totalPremiumNetRate}
                    onChange={(e) => formik.setFieldValue("projection.totalPremiumNetRate", e.target.value)}
                  />
                </div>
              </div>

              <div className="w-full flex items-center justify-center">
                <div className="w-1/3 p-2 pl-4 text-sm">Total Gross Premium</div>

                <div className="w-2/3 p-2">
                  <FormattedNumberInput
                    name="projection.totalPremiumGrossRate"
                    value={formik?.values?.projection?.totalPremiumGrossRate}
                    onChange={(e) => formik.setFieldValue("projection.totalPremiumGrossRate", e.target.value)}
                  />
                </div>
              </div>

              <div className="w-full flex items-center justify-center">
                <div className="w-1/3 p-2 pl-4 text-sm">No. of Claims</div>
                <div className="w-2/3 p-2">
                  <FormattedNumberInput
                    name="projection.numberOfClaims"
                    value={formik?.values?.projection?.numberOfClaims}
                    onChange={(e) => formik.setFieldValue("projection.numberOfClaims", e.target.value)}
                  />
                </div>
              </div>

              <div className="w-full flex items-center justify-center">
                <div className="w-1/3 p-2 pl-4 text-sm">Amount of Claims</div>
                <div className="w-2/3 p-2">
                  <FormattedNumberInput
                    name="projection.amountOfClaims"
                    value={formik.values?.projection?.amountOfClaims}
                    onChange={(e) => formik.setFieldValue("projection.amountOfClaims", e.target.value)}
                  />
                </div>
              </div>

              <div className="w-full flex items-center justify-center">
                <div className="w-1/3 p-2 pl-4 text-sm">Claims Ratio</div>
                <div className="w-2/3 p-2">
                  <FormattedNumberInput name="projection.claimsRatio" value={formik.values?.projection?.claimsRatio} onChange={(e) => formik.setFieldValue("projection.claimsRatio", e.target.value)} />
                </div>
              </div>
            </div>

            <div className="w-[37.5%] h-96 border border-zinc-300 rounded-md">
              <div className="bg-primary py-3 text-white font-poppins-semibold flex justify-between items-center px-4">
                <span>BENEFIT RATING</span>
                {/* <Button classNames="text-xs bg-white text-primary hover:bg-primary hover:text-white border" outline onClick={handleRatingModal}>
                  Add
                </Button> */}
              </div>
              <div className="max-h-72 overflow-y-auto ">
                <table className="w-full border-collapse">
                  <tbody>
                    {formik.values.rating.map((row: any, rowIndex) => (
                      <tr className={`w-full  first-line:cursor-pointer even:bg-gray-100  `} key={rowIndex}>
                        <td className="p-2 w-2/5 pl-4">{row.benefitName ? row?.benefitName : row?.benefit?.benefitName}</td>
                        <td className=" w-1/5 text-center">
                          {(formik.values.oldRating?.[rowIndex] as any)?.rate ? (
                            <span className="py-2 px-4 rounded-lg w-full bg-zinc-200 text-zinc-400">{parseFloat((formik.values.oldRating?.[rowIndex] as any).rate).toFixed(3)}</span>
                          ) : (
                            ""
                          )}
                        </td>
                        <td className="p-2 w-1/5 text-black">
                          <TextField type="number" name={`rating[${rowIndex}].rate`} value={(formik.values.rating[rowIndex] as any).rate} onChange={formik.handleChange} onBlur={formik.handleBlur} />
                        </td>
                        <td onClick={() => handleDeleteRating(rowIndex)} className=" w-1/5 mt-2  ">
                          <span className="flex  items-center justify-center h-full ">
                            {" "}
                            <BiMinusCircle size={30} className="text-red-500 text-center cursor-pointer " />
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="w-[37.5%] h-96  border border-zinc-300 rounded-md">
              <div className="bg-primary py-3 text-white font-poppins-semibold flex justify-between items-center px-4">
                <span>COMMISSION LOADING</span>
                <Button classNames="text-xs bg-white text-primary hover:bg-primary hover:text-white border" outline onClick={handleCommissionLoadingModal}>
                  Add
                </Button>
              </div>
              <div className="max-h-72 overflow-y-auto ">
                <table className="w-full border-collapse ">
                  <tbody>
                    {formik.values.commissionLoading.map((row: any, rowIndex) => (
                      <tr className={`w-full  first-line:cursor-pointer even:bg-gray-100`} key={rowIndex}>
                        <td className="p-2 w-2/5 pl-4">{row?.commissionTypeName ? row?.commissionTypeName : row?.commissionType?.commissionName}</td>
                        <td className=" w-1/5 text-center">
                          {(formik.values.oldCommissionLoading?.[rowIndex] as any)?.rate ? (
                            <span className="py-2 px-4 rounded-lg w-full bg-zinc-200 text-zinc-400">{parseFloat((formik.values.oldCommissionLoading?.[rowIndex] as any).rate).toFixed(3)}</span>
                          ) : (
                            ""
                          )}
                        </td>
                        <td className="p-2 text-black">
                          <TextField
                            type="number"
                            name={`commissionLoading[${rowIndex}].rate`}
                            value={(formik.values.commissionLoading[rowIndex] as any).rate}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            className="w-10"
                          />
                        </td>
                        <td onClick={() => handleDeleteCommissionLoading(rowIndex)} className=" w-1/5 mt-2  ">
                          <span className="flex  items-center justify-center h-full ">
                            {" "}
                            <BiMinusCircle size={30} className="text-red-500 text-center cursor-pointer " />
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>{" "}
          <div className="p-4 mt-4">
            <div className="font-poppins-semibold text-xl">CONDITIONS</div>
            <div className="w-full h-max rounded-md mt-2">
              <Wysiwyg stateValue={condition} className="min-h-60 h-max" onChange={(content) => setCondition(content)} />
            </div>
          </div>
          <div className="mt-8 border-t-8  border-primary ">
            <p className="text-xl mt-4 font-poppins-semibold">Options</p>
            <p className="text-zinc-400">Current options added are display below.</p>
            {options && (
              <div className="w-full flex gap-2 mt-2">
                {options.map((option: number) => (
                  <div
                    className={`text-sm font-poppins-medium p-2 px-4 cursor-pointer border ${viewOption === option ? "bg-primary text-white" : "border-primary text-primary"} rounded-md`}
                    key={option}
                    onClick={() => setViewOption(option)}
                  >
                    Option {option}
                  </div>
                ))}
              </div>
            )}
          </div>
          <div className="w-full mt-4">
            {viewOption != 0 && (
              <div className="w-full flex flex-col border-t pt-4 border-zinc-300 ">
                <div className="flex justify-between mb-4">
                  <div className="text-2xl font-poppins-semibold">Option {viewOption}</div>
                  <div className="flex gap-2">
                    <Button classNames="text-xs" variant="primary" outline onClick={loadOptions}>
                      Load Above
                    </Button>
                    <Button classNames="text-xs text-red-500" variant="danger" outline onClick={() => handleDeleteOption(viewOption)}>
                      Remove
                    </Button>
                    <div className="flex items-center text-xs text-zinc-400 justify-center cursor-pointer px-1 " onClick={() => setViewOption(0)}>
                      Hide
                    </div>
                  </div>
                </div>
                {optionQuotationPremiums
                  .filter((item: any) => item.option === viewOption)
                  .map((item: any, index: number) => (
                    <div key={index} className="flex gap-4 text-primary mb-6">
                      <div className=" w-1/2 flex flex-col">
                        <span className="text-lg  font-poppins-semibold"> Net Premium</span>
                        <span className="font-poppins-semibold text-5xl"> {parseFloat(item.netPremium).toLocaleString()}</span>
                      </div>
                      <div className=" w-1/2 flex flex-col">
                        <span className="text-lg  font-poppins-semibold"> Gross Premium</span>
                        <span className="font-poppins-semibold text-5xl"> {parseFloat(item.grossPremium).toLocaleString()}</span>
                      </div>
                    </div>
                  ))}
                <div className="w-full h-max border border-zinc-300 rounded-xl">
                  <Table columns={columnsSimple} data={optionGyrtBenefits.filter((benefit: any) => benefit.option === viewOption)} headerClassNames={"bg-primary text-white"} />
                </div>
              </div>
            )}
          </div>
          <div className="flex w-full justify-end gap-2 pb-20 mt-10 ">
            <Button classNames="text-zinc-500 w-40" onClick={handleSaveOption}>
              Save Option
            </Button>
            <Button classNames="bg-green-500 text-white w-40" onClick={handleCalculate}>
              Calculate
            </Button>
            <Button onClick={handleCreateAER} isSubmitting={options.length === 0} classNames={` text-white w-40 ${options.length === 0 ? "bg-primary opacity-75" : " bg-primary"}`}>
              Create AER
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};

export default GYRT;
