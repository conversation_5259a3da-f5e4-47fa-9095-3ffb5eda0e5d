import React, { useEffect, useState } from "react";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";

import { useNavigate } from "react-router-dom";
import { ROUTES } from "@constants/routes";

import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useSelectOptions } from "@hooks/useSelectOptions";
import { useDebouncedSearch } from "@hooks/useDebouncedSearch";
import { createDateChangeHandler, createSelectChangeHandler } from "@helpers/handlers";
import { useFetchWithParams } from "@hooks/useFetchWithParams";
import { getColumns } from "../components/table/column/return-column";
import { getColumns as getModalColumns } from "../components/table/column/completed-column";
import TableFilter from "../components/table/filter/TableFilter";

import { IPadAssignments } from "@interface/form-inventory.interface";
import TransmittalTable from "../components/table";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { FormStatus, RoleType } from "@enums/form-status";
import { useAreaActions } from "@state/reducer/utilities-areas";
import Button from "@components/common/Button";
import { FaPlus } from "react-icons/fa";
import CompletedPadsModal from "../components/modal/compeleted-pads-modal";
import { useIncomingReceivedFormActions } from "@state/reducer/form-inventory-incoming-received-form";

const ReturnPadTab: React.FC = () => {
  const navigate = useNavigate();

  // States
  // Main page
  const [resetCounter, setResetCounter] = useState(0);
  const [divisionFilter, setDivisionFilter] = useState<number>(0);
  const [type, setType] = useState<number>(0);
  const [area, setArea] = useState<number>(0);
  const [dateFrom, setDateFrom] = useState<string>("");
  const [dateTo, setDateTo] = useState<string>("");
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isFormOpen, setIsFormOpen] = useState<boolean>(false);
  const [localSelectedPads, setLocalSelectedPads] = useState<IPadAssignments[]>([]);

  // Modal
  const [resetCounterModal, setResetCounterModal] = useState(0);
  const [divisionFilterModal, setDivisionFilterModal] = useState<number>(0);
  const [typeModal, setTypeModal] = useState<number>(0);
  const [areaModal, setAreaModal] = useState<number>(0);

  const [dateFromModal, setDateFromModal] = useState<string>("");
  const [dateToModal, setDateToModal] = useState<string>("");
  const [pageModal, setPageModal] = useState(1);
  const [pageSizeModal, setPageSizeModal] = useState(10);

  // Global state
  const { data, loading } = useSelector((state: RootState) => state.formInventoryTransmittal.getCurrentUserFormTransmittalTrail);
  const { data: completedPads, loading: loadingModal } = useSelector((state: RootState) => state.formInventoryIncomingReceivedForms.getCompletedPads);

  // for Select options
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const types = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const areas = useSelector((state: RootState) => state.utilitiesAreas.areas);

  // Actions
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const { getCurrentUserOldestFormTransmittalTrail } = useTransmittalFormActions();
  const { getCompletedPads, bulkAddCompletedPads } = useIncomingReceivedFormActions();

  // Custom hooks
  const { value: searchText, handleChange: handleSearch, setValue: setSearchText } = useDebouncedSearch();
  const { value: searchTextModal, handleChange: handleSearchModal, setValue: setSearchTextModal } = useDebouncedSearch();

  const divisionOptions = useSelectOptions({
    data: divisions,
    firstOptionText: "Select Division",
    valueKey: "id",
    textKey: "divisionName",
  });

  const typeOptions = useSelectOptions({
    data: types,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: "formTypeName",
  });

  const areaOptions = useSelectOptions({
    data: areas,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: "areaName",
  });

  // Select handlers
  // Main page
  const handleDivisionChange = createSelectChangeHandler(setDivisionFilter);
  const handleTypeChange = createSelectChangeHandler(setType);
  const handleAreaChange = createSelectChangeHandler(setArea);

  // modal handlers
  const handleDivisionModalChange = createSelectChangeHandler(setDivisionFilterModal);
  const handleTypeModalChange = createSelectChangeHandler(setTypeModal);
  const handleAreaModalChange = createSelectChangeHandler(setAreaModal);

  // Date handlers
  // Main page handlers
  const handleDateFromChange = createDateChangeHandler(setDateFrom);
  const handleDateToChange = createDateChangeHandler(setDateTo);

  // modal date handlers
  const handleDateFromModalChange = createDateChangeHandler(setDateFromModal);
  const handleDateToModalChange = createDateChangeHandler(setDateToModal);

  const handleCheckboxChange = (row: IPadAssignments, isChecked: boolean) => {
    if (isChecked) {
      // Add to local state if not already there
      setLocalSelectedPads((prev) => {
        const exists = prev.some((p) => p.id === row.id);
        return exists ? prev : [...prev, row];
      });
    } else {
      // Remove from local state
      setLocalSelectedPads((prev) => prev.filter((p) => p.id !== row.id));
    }
  };

  const handleAddCompletedPads = () => {
    bulkAddCompletedPads(localSelectedPads);
    navigate(ROUTES.GAM.transmittalReturnedForm.key);
  };

  // Declare column action and add it
  // const getActionEvents = (Transmittal: IFormTransmittal): IActions<any>[] => {
  //   const actions: IActions<any>[] = [
  //     {
  //       name: "View",
  //       event: () => {
  //         navigate(ROUTES.GAM.viewForReceivingForm.parse(String(Transmittal.id || "")));
  //       },
  //       icon: GoVersions,
  //       color: "primary",
  //     },
  //   ];
  //   return actions;
  // };
  const columns = getColumns({ divisions, areas, formTypes: types });

  const modalColumns = getModalColumns({ isColumnModal: true, handleCheckboxChange, localSelectedPads });

  // Fetch padRequests with filters and pagination
  useFetchWithParams(
    getCurrentUserOldestFormTransmittalTrail,
    {
      page,
      pageSize,
      filter: searchText,
      divisionFilter,
      type: RoleType.GAM,
      statusFilter: FormStatus.RETURNED,
      eq: true,
    },
    [searchText, divisionFilter, type, page, pageSize, dateFrom, dateTo],
    false
  );

  useFetchWithParams(
    getCompletedPads,
    {
      page,
      pageSize,
      filter: searchText,
      divisionFilter,
    },
    [searchTextModal, divisionFilterModal, typeModal, pageModal, pageSizeModal, dateFromModal, dateToModal]
  );

  const handleToggleFormModal = () => {
    setIsFormOpen((prev) => !prev);
    setLocalSelectedPads([]);
  };

  const handleClearAll = () => {
    setSearchText("");
    setDivisionFilter(0);
    setType(0);
    setDateFrom("");
    setDateTo("");
    setResetCounter((prev) => prev + 1);
  };

  const handleModalClearAll = () => {
    setSearchTextModal("");
    setDivisionFilterModal(0);
    setTypeModal(0);
    setDateFromModal("");
    setDateToModal("");
    setResetCounterModal((prev) => prev + 1);
  };

  useEffect(() => {
    getDivisions({ filter: "" });
    getFormTypes({ filter: "" });
    getAreas({ filter: "" });
  }, []);

  return (
    <div className="p-4">
      <div className="mt-8">
        <div className="flex flex-row items-center justify-between">
          <TableFilter
            divisionFilter={divisionFilter}
            divisionOptions={divisionOptions}
            handleClearAll={handleClearAll}
            handleDivisionChange={handleDivisionChange}
            searchText={searchTextModal}
            handleSearch={handleSearchModal}
            handleTypeChange={handleTypeChange}
            areaFilter={area}
            areaOptions={areaOptions}
            handleAreaChange={handleAreaChange}
            resetCounter={resetCounter}
            type={type}
            typeOptions={typeOptions}
            dateFrom={dateFrom}
            handleDateFromChange={handleDateFromChange}
            dateTo={dateTo}
            handleDateToChange={handleDateToChange}
          />
          <Button variant="primary" classNames="flex items-center gap-2" onClick={handleToggleFormModal}>
            <FaPlus />
            Add New
          </Button>
        </div>
        {/* Table Section */}
        <TransmittalTable
          data={data || []}
          columns={columns}
          loading={loading}
          totalCount={data?.length || [].length}
          onChangeRowsPerPage={(newPageSize, newPage) => {
            setPageSize(newPageSize);
            setPage(newPage);
          }}
          onPaginate={(newPage) => setPage(newPage)}
        />
        {/* Completed Modal */}
        <CompletedPadsModal
          handleToggleFormModal={handleToggleFormModal}
          isFormOpen={isFormOpen}
          handleAddCompletedPads={handleAddCompletedPads}
          divisionFilter={divisionFilterModal}
          divisionOptions={divisionOptions}
          handleClearAll={handleModalClearAll}
          handleDivisionChange={handleDivisionModalChange}
          searchText={searchText}
          handleSearch={handleSearch}
          handleTypeChange={handleTypeModalChange}
          areaFilter={areaModal}
          areaOptions={areaOptions}
          handleAreaChange={handleAreaModalChange}
          resetCounter={resetCounterModal}
          type={typeModal}
          typeOptions={typeOptions}
          dateFrom={dateFromModal}
          handleDateFromChange={handleDateFromModalChange}
          dateTo={dateToModal}
          handleDateToChange={handleDateToModalChange}
          columns={modalColumns}
          data={completedPads?.data || []}
          totalCount={completedPads?.meta?.total || [].length}
          loading={loadingModal}
          onChangeRowsPerPage={(newPageSize, newPage) => {
            setPageSizeModal(newPageSize);
            setPageModal(newPage);
          }}
          onPaginate={(newPage) => setPage(newPage)}
        />
      </div>
    </div>
  );
};

export default ReturnPadTab;
