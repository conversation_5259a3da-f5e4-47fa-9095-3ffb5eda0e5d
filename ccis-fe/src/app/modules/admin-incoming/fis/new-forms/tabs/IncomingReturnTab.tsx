import React, { ChangeEvent, useEffect, useState } from "react";
import { useDebouncedCallback } from "use-debounce";
import Filter from "@components/common/Filter";
import TextField from "@components/form/TextField";
import Select from "@components/form/Select";
import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import Table from "@components/common/Table";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import ActionDropdown from "@components/common/ActionDropdown";
import Typography from "@components/common/Typography";
import { getTextStatusColor } from "@helpers/text";
import { IActions, IDefaultParams } from "@interface/common.interface";
import { TableColumn } from "react-data-table-component";
import { GoVersions } from "react-icons/go";
import { useNavigate } from "react-router-dom";
import { FaPrint } from "react-icons/fa";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { getDivisions } from "@state/reducer/form-inventory-utilities-divisions";
import { getFormTypes } from "@state/reducer/form-inventory-utilities-form-types";
import { getAreas } from "@state/reducer/utilities-areas";
import { ROUTES } from "@constants/routes";
import SeriesTransmittalTable from "../../components/CreateTransmittalForm";
import { FormStatus } from "@enums/form-status";
import { IFormTransmittal } from "@interface/form-inventory.interface";
import { printFile } from "@helpers/print";
import { formatSelectOptions, sortByKey } from "@helpers/array";

const IncomingReleasedTab: React.FC = () => {
  const [searchText, setSearchText] = useState<string | undefined>("");
  const [resetCounter, setResetCounter] = useState(0);
  const [statusFilter, setStatusFilter] = useState<string | undefined>("");
  const [dateFrom, setDateFrom] = useState<string>("");
  const [dateTo, setDateTo] = useState<string>("");
  const [isFormOpen, setIsFormOpen] = useState<boolean>(false);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const { getCurrentUserFormTransmittalTrail, getTransmittalTrailActivityLog, getTransmittalFormToClifsaPrint, clearTransmittalFormToClifsaPrint } = useTransmittalFormActions();

  const loading = useSelector((state: RootState) => state.formInventoryTransmittal.getCurrentUserFormTransmittalTrail?.loading);
  const { data: transmittalForms } = useSelector((state: RootState) => state.formInventoryTransmittal?.getCurrentUserFormTransmittalTrail);

  const userId = useSelector((state: RootState) => state?.auth?.user.data?.id);
  const filterDivision = "";
  const filterFormType = "";
  const filterArea = "";
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const formTypes = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);

  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const pdfBlob = useSelector((state: RootState) => state?.formInventoryTransmittal?.getTransmittalFormToClifsaPrint?.pdfUrl);
  const successPdfBlob = useSelector((state: RootState) => state?.formInventoryTransmittal?.getTransmittalFormToClifsaPrint?.success);
  const navigate = useNavigate();

  const uniqueStatusWithIds = [
    {
      status: FormStatus.RECEIVED,
    },
    {
      status: FormStatus.RELEASED,
    },
  ];
  const statusOptions = formatSelectOptions(uniqueStatusWithIds, "status");
  const handleSearch = useDebouncedCallback((event: ChangeEvent<HTMLInputElement>) => {
    // Extract the current input value from the event
    const value = event.target.value;
    setSearchText(value);
  }, 500);
  const handleSearch2 = (value: string) => {
    setSearchText(value);
  };
  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const handleClearAll = () => {
    setSearchText("");
    setStatusFilter("");
    setDateFrom("");
    setDateTo("");
    setResetCounter((prev) => prev + 1);
  };

  const handleStatusChange = (event: ChangeEvent<HTMLSelectElement>) => {
    const selectedText = event.target.options[event.target.selectedIndex].text;
    setStatusFilter(selectedText);
  };

  const handleDateFromChange = (event: ChangeEvent<HTMLInputElement>) => {
    setDateFrom(event.target.value);
  };

  const handleDateToChange = (event: ChangeEvent<HTMLInputElement>) => {
    setDateTo(event.target.value);
  };
  const handlePaginate = (pagination: number) => {
    setPage(pagination);
  };
  const handleRowsChange = (newPerPage: number, pagination: number) => {
    setPageSize(newPerPage);
    setPage(pagination);
  };
  const handleToggleFormModal = () => {
    setIsFormOpen((prev) => !prev);
  };
  const fetchForms = () => {
    const params: IDefaultParams = {
      // statusFilter: FormStatus.RETURNED,
      id: userId,
      page: page,
      pageSize: pageSize,
      dateFrom: dateFrom,
      dateTo: dateTo,
      useReturnedPads: true,
      statusFilter: FormStatus.APPROVED,
    } as IDefaultParams;
    getCurrentUserFormTransmittalTrail(params);
  };

  useEffect(() => {
    const timer = setTimeout(() => fetchForms(), 500);
    return () => clearTimeout(timer);
  }, [searchText, dateFrom, dateTo, statusFilter]);
  const sortedTransmittal = sortByKey(transmittalForms?.data, "id", "desc");

  useEffect(() => {
    getDivisions({ filter: filterDivision });
    getFormTypes({ filter: filterFormType });
    getAreas({ filter: filterArea });
    fetchForms();
  }, []);
  useEffect(() => {
    let printed = false;
    if (successPdfBlob) {
      handlePrint(pdfBlob);
      printed = true;
    }
    if (printed) {
      setTimeout(() => {
        clearTransmittalFormToClifsaPrint();
      }, 2000); // 2 seconds delay
    }
  }, [successPdfBlob]);
  const handlePrint = async (pdfUrl: Blob | string | null | undefined) => {
    await printFile(pdfUrl);
  };

  const getActionEvents = (Transmittal: any): IActions<any>[] => {
    const actions: IActions<any>[] = [
      {
        name: "View",
        event: () => {
          getTransmittalTrailActivityLog({ id: Transmittal.id });
          navigate(ROUTES.INCOMINGADMIN.viewIncomingAdminTransmittalTrail.parse(Transmittal.id));
        },
        icon: GoVersions,
        color: "primary",
      },
      {
        name: "Print",
        event: () => {
          getTransmittalFormToClifsaPrint({
            formTransmittalId: Transmittal.id,
          });
        },
        icon: FaPrint,
        color: "primary",
      },
    ];
    return actions;
  };

  const columns: TableColumn<IFormTransmittal>[] = [
    {
      name: "No.",
      cell: (row) => row.id,
      width: "80px",
      ...commonSetting,
    },
    {
      name: "Transmittal No.",
      cell: (row) => row.transmittalNumber,
      ...commonSetting,
    },
    {
      name: "Division",
      cell: (row) => {
        const divisionsList = Array.from(
          new Set(
            row.returnedPads
              ?.map((assignment: { form?: { divisionId?: number } }) => {
                const division = divisions.find((division) => division.id === assignment.form?.divisionId);
                return division ? division.divisionName : null;
              })
              .filter(Boolean) // Remove null values
          )
        );
        return divisionsList.length > 0 ? divisionsList.join(", ") : "N/A";
      },
      ...commonSetting,
    },
    {
      name: "Type",
      cell: (row) => {
        const uniqueFormTypes = Array.from(
          new Set(
            row.returnedPads
              ?.map((assignment: { form?: { formTypeId?: number } }) => {
                const formType = formTypes.find((type) => type.id === assignment.form?.formTypeId);
                return formType ? formType.formTypeName : null;
              })
              .filter(Boolean) // Remove null values
          )
        );
        return uniqueFormTypes.length > 0 ? uniqueFormTypes.join(", ") : "N/A";
      },
      ...commonSetting,
    },
    {
      name: "Released Area",
      cell: (row) => {
        const releasedArea = area.find((area) => area.id === row.returnedPads?.[0]?.form?.areaId);
        return releasedArea ? releasedArea.areaName : "N/A";
      },
      ...commonSetting,
    },
    {
      name: "Series from",
      cell: (row) => row.returnedPads?.[0]?.seriesFrom || 0,

      ...commonSetting,
    },
    {
      name: "Series to",

      cell: (row) => row.returnedPads?.[row.returnedPads.length - 1]?.seriesTo || 0,

      ...commonSetting,
    },
    {
      name: "# of Pads",
      cell: (row) => row.returnedPads?.length || "N/A",
      width: "120px",
      ...commonSetting,
    },
    {
      name: "Status",
      cell: (row) => {
        const trail = row?.formTransmittalTrails?.find((item: any) => item.createdBy?.id === userId);

        // Convert receivedAt to boolean - true if not null, false if null
        const isReceived = trail ? trail.receivedAt !== null : false;
        const status = trail ? trail.status : "No Status";

        let displayStatus = status;

        if (status === FormStatus.RETURNED && !isReceived) {
          displayStatus = FormStatus.NOT_YET_RECEIVED;
        } else if (status === FormStatus.RECEIVED || isReceived) {
          displayStatus = FormStatus.RECEIVED;
        } else {
          displayStatus = status;
        }

        return (
          <Typography className={displayStatus === FormStatus.RECEIVED ? getTextStatusColor(FormStatus.RECEIVED) : getTextStatusColor(FormStatus.NOT_YET_RECEIVED)}>
            {displayStatus
              ?.replace(/_/g, " ")
              .toLowerCase()
              .replace(/\b\w/g, (char) => char.toUpperCase())}
          </Typography>
        );
      },
    },
    {
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />,
    },
  ];

  return (
    <div className="p-4">
      <div className="text-xl font-semibold uppercase mt-4">Transmittal Lists</div>
      <div className="mt-8">
        <div className="flex flex-row justify-between">
          <Filter search={searchText} onChange={handleSearch}>
            <div className="flex justify-end">
              <button className="text-primary text-xs btn-sm" type="button" onClick={handleClearAll}>
                Clear All
              </button>
            </div>
            <div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <div className="text-xs">Date From</div>
                  <TextField className="" type="date" size="sm" value={dateFrom} onChange={handleDateFromChange} />
                </div>
                <div>
                  <div className="text-xs">Date To</div>
                  <TextField className="" type="date" size="sm" value={dateTo} onChange={handleDateToChange} />
                </div>
              </div>
              <div className="text-xs">Status</div>
              <Select key={`status-${resetCounter}`} size="sm" placeholder="Status" options={statusOptions} value={statusFilter} onChange={handleStatusChange} />
            </div>
          </Filter>

          <div className="flex justify-end gap-2">
            {/* for future use */}
            {/* <Button classNames="btn btn-sm">
              <FaPrint className="text-slate-400" />
            </Button> */}
            <Button classNames="text-primary text-xs btn-primary btn btn-sm" onClick={handleToggleFormModal}>
              + Add New
            </Button>
          </div>
        </div>
        <Table
          className="!min-h-[100%] h-[300px] mt-4 border-[1px] border-zinc-300"
          columns={columns}
          data={sortedTransmittal}
          loading={loading}
          onSearch={handleSearch2}
          multiSelect={false}
          paginationTotalRows={transmittalForms?.meta?.total}
          onPaginate={handlePaginate}
          onChangeRowsPerPage={handleRowsChange}
        />
        <Modal
          title={"Add New Transmittal"}
          modalContainerClassName="max-w-3xl mx-auto"
          titleClass="text-primary text-lg uppercase"
          isOpen={isFormOpen}
          onClose={handleToggleFormModal}
          className="w-full"
        >
          <Filter />
          <SeriesTransmittalTable statusFilter={statusFilter} searchText={searchText} handleToggleFormModal={handleToggleFormModal} />
        </Modal>
      </div>
    </div>
  );
};

export default IncomingReleasedTab;
