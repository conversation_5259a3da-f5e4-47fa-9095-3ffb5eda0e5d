import { TableColumn } from "react-data-table-component";
import Typography from "@components/common/Typography";
import ActionDropdown from "@components/common/ActionDropdown";
import { IActions } from "@interface/common.interface";
import { IPadAssignments } from "@interface/form-inventory.interface";
import CheckBox from "@components/form/CheckBox";

type GetActionEventsFn = (row: IPadAssignments) => IActions<any>[];

type GetColumnsParams = {
  isColumnModal?: boolean;
  handleCheckboxChange?: (row: IPadAssignments, isChecked: boolean) => void;
  getActionEvents?: GetActionEventsFn;
  localSelectedPads?: IPadAssignments[];
};

export const getColumns = ({ isColumnModal, handleCheckboxChange, getActionEvents, localSelectedPads }: GetColumnsParams): TableColumn<IPadAssignments>[] => {
  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const columns: TableColumn<IPadAssignments>[] = [
    {
      name: "No.",
      cell: (row) => row.id,
      width: "80px",
      ...commonSetting,
    },
    {
      name: "Transmittal No.",
      cell: (row) => row.transmittalNumber || "N/A",
      ...commonSetting,
    },
    {
      name: "Division",
      cell: () => {
        return "N/A";
      },
      ...commonSetting,
    },
    {
      name: "Series From",
      cell: (row) => row.seriesFrom || "N/A",
      width: "120px",
      ...commonSetting,
    },
    {
      name: "Series To",
      cell: (row) => row.seriesTo || "N/A",
      width: "120px",
      ...commonSetting,
    },
  ];

  if (isColumnModal && handleCheckboxChange) {
    columns.push({
      name: "",
      width: "80px",
      cell: (row) => <CheckBox checked={!!localSelectedPads?.some((p) => p.id === row.id)} onChange={(e) => handleCheckboxChange(row, e.target.checked)} />,
    });
  }

  if (getActionEvents) {
    columns.push({
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />,
    });
  }

  return columns;
};
