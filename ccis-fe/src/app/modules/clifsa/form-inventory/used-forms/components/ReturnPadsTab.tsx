import React, { ChangeEvent, useEffect, useState } from "react";
import { useDebouncedCallback } from "use-debounce";
import Filter from "@components/common/Filter";
import TextField from "@components/form/TextField";
import Select from "@components/form/Select";
import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import Table from "@components/common/Table";
import { RootState } from "@state/reducer";
import { useSelector } from "react-redux";
import ActionDropdown from "@components/common/ActionDropdown";
import Typography from "@components/common/Typography";
import { getTextStatusColor } from "@helpers/text";
import { IActions, IDefaultParams } from "@interface/common.interface";
import { TableColumn } from "react-data-table-component";
import { GoVersions } from "react-icons/go";
import { useNavigate } from "react-router-dom";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { getDivisions } from "@state/reducer/form-inventory-utilities-divisions";
import { getFormTypes } from "@state/reducer/form-inventory-utilities-form-types";
import { getAreas } from "@state/reducer/utilities-areas";
import { ROUTES } from "@constants/routes";

import { IFormTransmittal } from "@interface/form-inventory.interface";
import SeriesTransmittalTable from "./Forms/CreateTransmittalForm";
import { FormStatus } from "@enums/form-status";
import { findItem } from "@helpers/array";

const ReturnPadsTab: React.FC = () => {
  const [searchText, setSearchText] = useState<string | undefined>("");
  const [dateFrom, setDateFrom] = useState<string>("");
  const [dateTo, setDateTo] = useState<string>("");
  const [isFormOpen, setIsFormOpen] = useState<boolean>(false);
  const { getCurrentUserFormTransmittalTrail } = useTransmittalFormActions();
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [resetCounter, setResetCounter] = useState(0);
  const [divisionFilter, setDivisionFilter] = useState<number | undefined>();
  const userId = useSelector((state: RootState) => state?.auth?.user.data?.id);
  const divisionOptions = [
    { value: 0, text: "Select Division" },
    ...useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions).map((value) => {
      return { value: value.id, text: value.divisionName };
    }),
  ];
  const loading = useSelector((state: RootState) => state.formInventoryTransmittal.getCurrentUserFormTransmittalTrail.loading);
  const { data: forms } = useSelector((state: RootState) => state.formInventoryTransmittal.getCurrentUserFormTransmittalTrail);
  const filterDivision = "";
  const filterFormType = "";
  const filterArea = "";
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const formTypes = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const area = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const navigate = useNavigate();
  const handleSearch = useDebouncedCallback((event: ChangeEvent<HTMLInputElement>) => {
    // Extract the current input value from the event
    const value = event.target.value;
    setSearchText(value);
  }, 500);

  const handleRowsChange = (newPerPage: number, pagination: number) => {
    setPageSize(newPerPage);
    setPage(pagination);
  };

  const handlePaginate = (page: number) => {
    setPage(page);
  };

  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const handleClearAll = () => {
    setSearchText("");
    setDivisionFilter(0);
    setDateFrom("");
    setDateTo("");
    setResetCounter((prev) => prev + 1);
  };

  const handleDivisionChange = (event: ChangeEvent<HTMLSelectElement>) => {
    const value = parseInt(event.target.value);
    setDivisionFilter(value);
  };

  const handleDateFromChange = (event: ChangeEvent<HTMLInputElement>) => {
    setDateFrom(event.target.value);
  };

  const handleDateToChange = (event: ChangeEvent<HTMLInputElement>) => {
    setDateTo(event.target.value);
  };

  const handleToggleFormModal = () => {
    setIsFormOpen((prev) => !prev);
  };

  const getActionEvents = (Transmittal: any): IActions<any>[] => {
    const actions: IActions<any>[] = [
      {
        name: "View",
        event: () => {
          navigate(ROUTES.CLIFSAADMIN.viewReturnedForm.parse(Transmittal.id));
        },
        icon: GoVersions,
        color: "primary",
      },
    ];
    return actions;
  };

  // Get data with RELEASED status and released by head cashier
  const columns: TableColumn<IFormTransmittal>[] = [
    {
      name: "No.",
      cell: (row) => row.id,
      width: "80px",
      ...commonSetting,
    },
    {
      name: "Transmittal No.",
      cell: (row) => row.transmittalNumber,
      ...commonSetting,
    },
    {
      name: "ATP Number",
      cell: (row) => row.returnedPads?.[0]?.form?.atpNumber || "N/A",
      ...commonSetting,
    },
    {
      name: "Division",
      cell: (row) => {
        const divisionsList = Array.from(
          new Set(
            row.returnedPads
              ?.map((assignment: { form?: { divisionId?: number } }) => {
                const division = divisions.find((division) => division.id === assignment.form?.divisionId);
                return division ? division.divisionName : null;
              })
              .filter(Boolean) // Remove null values
          )
        );
        return divisionsList.length > 0 ? divisionsList.join(", ") : "N/A";
      },
      ...commonSetting,
    },
    {
      name: "Type",
      cell: (row) => {
        const uniqueFormTypes = Array.from(
          new Set(
            row.returnedPads
              ?.map((assignment: { form?: { formTypeId?: number } }) => {
                const formType = formTypes.find((type) => type.id === assignment.form?.formTypeId);
                return formType ? formType.formTypeCode : null;
              })
              .filter(Boolean) // Remove null values
          )
        );
        return uniqueFormTypes.length > 0 ? uniqueFormTypes.join(", ") : "N/A";
      },
      ...commonSetting,
    },
    {
      name: "Released Area",
      cell: (row) => <div>{String(findItem(area, "id", Number(row?.releasedAreaId), "areaName") || "N/A")}</div>,
      ...commonSetting,
    },
    {
      name: "No. of Pads",
      cell: (row) => row.returnedPads?.length || "N/A",
      width: "120px",
      ...commonSetting,
    },
    {
      name: "Status",
      cell: (row) => {
        const trail = row?.formTransmittalTrails?.find((item: any) => item.createdBy?.id === userId);

        // Convert receivedAt to boolean - true if not null, false if null
        const isReceived = trail ? trail.receivedAt !== null : false;
        const status = trail ? trail.status : "No Status";

        let displayStatus = status;

        if (status === FormStatus.RETURNED && !isReceived) {
          displayStatus = FormStatus.NOT_YET_RECEIVED;
        } else if (status === FormStatus.RECEIVED || isReceived) {
          displayStatus = FormStatus.RECEIVED;
        } else {
          displayStatus = status;
        }

        return (
          <Typography className={displayStatus === FormStatus.RECEIVED ? getTextStatusColor(FormStatus.RECEIVED) : getTextStatusColor(FormStatus.NOT_YET_RECEIVED)}>
            {displayStatus
              ?.replace(/_/g, " ")
              .toLowerCase()
              .replace(/\b\w/g, (char) => char.toUpperCase())}
          </Typography>
        );
      },
    },
    {
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />,
    },
  ];
  const sortedTransmittal = forms?.data?.slice().sort((a: IFormTransmittal, b: IFormTransmittal) => {
    return Number(b.id) - Number(a.id); // Explicitly convert to number
  });

  const fetchForms = () => {
    const payload = {
      filter: searchText,
      // type
      dateFrom: dateFrom,
      dateTo: dateTo,
      statusFilter: FormStatus.APPROVED,
      id: userId,
      useReturnedPads: true,
    } as IDefaultParams;
    getCurrentUserFormTransmittalTrail(payload);
  };

  useEffect(() => {
    getDivisions({ filter: filterDivision });
    getFormTypes({ filter: filterFormType });
    getAreas({ filter: filterArea });
    fetchForms();
  }, [searchText, page, pageSize, dateFrom, dateTo]);

  return (
    <div className="p-4">
      <div className="text-xl font-semibold uppercase mt-4 tracking-wider text-[#042781]">Return Pads List</div>
      <div className="mt-8">
        <div className="flex flex-row justify-between">
          <Filter search={searchText} onChange={handleSearch}>
            <div className="flex justify-end">
              <button className="text-primary text-xs btn-sm" type="button" onClick={handleClearAll}>
                Clear All
              </button>
            </div>
            <div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <div className="text-xs">Date From</div>
                  <TextField type="date" size="sm" value={dateFrom} onChange={handleDateFromChange} />
                </div>
                <div>
                  <div className="text-xs">Date To</div>
                  <TextField type="date" size="sm" value={dateTo} onChange={handleDateToChange} />
                </div>
              </div>
            </div>
          </Filter>

          <div className="flex justify-end gap-2">
            <Button classNames="text-primary text-xs btn-primary btn btn-sm" onClick={handleToggleFormModal}>
              + Add New
            </Button>
          </div>
        </div>
        <Table
          className="!min-h-[500px] mt-4 border-[1px] border-zinc-300"
          columns={columns}
          paginationServer={true}
          paginationTotalRows={forms?.meta?.total}
          data={sortedTransmittal}
          onChangeRowsPerPage={handleRowsChange}
          onPaginate={handlePaginate}
          loading={loading}
          searchable={false}
          multiSelect={false}
        />
        <Modal
          title={"Add New Transmittal"}
          modalContainerClassName="max-w-3xl mx-auto"
          titleClass="text-primary text-lg uppercase"
          isOpen={isFormOpen}
          onClose={handleToggleFormModal}
          className="w-full"
        >
          <div className="flex justify-start">
            <Filter search={searchText} onChange={handleSearch}>
              <div className="flex justify-end">
                <button className="text-primary text-xs btn-sm" type="button" onClick={handleClearAll}>
                  Clear All
                </button>
              </div>
              <div>
                <div className="text-xs">Division</div>
                <Select key={`division-${resetCounter}`} size="sm" options={divisionOptions} value={divisionFilter} onChange={handleDivisionChange} />
              </div>
            </Filter>
          </div>

          <SeriesTransmittalTable searchText={searchText} divisionFilter={divisionFilter} handleToggleFormModal={handleToggleFormModal} />
        </Modal>
      </div>
    </div>
  );
};

export default ReturnPadsTab;
