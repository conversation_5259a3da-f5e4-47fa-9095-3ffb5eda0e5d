import Typography from "@components/common/Typography";
import TableFilter from "../components/table/filter/TableFilter";
import { useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { useSelectOptions } from "@hooks/useSelectOptions";
import { createDateChangeHandler, createSelectChangeHandler } from "@helpers/handlers";
import { useDebouncedSearch } from "@hooks/useDebouncedSearch";
import { useFetchWithParams } from "@hooks/useFetchWithParams";
import Table from "@components/common/Table";
import { GoVersions } from "react-icons/go";
import { IActions } from "@interface/common.interface";
import { getColumns } from "../components/table/column/request-pads.column";
import { RequestPadStatus } from "@enums/request-pads";
import { usePadRequestActions } from "@state/reducer/gam-pad-request";
import { IGamPadRequest } from "@interface/gam-request-pads";
import { ROUTES } from "@constants/routes";
import { useNavigate } from "react-router-dom";

const RequestPads = () => {
  const navigate = useNavigate();
  //State
  const [resetCounter, setResetCounter] = useState(0);
  const [divisionFilter, setDivisionFilter] = useState<number>(0);
  const [areaFilter, setAreaFilter] = useState<number>(0);
  const [type, setType] = useState<number>(0);
  const [dateFrom, setDateFrom] = useState<string>("");
  const [dateTo, setDateTo] = useState<string>("");
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Global States
  const { gamPadRequesTable } = useSelector((state: RootState) => state.gamPadRequest);

  // for Select options
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const types = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const areas = useSelector((state: RootState) => state.utilitiesAreas.areas);

  // Actions
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const { getPadRequests } = usePadRequestActions();

  // custom hooks
  const divisionOptions = useSelectOptions({
    data: divisions,
    firstOptionText: "Select Division",
    valueKey: "id",
    textKey: "divisionName",
  });

  const typeOptions = useSelectOptions({
    data: types,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: "formTypeName",
  });

  const areaOptions = useSelectOptions({
    data: areas,
    firstOptionText: "Select Area",
    valueKey: "id",
    textKey: "areaName",
  });

  const { value: searchText, handleChange: handleSearch, setValue: setSearchText } = useDebouncedSearch();

  // Select handlers
  const handleDivisionChange = createSelectChangeHandler(setDivisionFilter);
  const handleTypeChange = createSelectChangeHandler(setType);
  const handleAreaChange = createSelectChangeHandler(setAreaFilter);

  // Date handlers
  const handleDateFromChange = createDateChangeHandler(setDateFrom);
  const handleDateToChange = createDateChangeHandler(setDateTo);

  // Get divisions, area and form type
  useFetchWithParams([getDivisions, getAreas, getFormTypes], { filter: "" }, [], false);

  // Fetch padRequests with filters and pagination
  useFetchWithParams(
    getPadRequests,
    {
      page,
      pageSize,
      filter: searchText,
      divisionFilter,
      areaFilter,
      formtypeFilter: type,
      dateFrom,
      dateTo,
      statusFilter: RequestPadStatus.APPROVED,
      parentFilter: RequestPadStatus.PENDING,
    },
    [searchText, divisionFilter, areaFilter, type, page, pageSize, dateFrom, dateTo] // triggers refetch
  );

  const handleClearAll = () => {
    setSearchText("");
    setDivisionFilter(0);
    setType(0);
    setAreaFilter(0);
    setResetCounter((prev) => prev + 1);
  };

  const getActionEvents = (row: IGamPadRequest): IActions<any>[] => {
    const actions: IActions<any>[] = [
      {
        name: "View",
        event: async () => {
          navigate(ROUTES.CHIEFCASHIER.viewRequestPadForm.parse(row.id.toString()));
        },
        icon: GoVersions,
        color: "primary",
      },
    ];
    return actions;
  };

  const columns = getColumns({ getActionEvents });

  return (
    <>
      <div className="my-4 py-4 border-b border-zinc-300 w-full text-zinc-500">
        Dashboard / <span className="text-primary font-poppins-semibold">Request Pads</span>
      </div>
      <Typography size="xl" className="text-primary my-16 flex flex-row font-poppins-semibold">
        REQUESTED PAD(s) LIST
      </Typography>
      <div className="flex items-center justify-between">
        <TableFilter
          divisionFilter={divisionFilter}
          divisionOptions={divisionOptions}
          handleClearAll={handleClearAll}
          handleDivisionChange={handleDivisionChange}
          searchText={searchText}
          handleSearch={handleSearch}
          handleTypeChange={handleTypeChange}
          resetCounter={resetCounter}
          type={type}
          typeOptions={typeOptions}
          areaFilter={areaFilter}
          areaOptions={areaOptions}
          handleAreaChange={handleAreaChange}
          dateFrom={dateFrom}
          handleDateFromChange={handleDateFromChange}
          dateTo={dateTo}
          handleDateToChange={handleDateToChange}
        />
      </div>
      <Table
        className="!min-h-[100%] h-[500px] border-[1px] border-zinc-300 mt-8"
        columns={columns}
        data={gamPadRequesTable?.tableData?.data?.data}
        loading={gamPadRequesTable?.tableData?.loading}
        paginationTotalRows={gamPadRequesTable?.tableData?.data?.meta?.total !== undefined ? gamPadRequesTable.tableData.data.meta.total : [].length}
        paginationServer={true}
        selectable={false}
        onChangeRowsPerPage={(newPageSize, newPage) => {
          setPageSize(newPageSize);
          setPage(newPage);
        }}
        onPaginate={(newPage) => setPage(newPage)}
      />
    </>
  );
};

export default RequestPads;
