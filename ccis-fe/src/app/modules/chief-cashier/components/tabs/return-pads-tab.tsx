import { FormStatus } from "@enums/form-status";
import { createDateChangeHandler, createSelectChangeHandler } from "@helpers/handlers";
import { useDebouncedSearch } from "@hooks/useDebouncedSearch";
import { useFetchWithParams } from "@hooks/useFetchWithParams";
import { useSelectOptions } from "@hooks/useSelectOptions";
import TableFilter from "@modules/treasury/request-pads/table/filter/TableFilter";
import { RootState } from "@state/reducer";
import { useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { useState } from "react";
import { useSelector } from "react-redux";
import { getColumns } from "../table/column/return-pads-column";
import Table from "@components/common/Table";

const ReturnPadTab = () => {
  // States
  const [resetCounter, setResetCounter] = useState(0);
  const [divisionFilter, setDivisionFilter] = useState<number>(0);
  const [type, setType] = useState<number>(0);
  const [area, setArea] = useState<number>(0);
  const [dateFrom, setDateFrom] = useState<string>("");
  const [dateTo, setDateTo] = useState<string>("");
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Global State

  // for Select options
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const types = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const areas = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const returnPads = useSelector((state: RootState) => state.formInventoryTransmittal.getReturnedPads);
  const user = useSelector((state: RootState) => state.auth.user.data);

  // Actions
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const { getReturnedPads } = useTransmittalFormActions();

  // Custom hooks
  const { value: searchText, handleChange: handleSearch, setValue: setSearchText } = useDebouncedSearch();

  const divisionOptions = useSelectOptions({
    data: divisions,
    firstOptionText: "Select Division",
    valueKey: "id",
    textKey: "divisionName",
  });

  const typeOptions = useSelectOptions({
    data: types,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: "formTypeName",
  });

  const areaOptions = useSelectOptions({
    data: areas,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: "areaName",
  });

  // Select handlers
  const handleDivisionChange = createSelectChangeHandler(setDivisionFilter);
  const handleTypeChange = createSelectChangeHandler(setType);
  const handleAreaChange = createSelectChangeHandler(setArea);

  // Date handlers
  const handleDateFromChange = createDateChangeHandler(setDateFrom);
  const handleDateToChange = createDateChangeHandler(setDateTo);

  useFetchWithParams(
    [getDivisions, getFormTypes, getAreas],
    {
      filter: "",
    },
    [],
    false
  );

  useFetchWithParams(
    getReturnedPads,
    {
      page,
      pageSize,
      filter: searchText,
      user: user.id,
      statusFilter: FormStatus.RECEIVED,
    },
    [searchText, type, page, pageSize, dateFrom, dateTo],
    false
  );

  const columns = getColumns({ divisions, areas, formTypes: types });

  const handleClearAll = () => {
    setSearchText("");
    setType(0);
    setDateFrom("");
    setDateTo("");
    setResetCounter((prev) => prev + 1);
  };

  return (
    <div className="p-4">
      <div className="mt-8">
        <div className="flex flex-row items-center justify-between">
          <TableFilter
            handleClearAll={handleClearAll}
            searchText={searchText}
            handleSearch={handleSearch}
            resetCounter={resetCounter}
            type={type}
            handleTypeChange={handleTypeChange}
            handleDivisionChange={handleDivisionChange}
            typeOptions={typeOptions}
            divisionFilter={divisionFilter}
            divisionOptions={divisionOptions}
            areaFilter={area}
            areaOptions={areaOptions}
            handleAreaChange={handleAreaChange}
            dateFrom={dateFrom}
            handleDateFromChange={handleDateFromChange}
            dateTo={dateTo}
            handleDateToChange={handleDateToChange}
          />
        </div>

        <Table
          className="!min-h-[100%] h-[500px] border-[1px] border-zinc-300 mt-8"
          columns={columns}
          data={returnPads?.data || []}
          searchable={false}
          multiSelect={false}
          paginationTotalRows={returnPads?.data?.length || [].length}
          paginationServer={true}
          // loading={loading}
          onChangeRowsPerPage={(newPageSize, newPage) => {
            setPageSize(newPageSize);
            setPage(newPage);
          }}
          onPaginate={(newPage) => setPage(newPage)}
        />
      </div>
    </div>
  );
};

export default ReturnPadTab;
