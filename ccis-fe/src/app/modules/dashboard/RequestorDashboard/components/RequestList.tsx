//Necessary Imports
import React, { useRef, useState, ReactNode, useEffect } from "react";
import { MoreVertical } from "lucide-react";
import Avatar from "@components/common/Avatar";
import { RequestStatus } from "@enums/ticket-status";
import { capitalizeFirstLetterWords, getNewTextStatusColor } from "@helpers/text";
import Button from "@components/common/Button";
import { FaPlus } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { RootState } from "@state/store";
import Pagination from "@modules/admin/form-inventory-and-tracking/new-forms/incoming/components/pagination";

//To get the manager IDs
import { useManagerIdActions } from "@state/reducer/manager-id";
import { useGlobalSettingsManagementActions } from "@state/reducer/global-settings";

//For the tabs:
import Tabs from "@components/common/Tabs";

//For fetching the tables for all 3 different lists, all of which
import { useTicketActions } from "@state/reducer/departmental-ticketing";
//For the interface shell for the ticket
import { ITicket, ITicketAssigneeTemplate, FilterCriteria, ITicketDepartmentUsers, ITicketInfo } from "@interface/departmental-ticketing-interface";
//For using the data selector
import { useSelector } from "react-redux";

//For loaders and state of the ticket methods
import Loader from "@components/Loader";
import { getRolePath, getRolePathWithId } from "@helpers/navigatorHelper";
import { ITicketAssigneeRoleConstant } from "@interface/ticket-assignee-roles.interface";

//*For the Filter Button and related components
import FilterButton from "@components/common/FilterButton";
import TextField from "@components/form/TextField";
import Select from "@components/form/Select";

//For mobile devices
import useIsMobile from "@hooks/useIsMobile";
import { FaBars } from "react-icons/fa";

//For Tooltips
import Tooltip from "@components/common/Tooltip";
import { StatusBadge } from "./common/status-badge";
import { SYSTEM_SETTINGS_IDS } from "@constants/global-constant-value";
import { SharedRoutes } from "@enums/shared-routes";

//Function to render the status based on the result of the string:
const statusTag = (status: string | undefined): RequestStatus => {
  switch (status?.toUpperCase()) {
    case "PENDING":
      return RequestStatus.Pending;
    case "APPROVED":
      return RequestStatus.Approved;
    case "REJECTED":
      return RequestStatus.Rejected;
    case "FOR_APPROVAL":
      return RequestStatus.ForApproval;
    case "ASSIGNED":
      return RequestStatus.Assigned;
    case "OVERDUE":
      return RequestStatus.Overdue;
    case "IN_PROGRESS":
      return RequestStatus.InProgress;
    case "RESOLVED":
      return RequestStatus.Resolved;
    case "UNRESOLVED":
      return RequestStatus.Unresolved;
    default:
      return RequestStatus.Pending;
  }
};

//For the icons:
interface ListComponentProps {
  filterCriteria: FilterCriteria;
}

const RequestDataList: React.FC<ListComponentProps> = ({ filterCriteria }) => {
  const navigate = useNavigate();
  //To fetch the ticket table
  const tickets = useSelector((state: RootState) => state.departmentalTicketing?.getTicket?.data || []);
  //For all the other states
  const loading = useSelector((state: RootState) => state.departmentalTicketing?.getTicket?.loading);
  const error = useSelector((state: RootState) => state.departmentalTicketing?.getTicket?.error);
  const me = useSelector((state: RootState) => state.auth.user.data);
  const { getTicket } = useTicketActions();
  //This part is for the pagination of the request list
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10; // Number of items per page
  //Gets the total of pages needed
  const totalPages = tickets ? Math.ceil(tickets?.data?.length / itemsPerPage) : 0;
  //Check if the user is using a mobile device:
  const isMobile = useIsMobile();
  //Handles the page change
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };
  //To hold the data of the current page
  const paginatedTickets = tickets?.data?.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage) || [];
  //For the dropdown, dunno if we're going to use this, but just in case.
  const [openDropdownIndex, setOpenDropdownIndex] = useState<number | null>(null);
  const dropdownRef = useRef<HTMLDivElement | null>(null);

  //* Function to build filter parameters
  const buildFilterParams = () => {
    // Always start with the base condition for this tab
    const condition = `createdBy.id[eq]=${me?.id}${filterCriteria.departmentId ? `&toDepartmentId[eq]=${filterCriteria.departmentId}` : ""}${
      filterCriteria.status ? `&status[eq]=${filterCriteria.status}` : ""
    }${
      filterCriteria.fromDate && filterCriteria.toDate
        ? `&createdAt[gte]=${filterCriteria.fromDate.toISOString()}&createdAt[lte]=${filterCriteria.toDate.toISOString()}`
        : filterCriteria.fromDate
          ? `&createdAt[gte]=${filterCriteria.fromDate.toISOString()}`
          : filterCriteria.toDate
            ? `&createdAt[lte]=${filterCriteria.toDate.toISOString()}`
            : ""
    }`;

    return condition;
  };

  //Filter the results on load, refresh whenever we get to that point and if there's nothing
  useEffect(() => {
    //Create a quick function to just run getTicket and then set up the error and loading scripts
    getTicket({
      params: {
        relations: `ticketAssignees.user|requestType|createdBy|latestTicketAssignee.user`,
        condition: buildFilterParams(),
        pageSize: 9999,
      },
    });
  }, [filterCriteria]);

  //insert loading and error sign here:

  if (loading) {
    return (
      <div className="flex flex-1 w-full items-center justify-center">
        <Loader />
      </div>
    );
  } else if (error) {
    return (
      <div className="flex flex-1 w-full items-center justify-center">
        <div className="p-4 text-red-500 text-center">
          <p>Failed to load requests. Please try again.</p>
        </div>
      </div>
    );
  } else if (!tickets.data || tickets.data.length === 0) {
    return (
      <div className="flex flex-1 w-full items-center justify-center">
        <div className="p-4 text-gray-500 text-center">
          <p>No requests found.</p>
        </div>
      </div>
    );
  }

  //For handling cases when pictures exceed
  const renderAssigneeProfiles = (taskCreator: ITicketDepartmentUsers, currentAssignee: ITicketAssigneeTemplate) => {
    return (
      <>
        {/* For the Creator of the Ticket */}
        <Tooltip text={`Created By: ${taskCreator?.firstname} ${taskCreator?.lastname}`} position="top">
          <Avatar classNames="h-6 w-6" src={taskCreator.profilePicturePath}>
            {taskCreator?.firstname.at(0)?.toUpperCase()}
            {taskCreator?.lastname.at(0)?.toUpperCase()}
          </Avatar>
        </Tooltip>

        {/* For the Current Assignee */}
        <Tooltip text={`Current Assignee: ${currentAssignee.user?.firstname} ${currentAssignee.user?.lastname}`} position="top">
          <Avatar classNames="h-6 w-6" src={currentAssignee.user.profilePicturePath}>
            {currentAssignee.user?.firstname.at(0)?.toUpperCase()}
            {currentAssignee.user?.lastname.at(0)?.toUpperCase()}
          </Avatar>
        </Tooltip>
      </>
    );
  };

  const renderDeadlineDate = (ticket: ITicket) => {
    const deadline = (ticket.expectedCompletionDate || "") > (ticket.extensionDate || "") ? ticket.expectedCompletionDate : ticket.extensionDate;
    if (!deadline || isNaN(new Date(deadline).getTime())) {
      return (
        <Tooltip text="No Deadline Set" position="top">
          <div className="flex flex-col bg-red-800 text-white text-center items-center justify-center  rounded-full size-10">
            <div>
              <div className="text-[14px] leading-none">N/A</div>
            </div>
          </div>
        </Tooltip>
      );
    }

    return (
      <Tooltip text={`Deadline: ${new Date(deadline).toLocaleString("en-US", { month: "long" })} ${new Date(deadline).getDate()} ${new Date(deadline).getFullYear()}`} position="top">
        <div className="flex flex-col bg-red-800 text-white text-center items-center justify-center  rounded-full size-10">
          <div>
            <div className="text-[8px] leading-none">{new Date(deadline).toLocaleString("en-US", { month: "short" })}</div>
            <div className="text-[18px] leading-none">{new Date(deadline).getDate()}</div>
          </div>
        </div>
      </Tooltip>
    );
  };

  return (
    <>
      {/* This is where the list of requests will be */}
      <ul className="list-disc" style={{ minWidth: isMobile ? "pl-1" : "pl-5" }}>
        {paginatedTickets.map((ticket: ITicket, index: number) => (
          <div key={index} className="border-b border-slate-300 pb-1 last:border-b-0">
            <div className={`${isMobile ? `flex-col` : `flex`} items-center justify-between`}>
              {/* Display Request Title here */}
              <div className="flex justify-between items-center w-full mx-2">
                <div className="flex">
                  <Tooltip text={ticket.id} position="top" className="flex items-center justify-between text-left w-full">
                    <span className="text-md px-2 flex-shrink-0">{ticket.id}</span>
                    <span className="text-xs flex-1 text-center px-2">{ticket.requestType?.name}</span>
                    <StatusBadge status={ticket.priorityLevel as RequestStatus} className="text-xs px-2 flex-shrink-0" />
                  </Tooltip>
                </div>
                <div className="flex-shrink-0">
                  {/* Display the status of the request here */}
                  {(() => {
                    const IconComponent = getNewTextStatusColor(ticket.status || "").icon;

                    return (
                      <div className={getNewTextStatusColor(ticket.status || "").className}>
                        <IconComponent className="h-4 w-4" />
                        {capitalizeFirstLetterWords(statusTag(ticket.status), "_")}
                      </div>
                    );
                  })()}
                </div>
              </div>
              <div className={`flex items-center gap-2 space-x-2 ${isMobile && `w-full justify-between`}`}>
                {isMobile ? (
                  <div className="flex mr-2">
                    {renderAssigneeProfiles(ticket.createdBy as unknown as ITicketDepartmentUsers, ticket.latestTicketAssignee as unknown as ITicketAssigneeTemplate)}
                    {renderDeadlineDate(ticket)}
                  </div>
                ) : (
                  <>
                    {/* Display the profile pics of all assigned users */}
                    {renderAssigneeProfiles(ticket.createdBy as unknown as ITicketDepartmentUsers, ticket.latestTicketAssignee as unknown as ITicketAssigneeTemplate)}
                    {/* Display deadline beside the profiles: */}
                    {renderDeadlineDate(ticket)}
                  </>
                )}

                <div className="relative flex-shrink-0" ref={dropdownRef}>
                  <MoreVertical className="w-4 h-4 cursor-pointer text-gray-500" onClick={() => setOpenDropdownIndex(openDropdownIndex === index ? null : index)} />

                  {openDropdownIndex === index && (
                    <div className="absolute right-0 mt-2 w-28 bg-white shadow-md border border-slate-300 z-50 rounded-md">
                      <button
                        className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
                        onClick={() => {
                          navigate(getRolePathWithId(SharedRoutes.REQUEST_FORM, ticket.id?.toString() || ""));
                        }}
                      >
                        View
                      </button>
                    </div>
                  )}
                </div>
                {/* End of dropdown */}
              </div>
            </div>
          </div>
        ))}
      </ul>
      {/*End of list */}
      <Pagination currentPage={currentPage} totalPages={totalPages} onPageChange={handlePageChange} />
    </>
  );
};

const AssignedTicketDataList: React.FC<ListComponentProps> = ({ filterCriteria }) => {
  const navigate = useNavigate();
  //To fetch the ticket table
  const tickets = useSelector((state: RootState) => state.departmentalTicketing?.getTicketsInfo?.data || []);
  //For all the other states
  const loading = useSelector((state: RootState) => state.departmentalTicketing?.getTicketsInfo?.loading);
  const error = useSelector((state: RootState) => state.departmentalTicketing?.getTicketsInfo?.error);
  const { getTicketsInfo } = useTicketActions();
  //This part is for the pagination of the request list
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10; // Number of items per page
  //Gets the total of pages needed
  const totalPages = tickets ? Math.ceil(tickets?.data?.length / itemsPerPage) : 0;
  //Check if the user is using a mobile device:
  const isMobile = useIsMobile();
  //Handles the page change
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };
  //To hold the data of the current page
  const paginatedTickets = tickets?.data?.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage) || [];

  //For the dropdown, dunno if we're going to use this, but just in case.
  const [openDropdownIndex, setOpenDropdownIndex] = useState<number | null>(null);
  const dropdownRef = useRef<HTMLDivElement | null>(null);

  //* Function to build filter parameters
  const buildFilterParams = () => {
    const condition = `myTicketAssignees.user.role[eq]=${ITicketAssigneeRoleConstant.ticketAssignee}${filterCriteria.status ? `&status[eq]=${filterCriteria.status}` : ""}${
      filterCriteria.fromDate && filterCriteria.toDate
        ? `&createdAt[gte]=${filterCriteria.fromDate.toISOString()}&createdAt[lte]=${filterCriteria.toDate.toISOString()}`
        : filterCriteria.fromDate
          ? `&createdAt[gte]=${filterCriteria.fromDate.toISOString()}`
          : filterCriteria.toDate
            ? `&createdAt[lte]=${filterCriteria.toDate.toISOString()}`
            : ""
    }`;

    return condition;
  };

  //Filter the results on load, refresh whenever we get to that point and if there's nothing
  useEffect(() => {
    //Create a quick function to just run getTicket and then set up the error and loading scripts
    getTicketsInfo({
      params: {
        relations: "ticketAssignees.user|toDepartment|fromDepartment|assignedTo|requestType|ticketAssignees|createdBy|latestTicketAssignee.user|myTicketAssignees.user",
        condition: buildFilterParams(),
        pageSize: 9999,
      },
    });
  }, [filterCriteria]);

  //insert loading and error sign here:

  if (loading) {
    return (
      <div className="flex flex-1 w-full items-center justify-center">
        <Loader />
      </div>
    );
  } else if (error) {
    return (
      <div className="flex flex-1 w-full items-center justify-center">
        <div className="p-4 text-red-500 text-center">
          <p>Failed to load requests. Please try again.</p>
        </div>
      </div>
    );
  } else if (!tickets.data || tickets.data.length === 0) {
    return (
      <div className="flex flex-1 w-full items-center justify-center">
        <div className="p-4 text-gray-500 text-center">
          <p>No requests found.</p>
        </div>
      </div>
    );
  }

  //For handling cases when pictures exceed
  const renderAssigneeProfiles = (taskCreator: ITicketDepartmentUsers, currentAssignee: ITicketAssigneeTemplate) => {
    return (
      <>
        {/* For the Creator of the Ticket */}
        <Tooltip text={`Created By: ${taskCreator?.firstname} ${taskCreator?.lastname}`} position="top">
          <Avatar classNames="h-6 w-6" src={taskCreator.profilePicturePath}>
            {taskCreator?.firstname.at(0)?.toUpperCase()}
            {taskCreator?.lastname.at(0)?.toUpperCase()}
          </Avatar>
        </Tooltip>

        {/* For the Current Assignee */}
        <Tooltip text={`Current Assignee: ${currentAssignee.user?.firstname} ${currentAssignee.user?.lastname}`} position="top">
          <Avatar classNames="h-6 w-6" src={currentAssignee.user.profilePicturePath}>
            {currentAssignee.user?.firstname.at(0)?.toUpperCase()}
            {currentAssignee.user?.lastname.at(0)?.toUpperCase()}
          </Avatar>
        </Tooltip>
      </>
    );
  };

  const renderDeadlineDate = (ticket: ITicketInfo) => {
    const deadline = (ticket.expectedCompletionDate || "") > (ticket.extensionDate || "") ? ticket.expectedCompletionDate : ticket.extensionDate;
    if (!deadline || isNaN(new Date(deadline).getTime())) {
      return (
        <Tooltip text="No Deadline Set" position="top">
          <div className="flex flex-col bg-red-800 text-white text-center items-center justify-center  rounded-full size-10">
            <div>
              <div className="text-[14px] leading-none">N/A</div>
            </div>
          </div>
        </Tooltip>
      );
    }

    return (
      <Tooltip text={`Deadline: ${new Date(deadline).toLocaleString("en-US", { month: "long" })} ${new Date(deadline).getDate()} ${new Date(deadline).getFullYear()}`} position="top">
        <div className="flex flex-col bg-red-800 text-white text-center items-center justify-center  rounded-full size-10">
          <div>
            <div className="text-[8px] leading-none">{new Date(deadline).toLocaleString("en-US", { month: "short" })}</div>
            <div className="text-[18px] leading-none">{new Date(deadline).getDate()}</div>
          </div>
        </div>
      </Tooltip>
    );
  };

  return (
    <>
      {/* This is where the list of requests will be */}
      <ul className="list-disc" style={{ minWidth: isMobile ? "pl-1" : "pl-5" }}>
        {paginatedTickets.map((ticket: ITicketInfo, index: number) => (
          <div key={index} className="border-b border-slate-300 pb-1 last:border-b-0">
            <div className={`${isMobile ? `flex-col` : `flex`} items-center justify-between`}>
              {/* Display Request Title here */}
              <div className="flex justify-between items-center w-full mx-2">
                <div className="flex">
                  <Tooltip text={ticket.id} position="top" className="flex items-center justify-between text-left w-full">
                    <span className="text-md px-2 flex-shrink-0">{ticket.id}</span>
                    <span className="text-xs flex-1 text-center px-2">{ticket.requestType?.name}</span>
                    <StatusBadge status={ticket.priorityLevel as RequestStatus} className="text-xs px-2 flex-shrink-0" />
                  </Tooltip>
                </div>
                <div className="flex-shrink-0">
                  {/* Display the status of the request here */}
                  {(() => {
                    const IconComponent = getNewTextStatusColor(ticket.status || "").icon;

                    return (
                      <div className={getNewTextStatusColor(ticket.status || "").className}>
                        <IconComponent className="h-4 w-4" />
                        {capitalizeFirstLetterWords(statusTag(ticket.status), "_")}
                      </div>
                    );
                  })()}
                </div>
              </div>
              <div className={`flex items-center gap-2 space-x-2 ${isMobile && `w-full justify-between`}`}>
                {isMobile ? (
                  <div className="flex mr-2">
                    {renderAssigneeProfiles(ticket.createdBy as unknown as ITicketDepartmentUsers, ticket.latestTicketAssignee as unknown as ITicketAssigneeTemplate)}
                    {renderDeadlineDate(ticket)}
                  </div>
                ) : (
                  <>
                    {/* Display the profile pics of all assigned users */}
                    {renderAssigneeProfiles(ticket.createdBy as unknown as ITicketDepartmentUsers, ticket.latestTicketAssignee as unknown as ITicketAssigneeTemplate)}
                    {/* Display deadline beside the profiles: */}
                    {renderDeadlineDate(ticket)}
                  </>
                )}

                <div className="relative flex-shrink-0" ref={dropdownRef}>
                  <MoreVertical className="w-4 h-4 cursor-pointer text-gray-500" onClick={() => setOpenDropdownIndex(openDropdownIndex === index ? null : index)} />

                  {openDropdownIndex === index && (
                    <div className="absolute right-0 mt-2 w-28 bg-white shadow-md border border-slate-300 z-50 rounded-md z-10">
                      <button
                        className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
                        onClick={() => {
                          navigate(getRolePathWithId(SharedRoutes.REQUEST_FORM, ticket.id?.toString() || ""));
                        }}
                      >
                        View
                      </button>
                    </div>
                  )}
                </div>
                {/* End of dropdown */}
              </div>
            </div>
          </div>
        ))}
      </ul>
      {/*End of list */}
      <Pagination currentPage={currentPage} totalPages={totalPages} onPageChange={handlePageChange} />
    </>
  );
};

const IncomingTicketDataList: React.FC<ListComponentProps> = ({ filterCriteria }) => {
  const navigate = useNavigate();
  //To fetch the ticket table
  const tickets = useSelector((state: RootState) => state.departmentalTicketing?.getTicketsInfo?.data || []);
  //For all the other states
  const loading = useSelector((state: RootState) => state.departmentalTicketing?.getTicketsInfo?.loading);
  const error = useSelector((state: RootState) => state.departmentalTicketing?.getTicketsInfo?.error);
  const me = useSelector((state: RootState) => state.auth.user.data);
  const { getTicketsInfo } = useTicketActions();
  //This part is for the pagination of the request list
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10; // Number of items per page
  //Gets the total of pages needed
  const totalPages = tickets ? Math.ceil(tickets?.data?.length / itemsPerPage) : 0;
  //Check if the user is using a mobile device:
  const isMobile = useIsMobile();
  //Handles the page change
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };
  //To hold the data of the current page
  const paginatedTickets = tickets?.data?.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage) || [];

  //For the dropdown, dunno if we're going to use this, but just in case.
  const [openDropdownIndex, setOpenDropdownIndex] = useState<number | null>(null);
  const dropdownRef = useRef<HTMLDivElement | null>(null);

  // Function to build filter parameters
  //* Yeah... Things get confusing when the ticket comes from your department and that ticket is approved, but somehow, you see it in the table.
  const buildFilterParams = () => {
    const condition = `createdBy.id[neq]=${me?.id}&myTicketAssignees.role[orWhere]=${ITicketAssigneeRoleConstant.receivingDepartment},${ITicketAssigneeRoleConstant.requestorManager},${ITicketAssigneeRoleConstant.president},${ITicketAssigneeRoleConstant.vicePresident},${ITicketAssigneeRoleConstant.topManApprover}${
      filterCriteria.departmentId ? `fromDepartmentId[eq]=${filterCriteria.departmentId}` : ""
    }${filterCriteria.status ? `&status[eq]=${filterCriteria.status}` : ""}${
      filterCriteria.fromDate && filterCriteria.toDate
        ? `&createdAt[gte]=${filterCriteria.fromDate.toISOString()}&createdAt[lte]=${filterCriteria.toDate.toISOString()}`
        : filterCriteria.fromDate
          ? `&createdAt[gte]=${filterCriteria.fromDate.toISOString()}`
          : filterCriteria.toDate
            ? `&createdAt[lte]=${filterCriteria.toDate.toISOString()}`
            : ""
    }`;
    return condition;
  };

  //Filter the results on load, refresh whenever we get to that point and if there's nothing
  useEffect(() => {
    //Create a quick function to just run getTicket and then set up the error and loading scripts
    getTicketsInfo({
      params: {
        relations: "ticketAssignees.user|toDepartment|fromDepartment|assignedTo|requestType|ticketAssignees|createdBy|latestTicketAssignee.user|myTicketAssignees.user",
        condition: buildFilterParams(),
        pageSize: 9999,
      },
    });
  }, [filterCriteria]);

  //insert loading and error sign here:
  if (loading) {
    return (
      <div className="flex flex-1 w-full items-center justify-center">
        <Loader />
      </div>
    );
  } else if (error) {
    return (
      <div className="flex flex-1 w-full items-center justify-center">
        <div className="p-4 text-red-500 text-center">
          <p>Failed to load requests. Please try again.</p>
        </div>
      </div>
    );
  } else if (!tickets.data || tickets.data.length === 0) {
    return (
      <div className="flex flex-1 w-full items-center justify-center">
        <div className="p-4 text-gray-500 text-center">
          <p>No requests found.</p>
        </div>
      </div>
    );
  }

  //For handling cases when pictures exceed
  const renderAssigneeProfiles = (taskCreator: ITicketDepartmentUsers, currentAssignee: ITicketAssigneeTemplate) => {
    return (
      <>
        {/* For the Creator of the Ticket */}
        <Tooltip text={`Created By: ${taskCreator?.firstname} ${taskCreator?.lastname}`} position="top">
          <Avatar classNames="h-6 w-6" src={taskCreator.profilePicturePath}>
            {taskCreator?.firstname.at(0)?.toUpperCase()}
            {taskCreator?.lastname.at(0)?.toUpperCase()}
          </Avatar>
        </Tooltip>

        {/* For the Current Assignee */}
        <Tooltip text={`Current Assignee: ${currentAssignee.user?.firstname} ${currentAssignee.user?.lastname}`} position="top">
          <Avatar classNames="h-6 w-6" src={currentAssignee.user.profilePicturePath}>
            {currentAssignee.user?.firstname.at(0)?.toUpperCase()}
            {currentAssignee.user?.lastname.at(0)?.toUpperCase()}
          </Avatar>
        </Tooltip>
      </>
    );
  };

  const renderDeadlineDate = (ticket: ITicketInfo) => {
    const deadline = (ticket.expectedCompletionDate || "") > (ticket.extensionDate || "") ? ticket.expectedCompletionDate : ticket.extensionDate;
    if (!deadline || isNaN(new Date(deadline).getTime())) {
      return (
        <Tooltip text="No Deadline Set" position="top">
          <div className="flex flex-col bg-red-800 text-white text-center items-center justify-center  rounded-full size-10">
            <div>
              <div className="text-[14px] leading-none">N/A</div>
            </div>
          </div>
        </Tooltip>
      );
    }

    return (
      <Tooltip text={`Deadline: ${new Date(deadline).toLocaleString("en-US", { month: "long" })} ${new Date(deadline).getDate()} ${new Date(deadline).getFullYear()}`} position="top">
        <div className="flex flex-col bg-red-800 text-white text-center items-center justify-center  rounded-full size-10">
          <div>
            <div className="text-[8px] leading-none">{new Date(deadline).toLocaleString("en-US", { month: "short" })}</div>
            <div className="text-[18px] leading-none">{new Date(deadline).getDate()}</div>
          </div>
        </div>
      </Tooltip>
    );
  };
  return (
    <>
      {/* This is where the list of requests will be */}
      <ul className="list-disc" style={{ minWidth: isMobile ? "pl-1" : "pl-5" }}>
        {paginatedTickets.map((ticket: ITicketInfo, index: number) => (
          <div key={index} className="border-b border-slate-300 pb-1 last:border-b-0">
            <div className={`${isMobile ? `flex-col` : `flex`} items-center justify-between`}>
              {/* Display Request Title here */}
              <div className="flex justify-between items-center w-full mx-2">
                <div className="flex">
                  <Tooltip text={ticket.id} position="top" className="flex items-center justify-between text-left w-full">
                    <span className="text-md px-2 flex-shrink-0">{ticket.id}</span>
                    <span className="text-xs flex-1 text-center px-2">{ticket.requestType?.name}</span>
                    <StatusBadge status={ticket.priorityLevel as RequestStatus} className="text-xs px-2 flex-shrink-0" />
                  </Tooltip>
                </div>
                <div className="flex-shrink-0">
                  {/* Display the status of the request here */}
                  {(() => {
                    const IconComponent = getNewTextStatusColor(ticket.status || "").icon;

                    return (
                      <div className={getNewTextStatusColor(ticket.status || "").className}>
                        <IconComponent className="h-4 w-4" />
                        {capitalizeFirstLetterWords(statusTag(ticket.status), "_")}
                      </div>
                    );
                  })()}
                </div>
              </div>
              <div className={`flex items-center gap-2 space-x-2 ${isMobile && `w-full justify-between`}`}>
                {isMobile ? (
                  <div className="flex mr-2">
                    {renderAssigneeProfiles(ticket.createdBy as unknown as ITicketDepartmentUsers, ticket.latestTicketAssignee as unknown as ITicketAssigneeTemplate)}
                    {renderDeadlineDate(ticket)}
                  </div>
                ) : (
                  <>
                    {/* Display the profile pics of all assigned users */}
                    {renderAssigneeProfiles(ticket.createdBy as unknown as ITicketDepartmentUsers, ticket.latestTicketAssignee as unknown as ITicketAssigneeTemplate)}
                    {/* Display deadline beside the profiles: */}
                    {renderDeadlineDate(ticket)}
                  </>
                )}

                <div className="relative flex-shrink-0" ref={dropdownRef}>
                  <MoreVertical className="w-4 h-4 cursor-pointer text-gray-500" onClick={() => setOpenDropdownIndex(openDropdownIndex === index ? null : index)} />

                  {openDropdownIndex === index && (
                    <div className="absolute right-0 mt-2 w-28 bg-white shadow-md border border-slate-300 z-50 rounded-md z-10">
                      <button
                        className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
                        onClick={() => {
                          navigate(getRolePathWithId(SharedRoutes.REQUEST_FORM, ticket.id?.toString() || ""));
                        }}
                      >
                        View
                      </button>
                    </div>
                  )}
                </div>
                {/* End of dropdown */}
              </div>
            </div>
          </div>
        ))}
      </ul>
      {/*End of list */}
      <Pagination currentPage={currentPage} totalPages={totalPages} onPageChange={handlePageChange} />
    </>
  );
};

const RequestList: React.FC = () => {
  //For the list tabs
  //We'll filter said headers and contents based on if the user is a Manager or not
  //This will get the method to run every page load
  const { getManagerId } = useManagerIdActions();
  const { getGlobalSettings } = useGlobalSettingsManagementActions();
  //to get the data from the managers table
  const managerIds = useSelector((state: RootState) => state.managerIds.managerIds || []);
  //This would still be in its string form, we need to convert this into an array of numbers
  const managerIdArray = JSON.parse(managerIds?.[0]?.value || "[]");
  //Check if the user ID matches any of the manager IDs, if so, we'll add the "Approval Request" tab
  const me = useSelector((state: RootState) => state.auth.user.data);

  //For mobile devices
  const isMobile = useIsMobile();
  let headerClass = "flex justify-between border-b border-slate-300 ";
  let contentClass = "border-none ";
  if (isMobile) {
    headerClass += "text-xs p-3";
    contentClass += "w-fit";
  } else {
    headerClass += "text-sm pb-4";
    contentClass += "w-full";
  }

  //*For the filter state:
  const [filterCriteria, setFilterCriteria] = useState<FilterCriteria>({});

  //Check what department the user is in
  const isManager = managerIdArray.includes(me?.id);
  const isVicePresident = useSelector((state: RootState) =>
    state.globalSettings?.getGlobalSettings?.data?.data?.find((setting: any) => setting.key === "vice_president_user_ids")?.value.includes(me?.id)
  );
  //Check if the user is a president
  const isPresident = useSelector((state: RootState) => state.globalSettings?.getGlobalSettings?.data?.data?.find((setting: any) => setting.key === "president_user_id")?.value === me?.id);
  //Then use a function to create those checks before displaying the headers
  let headers: string[] = [];
  let contents: ReactNode[] = [];
  //Checks depending on the type of user, the filterCriteria is for parameters that would be changed by the user for each list
  if (isManager || isVicePresident || isPresident) {
    headers = ["Request", "Incoming Request", "Assigned Ticket"];
    contents = [<RequestDataList filterCriteria={filterCriteria} />, <IncomingTicketDataList filterCriteria={filterCriteria} />, <AssignedTicketDataList filterCriteria={filterCriteria} />];
  } else {
    headers = ["Request", "Assigned Ticket"];
    contents = [<RequestDataList filterCriteria={filterCriteria} />, <AssignedTicketDataList filterCriteria={filterCriteria} />];
  }

  //This method will run every load.
  useEffect(() => {
    getManagerId();
    getGlobalSettings({
      params: {
        filter: `${SYSTEM_SETTINGS_IDS.VICE_PRESIDENT_USER_ID},${SYSTEM_SETTINGS_IDS.PRESIDENT_USER_ID},${SYSTEM_SETTINGS_IDS.MANAGER_USER_ID}`,
      },
    });
  }, []);

  const navigate = useNavigate();

  //* Add state to track current tab
  const [currentTab, setCurrentTab] = useState(0);
  const [showMobileMenu, setShowMobileMenu] = useState(false);

  //* For button handling
  // Function to handle filter apply
  const handleFilterApply = (criteria: FilterCriteria) => {
    setFilterCriteria(criteria);
  };

  //* Function to handle filter clear
  const handleFilterClear = () => {
    setFilterCriteria({});
  };

  return (
    <div className="p-4 bg-white shadow rounded-md relative">
      {/* Buttons positioned to align with tab headers */}
      <div className={`absolute top-4 right-4 flex gap-2 z-50 ${isMobile ? "flex-col" : ""}`}>
        {/* Hamburger menu for mobile */}
        {isMobile && (
          <div className="relative">
            <div className="flex flex-col gap-1 cursor-pointer p-2 bg-white rounded shadow" onClick={() => setShowMobileMenu(!showMobileMenu)}>
              <FaBars size={20} />
            </div>

            {/* Mobile dropdown menu */}
            {showMobileMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-white shadow-lg border border-gray-200 rounded-md z-50">
                <div className="py-1">
                  <div className="flex justify-center px-4 py-2">
                    <FilterButton<FilterCriteria> initialCriteria={filterCriteria} onApply={handleFilterApply} onClear={handleFilterClear} isMobile={isMobile}>
                      {({ criteria, setCriteria }) => (
                        <div className="space-y-4">
                          {currentTab !== 0 && !((isManager && currentTab === 2) || (!isManager && !isVicePresident && !isPresident && currentTab === 1)) && (
                            <div>
                              <label className="text-xs block mb-1">Department</label>
                              <TextField
                                type="number"
                                value={criteria.departmentId?.toString() || ""}
                                onChange={(e) =>
                                  setCriteria({
                                    ...criteria,
                                    departmentId: e.target.value ? parseInt(e.target.value) : undefined,
                                  })
                                }
                                placeholder="Department ID"
                                size="sm"
                              />
                            </div>
                          )}
                          <div>
                            <label className="text-xs block mb-1">Status</label>
                            <Select
                              value={criteria.status ?? ""}
                              onChange={(e) =>
                                setCriteria({
                                  ...criteria,
                                  status: e.target.value === "" ? undefined : e.target.value,
                                })
                              }
                              size="sm"
                              placeholder="All Statuses"
                              options={[
                                { value: "", text: "All Statuses" },
                                { value: "PENDING", text: "Pending" },
                                { value: "APPROVED", text: "Approved" },
                                { value: "REJECTED", text: "Rejected" },
                              ]}
                            />
                          </div>
                          <div className="grid grid-cols-2 gap-2">
                            <div>
                              <label className="text-xs block mb-1">From Date</label>
                              <TextField
                                type="date"
                                value={criteria.fromDate ? criteria.fromDate.toISOString().split("T")[0] : ""}
                                onChange={(e) =>
                                  setCriteria({
                                    ...criteria,
                                    fromDate: e.target.value ? new Date(e.target.value) : null,
                                  })
                                }
                                size="xs"
                              />
                            </div>
                            <div>
                              <label className="text-xs block mb-1">To Date</label>
                              <TextField
                                type="date"
                                value={criteria.toDate ? criteria.toDate.toISOString().split("T")[0] : ""}
                                onChange={(e) =>
                                  setCriteria({
                                    ...criteria,
                                    toDate: e.target.value ? new Date(e.target.value) : null,
                                  })
                                }
                                size="xs"
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </FilterButton>
                  </div>
                  {/* <button className="w-full text-left px-4 py-2 text-sm text-gray-400 hover:bg-gray-50 cursor-not-allowed" disabled>
                    <FaDownload className="inline mr-2" />
                    Download
                  </button> */}

                  <button
                    className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
                    onClick={() => {
                      navigate(getRolePath(SharedRoutes.REQUEST_FORM));
                      setShowMobileMenu(false);
                    }}
                  >
                    <FaPlus className="inline mr-2" />
                    New Request
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
        <div className={`${isMobile ? "hidden" : "flex gap-2 btn-sm"}`}>
          <FilterButton<FilterCriteria> initialCriteria={filterCriteria} onApply={handleFilterApply} onClear={handleFilterClear}>
            {({ criteria, setCriteria }) => (
              <div className="space-y-4">
                {currentTab !== 0 && !((isManager && currentTab === 2) || (!isManager && !isVicePresident && !isPresident && currentTab === 1)) && (
                  <div>
                    <label className="text-xs block mb-1">Department</label>
                    <TextField
                      type="number"
                      value={criteria.departmentId?.toString() || ""}
                      onChange={(e) =>
                        setCriteria({
                          ...criteria,
                          departmentId: e.target.value ? parseInt(e.target.value) : undefined,
                        })
                      }
                      placeholder="Department ID"
                      size="sm"
                    />
                  </div>
                )}
                {!(currentTab === 1 && (isVicePresident || isPresident)) && (
                  <div>
                    <label className="text-xs block mb-1">Status</label>
                    <Select
                      value={criteria.status || ""}
                      onChange={(e) =>
                        setCriteria({
                          ...criteria,
                          status: e.target.value || undefined,
                        })
                      }
                      size="sm"
                      placeholder="All Statuses"
                      options={[
                        { value: "PENDING", text: "Pending" },
                        { value: "APPROVED", text: "Approved" },
                        { value: "REJECTED", text: "Rejected" },
                      ]}
                    />
                  </div>
                )}
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="text-xs block mb-1">From Date</label>
                    <TextField
                      type="date"
                      value={criteria.fromDate ? criteria.fromDate.toISOString().split("T")[0] : ""}
                      onChange={(e) =>
                        setCriteria({
                          ...criteria,
                          fromDate: e.target.value ? new Date(e.target.value) : null,
                        })
                      }
                      size="sm"
                    />
                  </div>
                  <div>
                    <label className="text-xs block mb-1">To Date</label>
                    <TextField
                      type="date"
                      value={criteria.toDate ? criteria.toDate.toISOString().split("T")[0] : ""}
                      onChange={(e) =>
                        setCriteria({
                          ...criteria,
                          toDate: e.target.value ? new Date(e.target.value) : null,
                        })
                      }
                      size="sm"
                    />
                  </div>
                </div>
              </div>
            )}
          </FilterButton>
          {/* <Button classNames="border border-slate-400 hover:bg-slate-100">
            <FaDownload className="text-slate-400 hover:text-slate-600" />
          </Button> */}
          <Button
            classNames="border border-slate-400 hover:bg-slate-100"
            onClick={() => {
              navigate(getRolePath(SharedRoutes.REQUEST_FORM));
            }}
          >
            <FaPlus className="text-slate-400 hover:text-slate-600" />
          </Button>
        </div>
      </div>

      {/* Modified Tabs with scrollable container */}
      <div className="relative">
        {isMobile ? (
          <div className="flex overflow-x-auto scrollbar-hide border-b border-slate-300 mr-16" style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}>
            {headers.map((header, index) => (
              <button
                key={index}
                onClick={() => setCurrentTab(index)}
                className={`flex-shrink-0 px-4 py-2 text-xs whitespace-nowrap border-b-2 ${currentTab === index ? "border-primary text-primary" : "border-transparent text-gray-500"}`}
              >
                {header}
              </button>
            ))}
          </div>
        ) : (
          <Tabs headers={headers} contents={contents} fullWidthHeader={false} onTabChange={setCurrentTab} headerClass={headerClass} contentClass={contentClass} />
        )}

        {/* Content for mobile */}
        {isMobile && <div className="mt-4">{contents[currentTab]}</div>}
      </div>
    </div>
  );
};

export default RequestList;
