import React from "react";
import { Form, FormikProvider, useFormik } from "formik";
import * as Yup from "yup";
import Modal from "@components/common/Modal";
import Button from "@components/common/Button";
import Select from "@components/form/Select";
import TextField from "@components/form/TextField";
import { toast } from "react-toastify";

// Define the export report payload interface
interface IExportReportPayload {
  departmentId: number | string;
  status: string;
  dateFrom: string;
  dateTo: string;
  fileType: string;
}

// Define the form values interface
interface IExportReportFormValues {
  departmentId: number | string;
  status: string;
  dateFrom: string;
  dateTo: string;
  fileType: string;
}

// Validation schema for the export form
const ExportReportSchema = Yup.object().shape({
  departmentId: Yup.string().required("Please select a department"),
  status: Yup.string().required("Please select a status"),
  dateFrom: Yup.string().required("Please select start date"),
  dateTo: Yup.string()
    .required("Please select end date")
    .test(
      "date-range",
      "End date must be after start date",
      function (value) {
        const { dateFrom } = this.parent;
        if (!dateFrom || !value) return true;
        return new Date(value) >= new Date(dateFrom);
      }
    ),
  fileType: Yup.string().required("Please select file type"),
});

type ExportReportModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onExport: (payload: IExportReportPayload) => void;
  departments: { id: number; name: string }[];
};

const ExportReportModal: React.FC<ExportReportModalProps> = ({
  isOpen,
  onClose,
  onExport,
  departments,
}) => {
  // Initial form values
  const initialValues: IExportReportFormValues = {
    departmentId: "",
    status: "",
    dateFrom: "",
    dateTo: "",
    fileType: "PDF",
  };

  // Formik setup
  const formik = useFormik({
    initialValues,
    validationSchema: ExportReportSchema,
    onSubmit: async (values, { resetForm }) => {
      try {
        await onExport(values);
        resetForm();
        onClose();
        toast.success("Export request submitted successfully");
      } catch (error) {
        toast.error(`Error exporting report: ${(error as any).message}`);
      }
    },
  });

  // Handle modal close and reset form
  const handleClose = () => {
    formik.resetForm();
    onClose();
  };

  // Department options for select
  const departmentOptions = [
    { value: "", text: "— Select Department —" },
    ...departments.map((dept) => ({
      value: dept.id.toString(),
      text: dept.name,
    })),
  ];

  // Status options
  const statusOptions = [
    { value: "", text: "— Select Status —" },
    { value: "all", text: "All" },
    { value: "resolved", text: "Resolved" },
    { value: "unresolved", text: "Unresolved" },
    { value: "pending", text: "Pending" },
    { value: "in_progress", text: "In Progress" },
  ];

  // File type options
  const fileTypeOptions = [
    { value: "PDF", text: "PDF" },
    { value: "CSV", text: "CSV" },
    { value: "EXCEL", text: "Excel" },
  ];

  return (
    <Modal
      title="Generate Reports"
      titleClass="text-lg font-semibold text-gray-900"
      modalContainerClassName="max-w-md"
      isOpen={isOpen}
      onClose={handleClose}
    >
      <FormikProvider value={formik}>
        <Form className="space-y-4">
          <div className="space-y-4">
            {/* Department Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Department <span className="text-red-500">*</span>
              </label>
              <Select
                name="departmentId"
                options={departmentOptions}
                value={formik.values.departmentId}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={!!(formik.touched.departmentId && formik.errors.departmentId)}
                errorText={formik.touched.departmentId ? formik.errors.departmentId : ""}
                placeholder="Select Department"
                required
              />
            </div>

            {/* Status Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status <span className="text-red-500">*</span>
              </label>
              <Select
                name="status"
                options={statusOptions}
                value={formik.values.status}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={!!(formik.touched.status && formik.errors.status)}
                errorText={formik.touched.status ? formik.errors.status : ""}
                placeholder="Select Status"
                required
              />
            </div>

            {/* Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Period <span className="text-red-500">*</span>
              </label>
              <div className="grid grid-cols-2 gap-2">
                <TextField
                  type="date"
                  name="dateFrom"
                  placeholder="From"
                  value={formik.values.dateFrom}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={!!(formik.touched.dateFrom && formik.errors.dateFrom)}
                  errorText={formik.touched.dateFrom ? formik.errors.dateFrom : ""}
                  required
                />
                <TextField
                  type="date"
                  name="dateTo"
                  placeholder="To"
                  value={formik.values.dateTo}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={!!(formik.touched.dateTo && formik.errors.dateTo)}
                  errorText={formik.touched.dateTo ? formik.errors.dateTo : ""}
                  required
                />
              </div>
            </div>

            {/* File Type Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                File Type <span className="text-red-500">*</span>
              </label>
              <Select
                name="fileType"
                options={fileTypeOptions}
                value={formik.values.fileType}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={!!(formik.touched.fileType && formik.errors.fileType)}
                errorText={formik.touched.fileType ? formik.errors.fileType : ""}
                placeholder="Select File Type"
                required
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="secondary"
              onClick={handleClose}
              classNames="px-4 py-2"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={formik.isSubmitting || !formik.isValid}
              classNames="px-4 py-2"
            >
              {formik.isSubmitting ? "Exporting..." : "Export"}
            </Button>
          </div>
        </Form>
      </FormikProvider>
    </Modal>
  );
};

export default ExportReportModal;

