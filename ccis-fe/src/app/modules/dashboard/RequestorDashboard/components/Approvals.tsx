import Modal from "@components/common/Modal";
import Tooltip from "@components/common/Tooltip";
import { getTextStatusColor } from "@helpers/text";
import { ITicketAssigneeRoleConstant } from "@interface/ticket-assignee-roles.interface";
import { ITicketDetails } from "@interface/ticket-assignee.interface";
import { imageStorageUrl } from "@services/variables";
import React, { useState } from "react";
import { LiaComments } from "react-icons/lia";

interface TicketApprovalProps {
  ticket: ITicketDetails;
}

interface RemarksModalData {
  userName: string;
  userRole: string;
  remarks: string;
  status: string;
  date?: string; // You can add this if you have timestamp data
  avatar: string;
}

const Approvals: React.FC<TicketApprovalProps> = ({ ticket }) => {
  const [isRemarksModalOpen, setIsRemarksModalOpen] = useState(false);
  const [selectedRemarks, setSelectedRemarks] = useState<RemarksModalData | null>(null);

  // Check if Vice President is the ticket creator
  const isVicePresidentCreator = ticket?.ticketAssignees?.some((assignee: any) => assignee.role === ITicketAssigneeRoleConstant.vicePresident && assignee.userId === ticket?.createdBy?.id);

  // Check if Requestor Manager is the ticket creator
  const isRequestorManagerCreator = ticket?.ticketAssignees?.some((assignee: any) => assignee.role === ITicketAssigneeRoleConstant.requestorManager && assignee.userId === ticket?.createdBy?.id);

  // Define approval orders based on who created the ticket
  const defaultApprovalOrder = [
    ITicketAssigneeRoleConstant.requestorManager,
    ITicketAssigneeRoleConstant.topManApprover,
    ITicketAssigneeRoleConstant.receivingDepartment,
    ITicketAssigneeRoleConstant.vicePresident,
    ITicketAssigneeRoleConstant.president,
  ];

  const vicePresidentTicketCreatorApprovalOrder = [ITicketAssigneeRoleConstant.vicePresident, ITicketAssigneeRoleConstant.receivingDepartment];

  const requestorManagerTicketCreatorApprovalOrder = [
    ITicketAssigneeRoleConstant.requestorManager,
    ITicketAssigneeRoleConstant.topManApprover,
    ITicketAssigneeRoleConstant.receivingDepartment,
    ITicketAssigneeRoleConstant.vicePresident,
    ITicketAssigneeRoleConstant.president,
  ];

  // Use the appropriate approval order
  const approvalOrder = isVicePresidentCreator ? vicePresidentTicketCreatorApprovalOrder : isRequestorManagerCreator ? requestorManagerTicketCreatorApprovalOrder : defaultApprovalOrder;

  // Map ticketAssignees to users array and sort by approval order
  const users =
    ticket?.ticketAssignees
      ?.filter((assignee: any) => assignee.role !== ITicketAssigneeRoleConstant.requestor && assignee.role !== ITicketAssigneeRoleConstant.ticketAssignee) // Exclude requestor and forwardedTo roles
      .map((assignee: any) => ({
        id: assignee.userId,
        name: `${assignee.user.firstname} ${assignee.user.lastname}`,
        role: assignee.role,
        status: assignee.status,
        avatar: `${imageStorageUrl}/${assignee.user.profilePicturePath}`,
        remarks: assignee.remarks,
        updatedAt: assignee.updatedAt,
      }))
      .sort((a: any, b: any) => {
        const indexA = approvalOrder.indexOf(a.role);
        const indexB = approvalOrder.indexOf(b.role);

        // If role is not in approvalOrder, put it at the end
        if (indexA === -1 && indexB === -1) return 0;
        if (indexA === -1) return 1;
        if (indexB === -1) return -1;

        return indexB - indexA;
      }) || [];

  const handleRemarksClick = (user: any) => {
    setSelectedRemarks({
      userName: user.name,
      userRole: user.role,
      remarks: user.remarks,
      status: user.status,
      avatar: user.avatar,
      date: user.updatedAt
        ? new Date(user.updatedAt).toLocaleDateString("en-US", {
            month: "2-digit",
            day: "2-digit",
            year: "numeric",
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
          })
        : undefined,
    });
    setIsRemarksModalOpen(true);
  };

  const closeRemarksModal = () => {
    setIsRemarksModalOpen(false);
    setSelectedRemarks(null);
  };

  return (
    <>
      <div className="mx-auto bg-white p-6 space-y-6">
        {users.map((user: any, index: number) => (
          <div key={user.id} className="flex items-center space-x-4">
            {/* Timeline dot */}
            <div className="flex flex-col items-center">
              <div className="w-3 h-3 bg-yellow-200 rounded-full"></div>
              {index !== users.length - 1 && <div className="w-0.5 h-16 bg-yellow-200 mt-2"></div>}
            </div>

            {/* User info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <div className="flex flex-col gap-1 w-full">
                  <div className="flex items-center w-full justify-between">
                    <h3 className="text-lg font-medium text-gray-900 truncate">{user?.name}</h3>
                    {/* Message icon - only show if remarks exist */}
                    {user?.remarks && (
                      <Tooltip text="View Remarks" className="cursor-pointer" position="top">
                        <LiaComments className="mr-1 text-primary" size={20} onClick={() => handleRemarksClick(user)} />
                      </Tooltip>
                    )}
                  </div>
                  <p className="text-sm text-gray-500 truncate">{user?.role}</p>
                </div>
              </div>
              <div className="mt-2">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getTextStatusColor(user?.status)}`}>{user?.status}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Remarks Modal */}
      <Modal isOpen={isRemarksModalOpen} onClose={closeRemarksModal} title="Remarks" modalContainerClassName="max-w-md">
        {selectedRemarks && (
          <div className="space-y-4">
            {/* User info with timeline design */}
            <div className="flex items-center space-x-4">
              <div className="flex flex-col items-center">
                {selectedRemarks.avatar && selectedRemarks.avatar !== "https://ccis-stage.s3.ap-southeast-1.amazonaws.com/null" ? (
                  <img src={selectedRemarks.avatar} alt="User Avatar" className="w-16 h-16 p-0 bg-primary rounded-full" />
                ) : (
                  <div className="w-16 h-16 p-4 rounded-full bg-[#94a3b8] flex items-center justify-center text-2xl font-bold text-gray-200">
                    {selectedRemarks.userName
                      ? (() => {
                          const [firstName, lastName] = selectedRemarks.userName.split(" ");
                          return `${firstName?.[0] ?? ""}${lastName?.[0] ?? ""}`;
                        })()
                      : ""}
                  </div>
                )}
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">{selectedRemarks.userName}</h3>
                <p className="text-sm text-gray-500">{selectedRemarks.userRole}</p>
              </div>
            </div>

            {/* Status and Date */}
            <div className="flex items-center justify-between text-sm">
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTextStatusColor(selectedRemarks.status)}`}>{selectedRemarks.status}</span>
              {selectedRemarks.date && <span className="text-gray-500">{selectedRemarks.date}</span>}
            </div>

            {/* Remarks */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-gray-800">{selectedRemarks.remarks}</p>
            </div>
          </div>
        )}
      </Modal>
    </>
  );
};

export default Approvals;
