import React from "react";

import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import dashboard from "@assets/dashboard_img.png";
import { FiChevronRight } from "react-icons/fi";
import Button from "@components/common/Button";
import { Bar, Doughnut } from "react-chartjs-2";
import { Chart as ChartJS, CategoryScale, ArcElement, LinearScale, BarElement, Title, Tooltip, Legend } from "chart.js";

// Register the components you plan to use
ChartJS.register(CategoryScale, LinearScale, BarElement, ArcElement, Title, Tooltip, Legend);

const SalesDashboard: React.FC = () => {
  const user = useSelector((state: RootState) => state.auth?.user?.data);

  const data = {
    labels: ["Jan", "Feb", "March", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
    datasets: [
      {
        label: "Registered Cooperatives 2024",
        data: [30, 21, 29, 40, 9, 20, 31, 50, 12, 31, 32, 25],
        backgroundColor: "#042882",
        borderRadius: 3,
        barThickness: 30, // Sets the exact width of each bar in pixels
        maxBarThickness: 40, // Sets the maximum width of the bar
        categoryPercentage: 0.8, // Controls the width of the category relative to the entire bar chart
        barPercentage: 0.9, // Controls the width of the bars relative to the category width
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      title: {
        display: false,
        text: "",
      },
    },
    scales: {
      y: {
        beginAtZero: true, // Ensures the y-axis starts at 0
        max: 60, // Set the maximum y-axis value
        ticks: {
          stepSize: 10, // Set the interval between ticks
        },
      },
    },
  };

  const data2 = {
    labels: ["Life Products", "Nonlife Products"],
    datasets: [
      {
        data: [1000, 500],
        borderRadius: 10,
        backgroundColor: ["#042882", "#ffd000"],
        borderWidth: 5,
        hoverBorderWidth: 0,
      },
    ],
  };

  const options2 = {
    responsive: true,
    plugins: {
      title: {
        display: false,
        text: "Donut Chart Example",
      },
    },
    cutout: "70%", // This creates the "donut" effect by cutting out 70% of the center
  };

  return (
    <div className="h-full w-full flex flex-col justify-center items-center ">
      <div className="h-1/3 w-full flex items-center justify-center xl:pt-16">
        <div className="w-2/3 h-full flex flex-col items-start justify-center p-4 glass rounded-xl ">
          <span className="text-white xl:text-4xl text-xl font-semibold mb-2">Welcome back, {user?.firstname}</span>
          <span className="xl:text-sm text-xs text-zinc-400">Have a nice day at work.</span>
        </div>
        <div className="w-1/3 h-full flex items-center justify-center ">
          <img src={dashboard} className="" />
        </div>
      </div>
      <br />

      {/* PARENT SA MGA CHARTS */}
      <div className="w-full h-1/2  ">
        <div className="w-full h-full flex">
          {" "}
          <div className="w-2/3 h-full ">
            <Bar data={data} options={options} />
          </div>
          <div className="w-1/3 h-[95%] flex items-center justify-center relative ">
            <Doughnut data={data2} options={options2} />
            <div className="absolute text-poppins-semifold text-center text-xl text-primary ">
              {" "}
              Total <br /> <strong className="text-3xl">1,500</strong>
            </div>
          </div>
        </div>
      </div>

      <div className="h-1/4 w-full flex flex-col mt-10">
        <div className="mb-4 xl:text-xl text-sm flex justify-between items-center px-10">
          <div>Continue where you left off ...</div>
          <div className="underline text-sm text-primary cursor-pointer flex items-center justify-center">
            View all drafts <FiChevronRight size={20} />
          </div>
        </div>

        <div className="w-full flex flex-col items-center justify-between gap-2 ">
          <div className=" border-b border-zinc-300 xl:p-6 p-2 w-full rounded-md flex items-center justify-center text-sm">
            <div className=" w-1/5 flex items-center justify-center">January 11, 2024</div>
            <div className=" w-1/5 flex items-center justify-center ">Product Guidelines</div>
            <div className=" w-1/5 flex items-center justify-center">Customized</div>
            <div className=" w-1/5 flex items-center justify-center ">
              <div className="px-4 p-2 flex items-center justify-center rounded-full bg-zinc-200  ">Draft</div>
            </div>
            <div className=" w-1/5 flex items-center justify-center ">
              <Button classNames="btn px-8 p-2 flex items-center justify-center rounded-md bg-primary  text-white">View Details</Button>
            </div>
          </div>

          <div className=" border-b border-zinc-300 xl:p-6 p-2 w-full rounded-md flex items-center justify-center text-sm">
            <div className=" w-1/5 flex items-center justify-center">January 11, 2024</div>
            <div className=" w-1/5 flex items-center justify-center ">Product Guidelines</div>
            <div className=" w-1/5 flex items-center justify-center">Standard</div>
            <div className=" w-1/5 flex items-center justify-center ">
              <div className="px-4 p-2 flex items-center justify-center rounded-full bg-zinc-200  ">Draft</div>
            </div>
            <div className=" w-1/5 flex items-center justify-center ">
              <Button classNames="btn px-8 p-2 flex items-center justify-center rounded-md bg-primary  text-white">View Details</Button>
            </div>
          </div>

          <div className=" border-b border-zinc-300 xl:p-6 p-2 w-full rounded-md flex items-center justify-center text-sm">
            <div className=" w-1/5 flex items-center justify-center">January 11, 2024</div>
            <div className=" w-1/5 flex items-center justify-center ">Product Guidelines</div>
            <div className=" w-1/5 flex items-center justify-center">Customized</div>
            <div className=" w-1/5 flex items-center justify-center ">
              <div className="px-4 p-2 flex items-center justify-center rounded-full bg-zinc-200  ">Draft</div>
            </div>
            <div className=" w-1/5 flex items-center justify-center ">
              <Button classNames="btn px-8 p-2 flex items-center justify-center rounded-md bg-primary  text-white">View Details</Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalesDashboard;
