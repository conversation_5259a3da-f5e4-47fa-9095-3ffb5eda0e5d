// import Swal from "sweetalert2";
import Swal, { SweetAlertOptions, SweetAlertResult } from "sweetalert2";
import { ReactElement } from "react";
import ReactDOMServer from "react-dom/server";
const renderToHtml = (element: ReactElement): string => {
  return ReactDOMServer.renderToString(element);
};

export const confirmDelete = async (
  entity: string,
  onDelete?: () => void,
  customTitle?: string,
  customText?: string,
  showDeleteIcon: boolean = false,
  confirmButtonText: string = "Delete",
  cancelButtonText: string = "Cancel",
  confirmButtonColor: string = "#FF5151",
  cancelButtonColor: string = "#d1d1d1",
  icon: "warning" | "error" | "success" | "info" | "question" = "warning",
  iconColor: string = "#FF5151",
  backgroundColor: string = "#FFFFFF"
) => {
  return Swal.fire({
    title: customTitle || `Delete ${entity}`,
    text: customText || `Are you sure you want to delete this ${entity}?`,
    showConfirmButton: true,
    confirmButtonText: confirmButtonText,
    cancelButtonText: cancelButtonText,
    confirmButtonColor: confirmButtonColor,
    showCancelButton: true,
    cancelButtonColor: cancelButtonColor,
    reverseButtons: true,
    preConfirm: onDelete,
    icon: showDeleteIcon ? icon : undefined,
    iconColor: iconColor,
    background: backgroundColor,
  });
};
export const confirmSaveOrEdit = async (title?: string, description?: string) => {
  const result = await Swal.fire({
    title: title ?? "Are you sure?",
    text: description ?? "",
    icon: "question",
    showCancelButton: true,
    cancelButtonText: "No, cancel",
    confirmButtonText: "Yes, do it!",
    reverseButtons: true,
  });

  return result.isConfirmed;
};

export const confirmPrintOrView = async (title?: string) => {
  const result = await Swal.fire({
    title: title ?? "Are you sure to print?",
    icon: "question",
    showCancelButton: true,
    cancelButtonText: "No, Just View",
    confirmButtonText: "Yes, do it!",
  });

  return {
    isConfirmed: result.isConfirmed,
    isDismissed: result.dismiss === Swal.DismissReason.cancel,
  };
};

export const showSuccess = async (title?: string, message?: string, onClose?: () => void, allowOutsideClick: boolean = true) => {
  const result = await Swal.fire({
    title: title ?? "Transaction Successful!",
    text: message ?? "Your transaction has been processed successfully.",
    showCancelButton: false,
    confirmButtonText: "Ok",
    icon: "success",
    allowOutsideClick, // Controls whether clicking outside closes the dialog
  });

  // Trigger the onClose callback if provided
  if (onClose) onClose();

  return result;
};

export const showError = async (title?: string, message?: string, onClose?: () => void) => {
  const result = await Swal.fire({
    title: title ?? "Something went wrong!",
    text: message ?? "An error has occurred. Please try again.",
    showCancelButton: false,
    confirmButtonText: "Ok",
    icon: "error",
  });

  if (onClose) onClose();

  return result;
};

export const confirmArchive = async (entity: string, onDelete?: () => void) => {
  return Swal.fire({
    title: `Archive ${entity}`,
    text: `Are you sure you want to archive this ${entity}?`,
    showConfirmButton: true,
    confirmButtonText: "Archive now",
    cancelButtonText: "Cancel",
    confirmButtonColor: "#FF5151",
    showCancelButton: true,
    cancelButtonColor: "#d1d1d1",
    reverseButtons: true,
    preConfirm: onDelete,
  });
};

export const showAlert = (title: string, text: string, icon?: ReactElement | string, options?: SweetAlertOptions): Promise<SweetAlertResult<any>> => {
  const iconHtml = icon && typeof icon !== "string" ? renderToHtml(icon) : undefined;

  return Swal.fire({
    title,
    text,
    icon: typeof icon === "string" ? undefined : "info", // Use 'info' or any default icon
    iconHtml, // Use custom HTML if provided
    ...options,
  });
};

export const showInfo = async (title: string, message: string, confirmButtonText: string) => {
  return Swal.fire({
    icon: "info",
    title: title,
    text: message,
    showConfirmButton: true,
    confirmButtonText: confirmButtonText,
    confirmButtonColor: "#042882",
    showCancelButton: false,
  });
};

export const showConfirmation = async (title: string, message: string, onSubmit?: () => void) => {
  return Swal.fire({
    title: `Submit ${title}`,
    text: message,
    showConfirmButton: true,
    confirmButtonText: "Submit",
    confirmButtonColor: "#042882",
    showCancelButton: true,
    cancelButtonColor: "#d1d1d1",
    preConfirm: onSubmit,
  });
};
