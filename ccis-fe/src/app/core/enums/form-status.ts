export enum FormStatus {
  INCOMING_RECEIVED = "INCOMING_RECEIVED",
  DRAFT = "DRAFT",
  FOR_APPROVAL = "FOR_APPROVAL",
  FOR_REVISION = "FOR_REVISION",
  APPROVED = "APPROVED",
  ACTIVE = "ACTIVE",
  DISAPPROVED = "DISAPPROVED",
  DENIED = "DENIED",
  REJECTED = "REJECTED",
  FOR_RECEIVING = "FOR_RECEIVING",
  RELEASED = "RELEASED",
  RECEIVED = "RECEIVED",
  VERIFY_LIST = "VERIFY_LIST",
  VERIFIED = "VERIFIED",
  NOT_YET_RECEIVED = "NOT_YET_RECEIVED",
  PENDING = "PENDING",
  RETURNED = "RETURNED",
  ON_HAND = "ON-HAND",
}

export enum PadStatus {
  USED = "USED",
  UNUSED = "UNUSED",
  CANCELLED = "CANCELLED",
}

//Role types
export enum RoleType {
  CHIEFCASHIER = "Head Cashier",
  CLIFSA = "clifsa admin",
  TREASURY = "Treasury",
  ADMINOUTGOING = "Admin Outgoing",
  ADMININCOMING = "Admin Incoming",
  CASHIER = "Cashier",
  GAM = "GAM",
  IOC = "Incoming and Outgoing Cashier",
  INCLUDES_CLIFSA = "Clifsa",
  INCLUDES_INCOMING = "incoming - admin",
  INCLUDES_CASHIER = "cashier",
  EXCLUDE_CLIFSA = "CLIFSA"
}

export enum AreaCode {
  LUZON = "L",
  VISAYAS = "V",
  MINDANAO = "M",
}

export enum ReleasedMethods {
  ON_HAND = "On-Hand",
  MAIL_COURIER = "Mail-Courier",
  THIRD_PARTY_PERSON = "Third-Party Person",
}
