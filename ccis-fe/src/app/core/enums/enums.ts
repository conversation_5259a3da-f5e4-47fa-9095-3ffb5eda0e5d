export enum CooperativeDataSourceType {
  CDA = "CDA",
  LOCAL = "LOCAL",
}

export enum AttachmentTypes {
  COMPLIANCE = "COMPLIANCE",
  SURVEY_RESULT = "SURVEY_RESULT",
}
// use this for mapping the condition for approval is product proposal viewing
export enum ProposableTypes {
  PRODUCT_REVISION = "PRODUCT_REVISION",
  AER = "ACTUARY_EVALUATION_REPORT",
}

export enum ProposalTypes {
  CUSTOMIZED = "CUSTOMIZED",
  STANDARD = "STANDARD",
}

export enum CustomizeType {
  PROVISIONS = "provisions",
  AER = "AER",
}

export enum ProductCategoryType {
  CLPP = "CLPP",
  CLSP = "CLSP",
  GYRT = "GYRT",
  FIP = "FIP",
  CFP = "CFP",
}

export enum ProposalUserType {
  RND = "RND",
  SALES = "SALES",
}
