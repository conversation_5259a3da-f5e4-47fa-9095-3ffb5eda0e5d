import { FC, ReactNode } from "react";
import { IoNotificationsOutline } from "react-icons/io5";
import { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { RootState } from "@state/store";
import dayjs from "dayjs";
import { useNotificationActions } from "@state/reducer/notification";
import notifGears from "@assets/notif-gears.webm";
import NoNotif from "@assets/no-notif.svg";
import { getRolePathWithId } from "@helpers/navigatorHelper";
import { NotificationTypes } from "@enums/notification-types";
import { SharedRoutes } from "@enums/shared-routes";

type TNotificationProps = {
  icon?: ReactNode;
  count?: number;
  data?: any[];
};

const Notification: FC<TNotificationProps> = ({ icon = <IoNotificationsOutline size={26} />, count = 0 }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { patchMarkAllAsReadNotifications, patchReadNotification, getNotifications } = useNotificationActions();
  const notifications = useSelector((state: RootState) => state.notification.notifications);

  const [open, setOpen] = useState(false);

  const handleOpen = () => {
    setOpen(!open);
  };

  const handleNotificationPage = () => {
    const pathRole = location.pathname.split("/")[1]; // e.g., 'admin'
    navigate(`/${pathRole}/notification`, { replace: true });
  };

  const handleMarkAllAsRead = async () => {
    await patchMarkAllAsReadNotifications();
    await getNotifications();
  };

  const handleReadNotification = async (notificationId: number) => {
    await patchReadNotification({ id: notificationId });
    await getNotifications();
  };

  const handleViewTicket = (e: React.MouseEvent, notifiableId: string) => {
    e.stopPropagation(); // Prevent triggering the parent click event
    navigate(getRolePathWithId(SharedRoutes.REQUEST_FORM, notifiableId));
  };

  return (
    <div className="relative">
      <div tabIndex={0} role="button" className="btn btn-ghost btn-circle">
        <div className="indicator">
          <div className=" p-2 relative" onClick={handleOpen}>
            {icon && icon}
            {count > 0 && <div className="absolute top-2 animate-ping">{icon && icon}</div>}
            <span className="badge badge-sm indicator-item bg-red-500 text-white">{count}</span>
          </div>
        </div>
      </div>

      {open && (
        <div className="animation duration-300 w-[30rem] h-[48rem] border-2 bg-white border-zinc-200 absolute top-14 -left-96 rounded-xl">
          <div className="w-full flex justify-between p-5 ">
            <div className="text-primary text-xl flex items-center justify-center">Notifications</div>
            <div className="text-zinc-400 flex items-center justify-center rounded-md p-2 px-4 cursor-pointer hover:bg-zinc-200" onClick={handleMarkAllAsRead}>
              Mark all as read
            </div>
          </div>

          <div className="overflow-y-auto h-[40rem] bg-white divide-y divide-zinc-300">
            {notifications.map((notification: any, idx) => (
              <div key={idx} className={`p-4 cursor-pointer flex relative`} onClick={() => handleReadNotification(notification.id)}>
                <div className="w-1/5 flex items-center justify-center pr-2">
                  {/* <div className="bg-amber-400 text-white rounded-full w-14 h-14 font-poppins-semibold flex items-center justify-center text-xl font-bold">
                    {notification?.notifiable?.firstname?.[0]}
                    {notification?.notifiable?.lastname?.[0]}
                  </div> */}
                  <video width="500" height="300" autoPlay muted>
                    <source src={notifGears} type="video/webm" />
                    Your browser does not support the video tag.
                  </video>
                </div>
                <div className="w-4/5 ">
                  <div className={`font-semibold text-start font-poppins-semibold mb-2 text-primary`}>
                    {notification.notifiableType
                      .toLowerCase()
                      .split("_")
                      .map((word: any) => word.charAt(0).toUpperCase() + word.slice(1))
                      .join(" ")}
                  </div>
                  <div className="text-sm text-justify">{notification.message.length > 100 ? `${notification.message.slice(0, 200)}. . . ` : notification.message}</div>
                  <div className="flex justify-between items-center mt-2 w-full">
                    <span className={`text-xs text-zinc-400`}>{dayjs(notification.createdAt).format("MMM D, YYYY h:mm A")}</span>
                    {notification.notifiableType === NotificationTypes.TICKET && (
                      <button
                        onClick={(e) => handleViewTicket(e, notification.notifiableId)}
                        className="text-xs bg-primary text-white px-3 py-1 rounded-md hover:bg-primary/90 transition-colors"
                      >
                        View
                      </button>
                    )}
                  </div>
                </div>
                <div className="flex items-center justify-center pl-2">{notification.readAt === null && <div className="w-3 h-3 rounded-full bg-red-500"></div>}</div>
              </div>
            ))}
            {notifications.length === 0 && (
              <div className="p-4 text-center text-gray-500 h-80 px-20 flex flex-col items-center justify-center pt-40">
                <img src={NoNotif} alt="No notifications" className=" h-60" />
                <br />
                <div className="text-zinc-400">You have no notifications at the moment.</div>
              </div>
            )}
          </div>
          <div className="w-full flex items-center justify-center text-sm">
            <div className=" text-primary text-center hover:bg-sky-50 p-2 px-4 w-max rounded-md cursor-pointer" onClick={handleNotificationPage}>
              Show all notifications
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Notification;