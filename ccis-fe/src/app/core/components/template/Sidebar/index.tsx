import { ReactNode, useContext, FC } from "react";
//import { LogOut } from "lucide-react";
import Brand from "../../common/Brand";
import { DashboardContext } from "../../../context/dashboardContext";
//import Avatar from "@components/common/Avatar";
//import { useSelector } from "react-redux";
//import { RootState } from "@state/store";
//import Typography from "@components/common/Typography";
//import { logoutRequest } from "@services/auth/auth.service";
//import { handleLogout } from "@services/utils/utils.service";

export interface ISidebarProps {
  children?: ReactNode | ReactNode[];
}

const Sidebar: FC<ISidebarProps> = ({ children }) => {
  // const user = useSelector((state: RootState) => state.auth.user.data);
  //const profile = useSelector((state: RootState) => state.profile.profile);
  const dashboardContext = useContext(DashboardContext);

  // const handleAccountLogout = async () => {
  //   try {
  //     const { status } = await logoutRequest();
  //     //@ts-expect-error status is not a number
  //     if (status === "success") {
  //       handleLogout();
  //     }
  //   } catch (error) {
  //     console.error(error);
  //   }
  // };

  return (
    <aside className={`h-full transition-all xl:pl-0  ${dashboardContext?.collapse ? "w-[80px]" : "xl:w-[250px] w-[300px]"}`}>
      <nav className="h-full w-full flex flex-col flex-1 justify-between bg-[url('/assets/sidebar-bg-dark.png')] bg-cover  shadow-sm overflow-auto no-scrollbar">
        <div>
          <div className="pt-10 flex flex-1 justify-center">
            <Brand height={100} width={150} type={1} />
          </div>
          <div className="flex flex-col overflow-y-auto">{children}</div>
        </div>
      </nav>
    </aside>
  );
};

export default Sidebar;
