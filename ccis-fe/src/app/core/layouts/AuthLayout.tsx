import { useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import Loader from "../components/Loader";
import { RootState } from "../state/reducer";
import { useSelector } from "react-redux";
import { UserRoles } from "../interface/routes.interface";
import { ROUTES } from "../constants/routes";
import { hasRole } from "@helpers/roleChecker";

const AuthLayout = ({ children }: { children?: React.ReactNode }) => {
  const navigate = useNavigate();

  const { data: currentUser, loading: getUserLoading, success: getUserSuccess } = useSelector((state: RootState) => state.auth.user);
  const { data: auth } = useSelector((state: RootState) => state?.auth?.login);

  const isAuthenticated = useMemo(() => {
    return !getUserLoading && getUserSuccess && currentUser && auth?.token;
  }, [getUserLoading, currentUser, getUserSuccess, auth?.token]);

  useEffect(() => {
    if (!getUserLoading) {
      if (isAuthenticated) {
        if (hasRole(currentUser?.roles ?? [], UserRoles.admin)) {
          //admin
          navigate(ROUTES.ADMIN.dashboard.key);
          //marketing
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.marketing)) {
          navigate(ROUTES.MARKETING.marketingDashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.actuary)) {
          //actuary
          navigate(ROUTES.ACTUARY.actuaryDashboard.key);
          //sales executive assistant
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.sea)) {
          navigate(ROUTES.SALESEXECUTIVEASSISTANT.salesExecutiveAssistantDashboard.key);
          //uatadmin
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.uatadmin)) {
          navigate(ROUTES.UATADMIN.uatManagement.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.cashier)) {
          navigate(ROUTES.CASHIER.actuaryDashboard.key);
        }
        //treasury
        else if (hasRole(currentUser?.roles ?? [], UserRoles.treasury)) {
          navigate(ROUTES.TREASURY.treasuryDashboard.key);
        }
        //compliance
        else if (hasRole(currentUser?.roles ?? [], UserRoles.compliance)) {
          navigate(ROUTES.COMPLIANCE.complianceDashboard.key);
        }
        //sales
        else if (hasRole(currentUser?.roles ?? [], UserRoles.sales)) {
          navigate(ROUTES.SALES.salesDashboard.key);
        }
        //cashier incoming
        else if (hasRole(currentUser?.roles ?? [], UserRoles.incomingCashier)) {
          navigate(ROUTES.INCOMINGCASHIER.incomingCashierDashboard.key);
        }
        //cashier outgoing
        else if (hasRole(currentUser?.roles ?? [], UserRoles.outgoingCashier)) {
          navigate(ROUTES.OUTGOINGCASHIER.outgoingCashierDashboard.key);
          //outgoing admin
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.outgoingAdmin)) {
          navigate(ROUTES.OUTGOINGADMIN.outgoingAdminDashboard.key);
          //chief cashier
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.chiefCashier)) {
          navigate(ROUTES.CHIEFCASHIER.chiefCashierDashboard.key);
        }
        //clifsa admin
        else if (hasRole(currentUser?.roles ?? [], UserRoles.clifsaAdmin)) {
          navigate(ROUTES.CLIFSAADMIN.clifsaAdminDashboard.key);
        }
        //gam
        else if (hasRole(currentUser?.roles ?? [], UserRoles.gam)) {
          navigate(ROUTES.GAM.gamDashboard.key);
        }
        //IO Cashier
        else if (hasRole(currentUser?.roles ?? [], UserRoles.ioc)) {
          navigate(ROUTES.INCOMINGOUTGOINGCASHIER.incomingOutgoingCashierDashboard.key);
        }
        //underwriting
        else if (hasRole(currentUser?.roles ?? [], UserRoles.underwriting)) {
          navigate(ROUTES.UNDERWRITING.underwritingDashboard.key);
        }
        //claims
        else if (hasRole(currentUser?.roles ?? [], UserRoles.claims)) {
          navigate(ROUTES.CLAIMS.claimsDashboard.key);
        }
        //incoming admin
        else if (hasRole(currentUser?.roles ?? [], UserRoles.incomingAdmin)) {
          navigate(ROUTES.INCOMINGADMIN.incomingAdminDashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.actuaryManager)) {
          navigate(ROUTES.ACTUARYMANAGER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.actuaryAnalyst1)) {
          navigate(ROUTES.ACTUARYANALYST1.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.actuaryAssistant1)) {
          navigate(ROUTES.ACTUARYASSISTANT1.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.avpLifeNonLife)) {
          navigate(ROUTES.AVPFORLIFEANDNONLIFE.dashboard.key);
          //Research and Development officer
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.rnd)) {
          navigate(ROUTES.RESEARCHANDDEVELOPMENT.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.claimsManager)) {
          navigate(ROUTES.CLAIMSMANAGER.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.vicePresidentSales)) {
          navigate(ROUTES.VICEPRESIDENTFORSALES.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.vicePresidentOperations)) {
          navigate(ROUTES.VICEPRESIDENTFOROPERATION.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.presidentCeo)) {
          navigate(ROUTES.PRESIDENTANDCEO.dashboard.key);
        } else if (hasRole(currentUser?.roles ?? [], UserRoles.areaSalesManager)) {
          navigate(ROUTES.AREASALESMANAGER.dashboard.key);
        }
        // infrastructure officer
        else if (hasRole(currentUser?.roles ?? [], UserRoles.infraOfficer)) {
          navigate(ROUTES.INFRASTRUCTUREOFFICER.dashboard.key);
        }
        // accounting
        else if (hasRole(currentUser?.roles ?? [], UserRoles.accounting)) {
          navigate(ROUTES.ACCOUNTING.dashboard.key);
        } else {
          navigate(ROUTES.USERS.dashboard.key);
        }
      } else {
        if (window.location.pathname === "/") navigate(ROUTES.AUTH.login.key);
      }
    }
  }, [isAuthenticated]);

  return (
    <div className="w-screen h-screen grid place-items-center overflow-hidden">
      {!getUserLoading && children}
      {getUserLoading && <Loader />}
    </div>
  );
};

export default AuthLayout;
