import { PayloadAction } from "@reduxjs/toolkit";
import { LoadingResult, IAttachments } from "@interface/common.interface";
import { IUser } from "@interface/user.interface";
import { ITicket, IDueTicket, IGraphTicket, ITicketInterface, TicketResponse, DueTicketResponse, GraphTicketResponse, ITicketDepartmentUsers, ITicketInfo, TicketAssigneeResponse} from "@interface/departmental-ticketing-interface";
import { IDepartment } from "@interface/ticket-assignee.interface";

// State type for managing Tickets
export type TTicketManagementState = {
  tickets: ITicket[];
  ticket?: ITicket;

  ticketsInfo: ITicketInfo[];
  ticketInfo?: ITicketInfo;
  getTicketsInfo?: GetTicketsInfo;

  priorityTickets: ITicket[];
  dueTickets: IDueTicket[];
  graphTickets: IGraphTicket[];

  selectedTicket: ITicketInfo[] | null;

  getTicket?: GetTicket;

  getPriorityTicket?: GetPriorityTicket;
  getGraphTicket?: GetGraphTicket;
  getDueTicket?: GetDueTicket;

  postTicket?: TDepartmentalTicketResponse;
  putTicket?: TDepartmentalTicketResponse;
  destroyTicket?: LoadingResult;
  departmentUsers?: ITicketDepartmentUsers[];
  getDepartmentUsers?: LoadingResult;
  postAssignTicketUser?: LoadingResult;
  getTicketAssigneeDetails?: LoadingResult;
  getTicketAssigneeInfo?: LoadingResult;
  getDepartments?: LoadingResult;
  putTicketExtendDate?: LoadingResult;
  postTicketForward?: LoadingResult;
  putUpdateTicketStatus?: LoadingResult;
  postTicketComment?: LoadingResult;
  postTicketCommentAttachment?: LoadingResult;
  postUpdateTicketApproval?: LoadingResult;
  getExportReport?: LoadingResult;
};

// The new Get function for Tickets
export type GetTicket = LoadingResult & {
  data?: TicketResponse;
};
export type GetDueTicket = LoadingResult & {
  data?: DueTicketResponse;
};
export type GetGraphTicket = LoadingResult & {
  data?: GraphTicketResponse;
};
export type GetTicketsInfo = LoadingResult & {
  data?: TicketAssigneeResponse;
}; 

export type GetPriorityTicket = GetTicket;

// For the Loading page
export type TDepartmentalTicketResponse = LoadingResult & {
  data?: ITicketInterface;
};

// Define the structure for Ticket data
export type TDepartmentalTicketPayload = {
  id?: number;
  subject: string;
  description: string;
  createdAt?: string;
  createdBy?: IUser;
  assignedToId?: number;
  fromDepartmentId: number;
  toDepartmentId: number;
  requestTypeId: number;
  issueType: string;
  coopId?: number;
  deviceId?: number;
  operatingSystemId?: number;
  applicationId?: number;
  isUrgent: boolean;
  priorityLevel?: string;
  closureStatus?: string;
  status?: string;
  startDate?: string;
  completionDate?: string;
  expectedCompletionDate?: string;
  extensionDate?: string;
  extensionRemarks?: string;
  formType?: string;
  attachments?: IAttachments;
  hasTopManApproval?: boolean;
  ticketAssignees?: ITicketInterface;
  ticketComments?: ITicketInterface;
  requestType?: ITicketInterface;
  latestTicketAssignee?: ITicketInterface;
};

//For form status
export type TUpdateFormStatusPayload = {
  status: string;
  remarks?: string;
};

// Payload type for deleting a Ticket
export type TDepartmentalTicketWithIDAndIndexPayload = {
  id: number;
  index: number;
};

export type TIDepartmentalTicketDataAndIndexPayload = {
  data: ITicketInterface;
  index: number;
};

export type TDepartmentalTicketDataAndIndexPayload = {
  data: TDepartmentalTicketPayload;
  index: number;
};

//For filter reasons
export type TGetTicketsWithFilterActionPayload = PayloadAction<{
  filter: string;
  filterStatus?: string;
  eq?: boolean;
  page?: number;
  rows?: number;
  sort?: string;
  relations?: string;
  id?: number | string;
}>;

// Post and Put
export type TDepartmentalTicketActionPayloadPostPut = PayloadAction<TDepartmentalTicketPayload>;
// Post Success
export type TIDepartmentalTicketActionPayloadPostSuccess = PayloadAction<ITicketInterface>;
// Selected Data and Put Success
export type TIDepartmentalTicketActionPayloadSelectedPutSuccess = PayloadAction<TIDepartmentalTicketDataAndIndexPayload>;
// Destroy
export type TDepartmentalTicketDelete = PayloadAction<TDepartmentalTicketWithIDAndIndexPayload>;

export type TDepartmentsState = {
  departments: IDepartment[];
  getDepartments: LoadingResult;
};

export type TTicketDepartmentUsersPayload = {
  departmentId: number;
  relations?: string;
};

export type TTicketDepartmentUsers = PayloadAction<TTicketDepartmentUsersPayload>;

export interface TAssignTicketUserPayload {
  ticketId: number;
  userId: number;
}

