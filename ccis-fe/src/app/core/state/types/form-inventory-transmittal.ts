import { PayloadAction } from "@reduxjs/toolkit";
import { IAttachments, LoadingResult } from "@interface/common.interface";
import {
  IApproval,
  IFormTransmittal,
  IFormTransmittalResponse,
  IFormTransmittalOutgoingPayload,
  IPadAssignments,
  IPadAssignmentsWithDetails,
  IPaymentDetails,
} from "@interface/form-inventory.interface";
import { IUser } from "@interface/user.interface";
import { TableState } from "./common-table-types";

// State type for managing incomingReceivedForms
export type TTransmittalFormState = {
  transmittalForms: IFormTransmittal[];
  // currentUserFormTransmittalTrail: GetTransmittal;
  selectedTransmittalForm: TTransmittalFormPayloadWithIndex;
  // getTransmittalForms: LoadingResult;
  getTransmittalForms: GetTransmittal;
  getTransmittalForm: LoadingResult;
  postTransmittalForm?: TTransmittalFormResponse;
  FormAttachment?: IAttachments[];
  getCurrentUserFormTransmittalTrail: GetTransmittal;
  getLatestTransmittalForm: LoadingResult;
  postFormTransmittalTrail: TIFormTransmittalOutgoingResponse;
  getTransmittalTrailActivityLog: LoadingResult;
  getTransmittalFormTrail: LoadingResult;
  getTransmittalFormToClifsaPrint?: TransmittalFormToClifsaPrintResponsePdf;
  postReturnedPads: LoadingResult;
  padAssignments?: IPadAssignmentsWithDetails[];
  getPadAssignments?: GetPadAssignments;
  userPadAssignment: TableState<IPadAssignments[]>;
  userPadAssignmentById?: LoadingResult;
  userPadSeriesDetails?: LoadingResult;
  putCancelReceipt?: LoadingResult;
  putIssueReceipt?: LoadingResult;
  getReturnedPads: GetTransmittal;
  getReturnedTransmittalTrail: LoadingResult;
  putTransmittalTrail?: LoadingResult;
};
export type TransmittalFormToClifsaPrintResponsePdf = LoadingResult & {
  pdfUrl?: string | null;
};

// Extending LoadingResult type to include a single IMasterlist data
export type TTransmittalFormResponse = LoadingResult & {
  data?: IFormTransmittal; // Optional masterlist data
};
export type TIFormTransmittalOutgoingResponse = LoadingResult & {
  data?: IFormTransmittalOutgoingPayload;
};

// Define the structure for TransmittalForm data
export type TTransmittalFormPayload = {
  id: number;
  divisionId: number;
  formTypeId: number;
  areaId: number;
  receivedDate: string;
  seriesFrom: number;
  seriesTo: number;
  atpNumber: string;
  withBirAttachment?: number;
  padAssignments?: IPadAssignments[];
  noPads: number;
  attachments: IAttachments[];
};

// Payload type for creating or updating a incomingReceivedForm
export type IFormTransmittalPayload = {
  id?: number;
  transmittalNumber?: number;
  releasedAreaId?: number;
  releasedToId?: number;
  withBirAttachment?: number;
  padAssignments?: IPadAssignments[];
  remarks?: string;
  status?: string;
  areaId?: number;
  approval?: IApproval;
  createdAt?: string;
  createdBy?: IUser;
  updatedAt?: string;
};

export type ITransmittalLetterPayload = {
  formTransmittalId: number;
};

export type ITransmittalReportPayload = {
  formTransmittalIds: number;
};

export type TUpdateFormStatusPayload = {
  status: string;
  remarks?: string;
};

export type TFormTransmittalOutgoingPayload = {
  id: number;
  releasedArea?: number;
  releasedBy?: string;
  releasedTo?: number;
  status?: string;
  releasedMethodId?: number;
  trackingNo?: string;
  deliveredBy?: string;
};

export type TTransmittalFormTrailPayload = {
  id?: number;
  formTransmittalId?: number;
  status?: string;
  deliveredBy?: string;
  receivedAt?: string;
  releasedMethodId: number;
  trackingNo?: string;
  createdAt?: string;
};

export type TTransmittalFormTrailStatusPayload = {
  formTransmittalId?: number;
  status: string;
};

export type TCreateReturnedPayload = {
  releasedArea: number;
  releasedTo: number;
  remarks?: string;
  status: string;
  padAssignment: IPadAssignments[];
  formTransmittalTrail: TFormTransmittalTrailPayload[];
};

export type TFormTransmittalTrailPayload = {
  id?: number;
  formTransmittalId?: number;
  status?: string;
  deliveredBy?: string;
  receivedAt?: string;
  releasedMethodId?: number;
  trackingNo?: string;
  createdAt?: string;
  releasedTo?: number;
};

// Payload type for deleting a incomingReceivedForm
export type IFormTransmittalWithIndexPayload = {
  id: number;
  index?: number;
};

export type TGetTransmittalFormsWithFilterActionPayload = PayloadAction<{
  filter: string;
  id?: number | string;
}>;

// Defining payload that handles TMasterlistPayload type as data
export type TTransmittalFormPayloadWithIndex = {
  data: IFormTransmittalPayload;
  index: number;
};

export type TIFormTransmittalWithIndexPayload = {
  filter: string;
  id: number;
};

export type GetTransmittal = LoadingResult & {
  data?: IFormTransmittalResponse;
};

export type GetPadAssignments = LoadingResult & {
  data?: IPadAssignmentsWithDetails;
};

export type TIssueReceipt = {
  id: number;
  seriesNo: number;
  padAssignmentId: number;
  issuedBy: number;
  remitTo: number;
  productId: number;
  cooperativeId: number;
  status: string;
  releasedAt: string;
  attachments: IAttachments[];
  paymentDetail: IPaymentDetails;
};

export type TCancelReceipt = {
  id: number;
  remarks: string;
  status: string;
  cancelledAt: string;
  attachments: IAttachments[];
};

export type TUpdateTransmitalStatus = {
  id: number;
  status: string;
};

export type TUpdateTransmitalStatusWithActionPayload = PayloadAction<TUpdateTransmitalStatus>;
export type TIssueReceiptWithActionPayload = PayloadAction<TIssueReceipt>;
export type TCancelReceiptWithActionPayload = PayloadAction<TCancelReceipt>;

export type TTransmittalFormToClifsaPrintWithIdActionPayload = PayloadAction<{
  id?: number | string;
  formTransmittalId?: number | string;
}>;
// Action payloads for incomingReceivedForms
export type TTransmittalFormPayloadAction = PayloadAction<TTransmittalFormPayload>;
// export type TTransmittalFormActionPayload = PayloadAction<{ data?: IFormTransmittal[] }>;
export type TIFormTransmittalActionPayload = PayloadAction<IFormTransmittalPayload>;
export type TTransmittalFormActionPayloadIFormTransmittal = PayloadAction<TTransmittalFormPayload>;

export type TTransmittalFormWithIndexActionPayload = PayloadAction<TTransmittalFormPayloadWithIndex>;
export type TTransmittalFormIDPayloadActionPayload = PayloadAction<IFormTransmittalWithIndexPayload>;
export type TGetTransmittalFormWithFilterActionPayload = PayloadAction<{
  filter?: string;
  id?: number;
}>;

export type TFormTransmittalOutgoingPayloadPostPut = PayloadAction<TFormTransmittalOutgoingPayload>;
export type TIFormTransmittalOutgoingPayloadPostSuccess = PayloadAction<IFormTransmittalOutgoingPayload>;

export type TCreateReturnedPayloadAction = PayloadAction<TCreateReturnedPayload>;

export type TIReturnedPadsPayloadAction = PayloadAction<IFormTransmittal>;
