import { createDynamicState } from "@helpers/array";
import { IDefaultParams } from "@interface/common.interface";
import { ICooperative, IProductProposal, IProvisionApproval } from "@interface/product-proposal.interface";
import { bindActionCreators, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { TIProductProposalCommisionActionPayloadPostPut, TProductProposalState } from "@state/types/product-proposal";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";

const initialState: TProductProposalState = {
  step: 0,
  isEditedGuidelines: false,
  customType: undefined,
  proposedProduct: undefined,
  managementPercentFee: 0,
  productProposals: [],
  productProposalNotarizations: [],
  productProposalProvisions: [],
  updatePartnershipAgreement: undefined,
  cooperatives: [],
  cooperative: undefined,
  cdaCoopearative: undefined,
  cdaCooperatives: [],
  getProductProposal: {
    data: undefined,
    loading: false,
    success: false,
    error: false,
  },
  postProductProposal: {
    loading: false,
    success: false,
    error: false,
  },
  putProductProposal: {
    loading: false,
    success: false,
    error: false,
  },
  getCooperatives: {
    loading: false,
    success: false,
    error: false,
  },
  getCdaCooperatives: {
    loading: false,
    success: false,
    error: false,
  },
  getProductProposalNotarization: {
    loading: false,
    success: false,
    error: false,
  },
  postProductProposalsNotarizations: {
    loading: false,
    success: false,
    error: false,
  },
  getMarketingProductProposals: {
    data: undefined,
    loading: false,
    success: false,
    error: false,
  },
  getProductProposalProvisions: {
    loading: false,
    success: false,
    error: false,
  },
  getProposal: createDynamicState(),
  postProposalCommission: createDynamicState(),
  putProductProposalCommission: createDynamicState(),
};

const productsProposallice = createSlice({
  name: "productProposal",
  initialState,
  reducers: {
    reset(state) {
      state.cooperative = initialState.cooperative;
      state.cooperatives = initialState.cooperatives;
      state.step = initialState.step;
      state.managementPercentFee = initialState.managementPercentFee;
      state.postProductProposal = initialState.postProductProposal;
      state.putProductProposal = initialState.putProductProposal;
      state.proposedProduct = initialState.proposedProduct;
      state.isEditedGuidelines = initialState.isEditedGuidelines;
    },
    clearProposalCommission(state) {
      state.postProposalCommission = createDynamicState();
    },
    clearProposal(state) {
      state.getProposal = createDynamicState();
    },
    setCustomType(state, action: PayloadAction<string | undefined>) {
      state.customType = action.payload;
    },
    setProposedProduct(state, action: PayloadAction<IProductProposal>) {
      state.proposedProduct = { ...action.payload };
    },
    setIsEditedGuidelines(state, action: PayloadAction<boolean>) {
      state.isEditedGuidelines = action.payload;
    },
    setStep(state, action: PayloadAction<number>) {
      state.step = action.payload;
    },
    setCooperative(state, action: PayloadAction<ICooperative>) {
      state.cooperative = action.payload;
    },
    setManagementFee(state, action: PayloadAction<number>) {
      state.managementPercentFee = action.payload;
    },
    setCdaCooperative(state, action: PayloadAction<ICooperative>) {
      state.cdaCoopearative = action.payload;
    },
    getProductProposal(state, _action: PayloadAction<IDefaultParams>) {
      state.getProductProposal = {
        loading: true,
        success: false,
        error: false,
      };
    },
    getProductProposalSuccess(state, action) {
      state.productProposals = action.payload.data;
      state.getProductProposal = {
        data: action.payload,
        loading: false,
        success: true,
        error: false,
      };
    },
    getProductProposalFailure(state) {
      state.getProductProposal = {
        loading: false,
        success: false,
        error: true,
      };
    },
    postProductProposal(state, _action) {
      state.postProductProposal = {
        loading: true,
        success: false,
        error: false,
      };
    },
    postProductProposalSuccess(state, action: PayloadAction<IProductProposal>) {
      state.proposedProduct = action.payload;
      state.postProductProposal = {
        loading: false,
        success: true,
        error: false,
      };
    },
    postProductProposalFailure(state) {
      state.postProductProposal = {
        loading: false,
        success: false,
        error: true,
      };
    },
    putProductProposal(state, _action) {
      state.putProductProposal = {
        loading: true,
        success: false,
        error: false,
      };
    },
    putProductProposalSuccess(state, action: PayloadAction<{ productProposal: IProductProposal } & { index: number }>) {
      state.productProposals[action.payload.index] = action.payload.productProposal;
      state.putProductProposal = {
        loading: false,
        success: true,
        error: false,
      };
    },
    putProductProposalFailure(state) {
      state.putProductProposal = {
        loading: false,
        success: false,
        error: true,
      };
    },
    getCooperatives(state, _action: PayloadAction<IDefaultParams>) {
      state.getCooperatives = {
        loading: true,
        success: false,
        error: false,
      };
    },
    getCooperativesSuccess(state, action: PayloadAction<ICooperative[]>) {
      state.cooperatives = action.payload;
      state.getCooperatives = {
        loading: false,
        success: true,
        error: false,
      };
    },
    getCooperativesFailure(state) {
      state.getCooperatives = {
        loading: false,
        success: false,
        error: true,
      };
    },
    getCdaCooperatives(state, _action: PayloadAction<IDefaultParams>) {
      state.getCdaCooperatives = {
        loading: true,
        success: false,
        error: false,
      };
    },
    getCdaCooperativesSuccess(state, action: PayloadAction<ICooperative[]>) {
      state.cdaCooperatives = action.payload;
      state.getCdaCooperatives = {
        loading: false,
        success: true,
        error: false,
      };
    },
    getCdaCooperativesFailure(state) {
      state.getCdaCooperatives = {
        loading: false,
        success: false,
        error: true,
      };
    },
    getProductProposalNotarization(state, _action: PayloadAction<IDefaultParams>) {
      state.getProductProposalNotarization = {
        loading: true,
        success: false,
        error: false,
      };
    },
    getProductProposalNotarizationSuccess(state, action) {
      state.productProposalNotarizations = action.payload;
      state.getProductProposalNotarization = {
        loading: false,
        success: true,
        error: false,
      };
    },
    getProductProposalNotarizationFailure(state) {
      state.getProductProposalNotarization = {
        loading: false,
        success: false,
        error: true,
      };
    },
    getMarketingProductProposals(state, _action: PayloadAction<IDefaultParams>) {
      state.getMarketingProductProposals = {
        loading: true,
        success: false,
        error: false,
      };
    },
    getMarketingProductProposalsSuccess(state, action) {
      state.getMarketingProductProposals = {
        data: action.payload,
        loading: false,
        success: true,
        error: false,
      };
    },
    getMarketingProductProposalsFailure(state) {
      state.getMarketingProductProposals = {
        loading: false,
        success: false,
        error: true,
      };
    },
    getUnderwritingProductProposalProvisions(state, _action: PayloadAction<IDefaultParams>) {
      state.getProductProposalProvisions = {
        loading: true,
        success: false,
        error: false,
      };
    },
    getUnderwritingProductProposalProvisionsSuccess(state, action) {
      state.productProposalProvisions = action.payload;
      state.getProductProposalProvisions = {
        loading: false,
        success: true,
        error: false,
      };
    },
    getUnderwritingProductProposalProvisionsFailure(state) {
      state.getProductProposalProvisions = {
        loading: false,
        success: false,
        error: true,
      };
    },
    getClaimsProductProposalProvisions(state, _action: PayloadAction<IDefaultParams>) {
      state.getProductProposalProvisions = {
        loading: true,
        success: false,
        error: false,
      };
    },
    getClaimsProductProposalProvisionsSuccess(state, action) {
      state.productProposalProvisions = action.payload;
      state.getProductProposalProvisions = {
        loading: false,
        success: true,
        error: false,
      };
    },
    getClaimsProductProposalProvisionsFailure(state) {
      state.getProductProposalProvisions = {
        loading: false,
        success: false,
        error: true,
      };
    },
    setSignatory(state, action: PayloadAction<IProvisionApproval>) {
      state.signatory = action.payload;
    },
    getProposal(state, _action: PayloadAction<IDefaultParams>) {
      state.getProposal = createDynamicState(["loading"]);
    },
    getProposalSuccess(state, action) {
      state.getProposal = createDynamicState(["success"], action.payload);
    },
    getProposalFailure(state) {
      state.getProposal = createDynamicState(["error"]);
    },

    //for post commission structure
    postProposalCommission(state, _action: TIProductProposalCommisionActionPayloadPostPut) {
      state.postProposalCommission = createDynamicState(["loading"]);
      toast.success("Signatory Template Selected Successfully!");
    },
    postProposalCommissionSuccess(state, action: TIProductProposalCommisionActionPayloadPostPut) {
      state.postProposalCommission = createDynamicState(["success"], action.payload);
    },
    postProposalCommissionFailure(state) {
      state.postProposalCommission = createDynamicState(["error"]);
    },
    // to update commission
    putProductProposalCommission(state, _action) {
      state.putProductProposalCommission = createDynamicState(["loading"]);
    },
    putProductProposalCommissionSuccess(state, action: PayloadAction<{ data: number } & { index: number }>) {
      state.putProductProposalCommission = createDynamicState(["success"], action.payload);
    },
    putProductProposalCommissionFailure(state) {
      state.putProductProposalCommission = createDynamicState(["error"]);
    },
  },
});

export const {
  getProductProposal,
  getProductProposalSuccess,
  getProductProposalFailure,
  postProductProposal,
  postProductProposalSuccess,
  postProductProposalFailure,
  putProductProposal,
  putProductProposalSuccess,
  putProductProposalFailure,
  getCooperatives,
  getCooperativesSuccess,
  getCooperativesFailure,
  getCdaCooperatives,
  getCdaCooperativesSuccess,
  getCdaCooperativesFailure,
  getProductProposalNotarization,
  getProductProposalNotarizationSuccess,
  getProductProposalNotarizationFailure,
  setCooperative,
  setStep,
  reset,
  setManagementFee,
  setCdaCooperative,
  setProposedProduct,
  getMarketingProductProposals,
  getMarketingProductProposalsSuccess,
  getMarketingProductProposalsFailure,
  setCustomType,
  getUnderwritingProductProposalProvisions,
  getUnderwritingProductProposalProvisionsSuccess,
  getUnderwritingProductProposalProvisionsFailure,
  setSignatory,
  getClaimsProductProposalProvisions,
  getClaimsProductProposalProvisionsSuccess,
  getClaimsProductProposalProvisionsFailure,
  setIsEditedGuidelines,
  getProposal,
  getProposalSuccess,
  getProposalFailure,
  clearProposalCommission,
  postProposalCommission,
  postProposalCommissionSuccess,
  postProposalCommissionFailure,
  putProductProposalCommission,
  putProductProposalCommissionSuccess,
  putProductProposalCommissionFailure,
  clearProposal,
} = productsProposallice.actions;

export const useProductProposalActions = () => {
  const dispatch = useDispatch();
  return bindActionCreators(
    {
      getProductProposal,
      postProductProposal,
      putProductProposal,
      getCooperatives,
      getCdaCooperatives,
      getProductProposalNotarization,
      setCooperative,
      setStep,
      reset,
      setManagementFee,
      setCdaCooperative,
      setProposedProduct,
      getMarketingProductProposals,
      setCustomType,
      setSignatory,
      getUnderwritingProductProposalProvisions,
      getClaimsProductProposalProvisions,
      setIsEditedGuidelines,
      getProposal,
      postProposalCommission,
      clearProposalCommission,
      putProductProposalCommission,
      clearProposal,
    },
    dispatch
  );
};

export default productsProposallice.reducer;
