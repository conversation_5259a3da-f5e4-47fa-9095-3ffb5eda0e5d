import { createDynamicState } from "@helpers/array";
import { bindActionCreators, createSlice } from "@reduxjs/toolkit";
import {
  TIDepartmentalTicketActionPayloadPostSuccess,
  TDepartmentalTicketActionPayloadPostPut,
  TDepartmentalTicketDelete,
  TTicketManagementState,
  TTicketDepartmentUsersPayload,
  TAssignTicketUserPayload,
} from "@state/types/departmental-ticketing";
import { useDispatch } from "react-redux";

//For Optimization of the Get
import { IDefaultParams } from "@interface/common.interface";
import { PayloadAction } from "@reduxjs/toolkit";
import { IDepartment, IExportReportPayload, IForwardTicketPayload, ISaveAttachmentsPayload, TITicketExtendDatePayload } from "@interface/departmental-ticketing-interface";
import { toast } from "react-toastify";
import { UpdateTicketApprovalPayload } from "@interface/update-ticket-approval";
import Swal from "sweetalert2";
import { showSuccess } from "@helpers/prompt";
import { showProcessingToast, showSuccessToast } from "@modules/dashboard/RequestorDashboard/components/prompts/DepartmentalTicketingPrompts";

const initialState: TTicketManagementState = {
  tickets: [],
  ticketsInfo: [],
  priorityTickets: [],
  dueTickets: [],
  graphTickets: [],
  selectedTicket: null,
  departmentUsers: [],

  getTicket: createDynamicState(),
  getPriorityTicket: createDynamicState(),
  getGraphTicket: createDynamicState(),
  getDueTicket: createDynamicState(),
  postTicket: createDynamicState(),
  putTicket: createDynamicState(),
  destroyTicket: createDynamicState(),
  getDepartmentUsers: createDynamicState(),
  postAssignTicketUser: createDynamicState(),
  getTicketAssigneeDetails: createDynamicState(),
  getTicketsInfo: createDynamicState(),
  getDepartments: createDynamicState(),
  putTicketExtendDate: createDynamicState(),
  postTicketForward: createDynamicState(),
  putUpdateTicketStatus: createDynamicState(),
  postTicketComment: createDynamicState(),
  postTicketCommentAttachment: createDynamicState(),
  postUpdateTicketApproval: createDynamicState(),
  getExportReport: createDynamicState(),
};

const departmentalTicketSlice = createSlice({
  name: "departmentalTicket",
  initialState,
  reducers: {
    clearSelectedTicket(state) {
      state.selectedTicket = initialState.selectedTicket;
    },
    getTicket(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.getTicket = createDynamicState(["loading"]);
    },
    getTicketSuccess(state, action) {
      state.tickets = action.payload.data.reverse();
      state.getTicket = createDynamicState(["success"], action.payload);
    },
    getTicketFailure(state) {
      state.getTicket = createDynamicState(["error"]);
    },

    getPriorityTicket(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.getPriorityTicket = createDynamicState(["loading"]);
    },
    getPriorityTicketSuccess(state, action) {
      state.priorityTickets = action.payload.data.reverse();
      state.getPriorityTicket = createDynamicState(["success"], action.payload);
    },
    getPriorityTicketFailure(state) {
      state.getPriorityTicket = createDynamicState(["error"]);
    },

    getDueTicket(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.getDueTicket = createDynamicState(["loading"]);
    },
    getDueTicketSuccess(state, action) {
      state.dueTickets = action.payload.data.reverse();
      state.getDueTicket = createDynamicState(["success"], action.payload);
    },
    getDueTicketFailure(state) {
      state.getDueTicket = createDynamicState(["error"]);
    },

    getGraphTicket(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.getGraphTicket = createDynamicState(["loading"]);
    },
    getGraphTicketSuccess(state, action) {
      state.graphTickets = action.payload.data.reverse();
      state.getGraphTicket = createDynamicState(["success"], action.payload);
    },
    getGraphTicketFailure(state) {
      state.getGraphTicket = createDynamicState(["error"]);
    },
    getExportReport(state, _action: PayloadAction<IExportReportPayload>) {
      state.getExportReport = createDynamicState(["loading"]);
    },
    getExportReportSuccess(state, action) {
      state.getExportReport = createDynamicState(["success"], action.payload);
    },
    getExportReportFailure(state) {
      state.getExportReport = createDynamicState(["error"]);
    },

    postTicket(state, _action: TDepartmentalTicketActionPayloadPostPut) {
      state.postTicket = createDynamicState(["loading"]);
      4;
    },
    postTicketSuccess(state, action: TIDepartmentalTicketActionPayloadPostSuccess) {
      state.tickets.unshift(action.payload);
      state.postTicket = createDynamicState(["success"]);
      showSuccess();
    },
    postTicketFailure(state) {
      state.postTicket = createDynamicState(["error"]);
    },

    putTicket(state, _action: TDepartmentalTicketActionPayloadPostPut) {
      state.putTicket = createDynamicState(["loading"]);
    },

    putTicketFailure(state) {
      state.putTicket = createDynamicState(["error"]);
    },

    destroyTicket(state, _action: TDepartmentalTicketDelete) {
      state.destroyTicket = createDynamicState(["loading"]);
    },
    destroyTicketSuccess(state, action) {
      state.tickets?.splice(action.payload, 1);
      state.destroyTicket = createDynamicState(["success"]);
      showSuccess("Ticket deleted successfully.");
    },
    destroyTicketFailure(state) {
      state.destroyTicket = createDynamicState(["error"]);
    },
    getDepartmentUsers(state, _action: PayloadAction<TTicketDepartmentUsersPayload>) {
      state.getDepartmentUsers = createDynamicState(["loading"]);
    },
    getDepartmentUsersSuccess(state, action) {
      state.departmentUsers = action.payload;
      state.getDepartmentUsers = createDynamicState(["success"]);
    },
    getDepartmentUsersFailure(state) {
      state.getDepartmentUsers = createDynamicState(["error"]);
      toast.error("Failed to load department users. Please try again.");
    },
    postAssignTicketUser(state, _action: PayloadAction<TAssignTicketUserPayload>) {
      state.postAssignTicketUser = createDynamicState(["loading"]);
      // Show processing toast when request starts
      showProcessingToast("Processing ticket assignment...");
    },
    postAssignTicketUserSuccess(state, action) {
      state.postAssignTicketUser = createDynamicState(["success"], action.payload);
      // Close any existing processing toast and show success
      Swal.close();
      showSuccessToast("Successfully assigned ticket to user");
    },
    postAssignTicketUserFailure(state) {
      state.postAssignTicketUser = createDynamicState(["error"]);
    },

    getTicketAssigneeDetails(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.getTicketAssigneeDetails = createDynamicState(["loading"]);
    },
    getTicketAssigneeDetailsSuccess(state, action) {
      state.selectedTicket = action.payload;
      state.getTicketAssigneeDetails = createDynamicState(["success"], action.payload);
    },
    getTicketAssigneeDetailsFailure(state) {
      state.getTicketAssigneeDetails = createDynamicState(["error"]);
    },
    clearTicketAssigneeDetails(state) {
      state.getTicketAssigneeDetails = createDynamicState();
    },
    getTicketsInfo(state, _action: PayloadAction<{ params: IDefaultParams }>) {
      state.getTicketsInfo = createDynamicState(["loading"]);
    },
    getTicketsInfoSuccess(state, action) {
      state.ticketsInfo = action.payload.data.reverse();
      state.getTicketsInfo = createDynamicState(["success"], action.payload);
    },
    getTicketsInfoFailure(state) {
      state.getTicketsInfo = createDynamicState(["error"]);
    },
    putTicketExtendDate(state, _action: PayloadAction<{ params: TITicketExtendDatePayload }>) {
      state.putTicketExtendDate = createDynamicState(["loading"]);
    },
    putTicketExtendDateSuccess(state, action) {
      state.putTicketExtendDate = createDynamicState(["success"], action.payload);
      toast.success("Ticket extension date updated successfully.");
    },
    putTicketExtendDateFailure(state) {
      state.putTicketExtendDate = createDynamicState(["error"]);
    },
    postForwardTicket(state, _action: PayloadAction<IForwardTicketPayload>) {
      state.postTicketForward = createDynamicState(["loading"]);
      // Show processing toast when request starts
      showProcessingToast("Processing ticket forwarding...");
    },
    postForwardTicketSuccess(state, action) {
      state.postTicketForward = createDynamicState(["success"], action.payload);
      Swal.close();
      showSuccessToast("Ticket forwarded successfully");
    },
    postForwardTicketFailure(state) {
      state.postTicketForward = createDynamicState(["error"]);
    },
    postForwardTicketReset(state) {
      state.postTicketForward = createDynamicState();
    },
    getDepartments(state) {
      state.getDepartments = createDynamicState(["loading"]);
    },
    getDepartmentsSuccess(state, action: PayloadAction<IDepartment>) {
      state.getDepartments = createDynamicState(["success"], action.payload);
    },
    getDepartmentsFailure(state) {
      state.getDepartments = createDynamicState(["error"]);
    },
    putUpdateTicketStatus(state, _action: PayloadAction<{ id: number; status: string }>) {
      state.putUpdateTicketStatus = createDynamicState(["loading"]);
    },
    putUpdateTicketStatusSuccess(state, action) {
      state.putUpdateTicketStatus = createDynamicState(["success"], action.payload);
      toast.success("Ticket status updated successfully.");
    },
    putUpdateTicketStatusFailure(state) {
      state.putUpdateTicketStatus = createDynamicState(["error"]);
    },
    postTicketComment(state, _action: PayloadAction<{ ticketId: number; comment: string }>) {
      state.postTicketComment = createDynamicState(["loading"]);
    },
    postTicketCommentSuccess(state, action) {
      state.postTicketComment = createDynamicState(["success"], action.payload);
      postTicketCommentReset();
      
    },
    postTicketCommentFailure(state) {
      state.postTicketComment = createDynamicState(["error"]);
      toast.error("Failed to post comment. Please try again.");
    },
    postTicketCommentReset(state) {
      state.postTicketComment = createDynamicState();
    },
    postTicketCommentAttachment(state, _action: PayloadAction<ISaveAttachmentsPayload>) {
      state.postTicketCommentAttachment = createDynamicState(["loading"]);
    },
    postTicketCommentAttachmentSuccess(state, action) {
      state.postTicketCommentAttachment = createDynamicState(["success"], action.payload);
      toast.success("Attachments saved successfully!");
    },
    postTicketCommentAttachmentFailure(state) {
      state.postTicketCommentAttachment = createDynamicState(["error"]);
    },
    postUpdateTicketApproval(state, _action: PayloadAction<{ ticketId: number; payload: UpdateTicketApprovalPayload }>) {
      state.postUpdateTicketApproval = createDynamicState(["loading"]);
      // Show processing toast when request starts
      showProcessingToast("Processing ticket approval...");
    },
    postUpdateTicketApprovalSuccess(state, action) {
      state.postUpdateTicketApproval = createDynamicState(["success"], action.payload);
      // Close any existing processing toast and show success
      Swal.close();
      showSuccessToast("Ticket Approval request submitted successfully");
    },
    postUpdateTicketApprovalFailure(state) {
      state.postUpdateTicketApproval = createDynamicState(["error"]);
    },
  },
});

export const {
  clearSelectedTicket,
  getTicket,
  getTicketSuccess,
  getTicketFailure,
  getPriorityTicket,
  getPriorityTicketSuccess,
  getPriorityTicketFailure,
  getDueTicket,
  getDueTicketSuccess,
  getDueTicketFailure,
  getGraphTicket,
  getGraphTicketSuccess,
  getGraphTicketFailure,
  postTicket,
  postTicketSuccess,
  postTicketFailure,
  putTicket,
  putTicketFailure,
  destroyTicket,
  destroyTicketSuccess,
  destroyTicketFailure,
  getDepartmentUsers,
  getDepartmentUsersSuccess,
  getDepartmentUsersFailure,
  postAssignTicketUser,
  postAssignTicketUserSuccess,
  postAssignTicketUserFailure,
  getTicketAssigneeDetails,
  getTicketAssigneeDetailsSuccess,
  getTicketAssigneeDetailsFailure,
  getTicketsInfo,
  getTicketsInfoSuccess,
  getTicketsInfoFailure,
  putTicketExtendDate,
  putTicketExtendDateSuccess,
  putTicketExtendDateFailure,
  postForwardTicket,
  postForwardTicketSuccess,
  postForwardTicketFailure,
  postForwardTicketReset,
  getDepartments,
  getDepartmentsSuccess,
  getDepartmentsFailure,
  putUpdateTicketStatus,
  putUpdateTicketStatusSuccess,
  putUpdateTicketStatusFailure,
  postTicketComment,
  postTicketCommentSuccess,
  postTicketCommentFailure,
  postTicketCommentReset,
  postTicketCommentAttachment,
  postTicketCommentAttachmentSuccess,
  postTicketCommentAttachmentFailure,
  postUpdateTicketApproval,
  postUpdateTicketApprovalSuccess,
  postUpdateTicketApprovalFailure,
  clearTicketAssigneeDetails,
  getExportReport,
  getExportReportSuccess,
  getExportReportFailure,
} = departmentalTicketSlice.actions;

export const useTicketActions = () => {
  return bindActionCreators(
    {
      clearSelectedTicket,
      getTicket,
      getPriorityTicket,
      getGraphTicket,
      getDueTicket,
      postTicket,
      putTicket,
      destroyTicket,
      getDepartmentUsers,
      postAssignTicketUser,
      getTicketAssigneeDetails,
      getTicketsInfo,
      putTicketExtendDate,
      postForwardTicket,
      postForwardTicketReset,
      getDepartments,
      putUpdateTicketStatus,
      postTicketComment,
      postTicketCommentAttachment,
      postTicketCommentReset,
      postUpdateTicketApproval,
      clearTicketAssigneeDetails,
      getExportReport,
    },
    useDispatch()
  );
};

export default departmentalTicketSlice.reducer;
