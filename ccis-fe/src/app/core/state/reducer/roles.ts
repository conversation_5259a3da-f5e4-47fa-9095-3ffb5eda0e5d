import { bindActionCreators, createSlice } from "@reduxjs/toolkit";
import { TRolesActionPayload, TRolesState, TRolesIDPayloadActionPayload, TRolesActionPayloadIrole } from "@state/types/roles";
import { useDispatch } from "react-redux";
import { showSuccess } from "@helpers/prompt";
import { IRole } from "@interface/roles.interface";

const initialState: TRolesState = {
  roles: [],
  role: {
    id: "",
    name: "",
    description: "",
    guardName: "",
    createdAt: "",
    updatedAt: "",
    permissionIds: [],
  },
  getRoles: {
    loading: false,
    success: false,
    error: false,
  },
  postRole: {
    loading: false,
    success: false,
    error: false,
  },
  putRole: {
    loading: false,
    success: false,
    error: false,
  },
  destroyRole: {
    loading: false,
    success: false,
    error: false,
  },
};

const rolesSlice = createSlice({
  name: "roles",
  initialState,
  reducers: {
    clearRole(state) {
      state.role = initialState.role;
    },
    getRoles(state, _action: TRolesIDPayloadActionPayload) {
      state.getRoles = {
        loading: true,
        success: false,
        error: false,
      };
    },
    getRolesSuccess(state, actions: TRolesActionPayload) {
      state.roles = actions.payload.data;
      state.getRoles = {
        loading: false,
        success: true,
        error: false,
      };
    },
    getRolesFailure(state) {
      state.getRoles = {
        loading: false,
        success: false,
        error: true,
      };
    },
    getRole(state, _actions: TRolesIDPayloadActionPayload) {
      state.getRoles = {
        loading: true,
        success: false,
        error: false,
      };
    },
    getRoleSuccess(state, actions: TRolesActionPayloadIrole) {
      state.role = actions.payload;
      state.getRoles = {
        loading: false,
        success: true,
        error: false,
      };
    },
    getRoleFailure(state) {
      state.getRoles = {
        loading: false,
        success: false,
        error: true,
      };
    },
    postRoles(state, _actions: TRolesActionPayloadIrole) {
      state.postRole = {
        loading: true,
        success: false,
        error: false,
      };
    },
    postRolesSuccess(state) {
      state.postRole = {
        loading: false,
        success: true,
        error: false,
      };
      showSuccess();
    },
    postRolesFailure(state) {
      state.postRole = {
        loading: false,
        success: false,
        error: true,
      };
    },
    postRolesUpdate(state, actions) {
      if (!state.roles) state.roles = [];
      state.roles.push(actions.payload);
    },
    putRoles(state, _actions: TRolesActionPayloadIrole) {
      state.putRole = {
        loading: true,
        success: false,
        error: false,
      };
    },
    putRolesSuccess(state) {
      state.putRole = {
        loading: false,
        success: true,
        error: false,
      };
      showSuccess();
    },
    putRolesFailure(state) {
      state.putRole = {
        loading: false,
        success: false,
        error: true,
      };
    },
    putRolesUpdate(state, action) {
      const newState = JSON.parse(JSON.stringify(state));
      newState.roles = newState.roles.map((item: IRole) => {
        if (item.id === action.payload.id) {
          return { ...item, ...action.payload };
        }
        return item;
      });
      return newState;
    },
    destroyRoles(state, _action: TRolesIDPayloadActionPayload) {
      state.destroyRole = {
        loading: true,
        success: false,
        error: false,
      };
    },
    destroyRolesSuccess(state) {
      state.destroyRole = {
        loading: false,
        success: true,
        error: false,
      };
    },
    destroyRolesFailure(state) {
      state.destroyRole = {
        loading: false,
        success: false,
        error: true,
      };
    },
    destroyRolesUpdate(state, actions) {
      const newState = JSON.parse(JSON.stringify(state));
      state.roles = newState.roles.filter((item: any) => item.id !== actions.payload.id);
    },
    destroyRolesReset(state) {
      state.destroyRole = {
        loading: false,
        success: false,
        error: false,
      };
    },
  },
});

export const {
  getRoles,
  getRolesSuccess,
  getRolesFailure,
  getRole,
  getRoleSuccess,
  getRoleFailure,
  postRoles,
  postRolesSuccess,
  postRolesFailure,
  postRolesUpdate,
  putRoles,
  putRolesSuccess,
  putRolesFailure,
  putRolesUpdate,
  destroyRoles,
  destroyRolesSuccess,
  destroyRolesFailure,
  destroyRolesReset,
  destroyRolesUpdate,
  clearRole,
} = rolesSlice.actions;
export const useRolesActions = () => {
  return bindActionCreators(
    {
      getRoles,
      getRole,
      getRoleSuccess,
      postRoles,
      putRoles,
      destroyRoles,
      destroyRolesReset,
      destroyRolesUpdate,
      postRolesUpdate,
      putRolesUpdate,
      clearRole,
    },
    useDispatch()
  );
};
export default rolesSlice.reducer;
