import { takeLatest, call, put } from "redux-saga/effects"; // Importing necessary functions from redux-saga
import { AxiosResponse } from "axios";
import {
  getCDACooperativesService,
  getCooperativesService,
  getProductProposalsNotarizationsService,
  getProductProposalsService,
  postProductProposalService,
  getMarketingProductProposalsService,
  getUnderwritingProductProposalProvisionsService,
  getClaimsProductProposalProvisionsService,
  getProposalService,
  postProductProposalCommissionApprovalService,
  putProductProposalCommissionService,
} from "@services/product-proposal/product-proposal.service";
import {
  getCdaCooperatives,
  getCdaCooperativesFailure,
  getCdaCooperativesSuccess,
  getCooperatives,
  getCooperativesFailure,
  getCooperativesSuccess,
  getProductProposal,
  getProductProposalFailure,
  getProductProposalNotarization,
  getProductProposalNotarizationFailure,
  getProductProposalNotarizationSuccess,
  getProductProposalSuccess,
  postProductProposal,
  postProductProposalFailure,
  postProductProposalSuccess,
  getMarketingProductProposals,
  getMarketingProductProposalsSuccess,
  getMarketingProductProposalsFailure,
  getUnderwritingProductProposalProvisionsSuccess,
  getUnderwritingProductProposalProvisions,
  getClaimsProductProposalProvisions,
  getClaimsProductProposalProvisionsSuccess,
  getClaimsProductProposalProvisionsFailure,
  getProposal,
  getProposalSuccess,
  getProposalFailure,
  postProposalCommission,
  postProposalCommissionSuccess,
  postProposalCommissionFailure,
  putProductProposalCommission,
  putProductProposalCommissionSuccess,
  putProductProposalCommissionFailure,
} from "@state/reducer/product-proposal";
import { handleServerException } from "@services/utils/utils.service";
import { PayloadAction } from "@reduxjs/toolkit";
import { TProductProposalPayload, TIProductProposalCommisionActionPayloadPostPut } from "@state/types/product-proposal";
import { IDefaultParams } from "@interface/common.interface";

// Saga for handling get product proposal request
function* getProductProposalsSaga(action: PayloadAction<IDefaultParams>) {
  try {
    const response: AxiosResponse = yield call(getProductProposalsService, action.payload); // Call the getProductProposalsService service and destructure data from the response
    yield put(getProductProposalSuccess(response)); // Dispatch getProductProposalSuccess action with the received data
  } catch (error) {
    yield call(handleServerException, error, getProductProposalFailure.type, true); // Handle any errors using handleServerException utility
  }
}

// Saga for handling post product proposal request
function* postProductsProposalSaga(action: PayloadAction<TProductProposalPayload>) {
  try {
    const { data }: AxiosResponse = yield call(postProductProposalService, action.payload); // Call the getProducts service and destructure data from the response
    yield put(postProductProposalSuccess(data)); // Dispatch getSuccess action with the received data
  } catch (error) {
    yield call(handleServerException, error, postProductProposalFailure.type, true); // Handle any errors using handleServerException utility
  }
}

// Saga for handling get cooperatives request
function* getProductProposalCooperativesSaga(action: PayloadAction<IDefaultParams>) {
  try {
    const { data }: AxiosResponse = yield call(getCooperativesService, action.payload); // Call the getProductProposalsService service and destructure data from the response
    yield put(getCooperativesSuccess(data)); // Dispatch getProductProposalSuccess action with the received data
  } catch (error) {
    yield call(handleServerException, error, getCooperativesFailure.type, true); // Handle any errors using handleServerException utility
  }
}

// Saga for handling get cooperatives request
function* getProductProposalCdaCooperativesSaga(action: PayloadAction<IDefaultParams>) {
  try {
    const { data }: AxiosResponse = yield call(getCDACooperativesService, action.payload); // Call the getProductProposalsService service and destructure data from the response
    yield put(getCdaCooperativesSuccess(data)); // Dispatch getProductProposalSuccess action with the received data
  } catch (error) {
    yield call(handleServerException, error, getCdaCooperativesFailure.type, true); // Handle any errors using handleServerException utility
  }
}

// Saga for handling get marketing product proposals request
function* getMarketingProductProposalsSaga(action: PayloadAction<IDefaultParams>) {
  try {
    const result: AxiosResponse = yield call(getMarketingProductProposalsService, action.payload);
    yield put(getMarketingProductProposalsSuccess(result));
  } catch (error) {
    yield call(handleServerException, error, getMarketingProductProposalsFailure.type, true);
  }
}

// Saga for handling get product proposal request
function* getProductProposalsNotarizationsSaga(actions: PayloadAction<IDefaultParams>) {
  try {
    const result: AxiosResponse = yield call(getProductProposalsNotarizationsService, actions.payload);
    yield put(getProductProposalNotarizationSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getProductProposalNotarizationFailure.type, true);
  }
}

function* getUnderwritingProductProposalProvisionsSaga(actions: PayloadAction<IDefaultParams>) {
  try {
    const result: AxiosResponse = yield call(getUnderwritingProductProposalProvisionsService, actions.payload);
    yield put(getUnderwritingProductProposalProvisionsSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getProductProposalNotarizationFailure.type, true);
  }
}
function* getClaimsProductProposalProvisionsSaga(actions: PayloadAction<IDefaultParams>) {
  try {
    const result: AxiosResponse = yield call(getClaimsProductProposalProvisionsService, actions.payload);
    yield put(getClaimsProductProposalProvisionsSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getClaimsProductProposalProvisionsFailure.type, true);
  }
}
function* getProposalSaga(actions: PayloadAction<IDefaultParams>) {
  try {
    const result: AxiosResponse = yield call(getProposalService, actions.payload);
    yield put(getProposalSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getProposalFailure.type, true);
  }
}
function* postProposalCommissionSaga(actions: TIProductProposalCommisionActionPayloadPostPut) {
  try {
    const { data }: AxiosResponse = yield call(postProductProposalCommissionApprovalService, actions.payload);
    yield put(postProposalCommissionSuccess(data?.data));
  } catch (error) {
    yield call(handleServerException, error, postProposalCommissionFailure.type, true);
  }
}

function* putProductProposalCommissionSaga(actions: PayloadAction<{ managementPercentFee: number; proposalId: number | string }>) {
  try {
    const { managementPercentFee, proposalId } = actions.payload;
    const { data }: AxiosResponse = yield call(putProductProposalCommissionService, { managementPercentFee }, proposalId);
    yield put(putProductProposalCommissionSuccess(data?.data));
  } catch (error) {
    yield call(handleServerException, error, putProductProposalCommissionFailure.type, true);
  }
}
// Root saga to watch for actions
export function* rootSaga() {
  yield takeLatest(getProductProposal.type, getProductProposalsSaga); // Watch for get action and run getProductProposalsSaga
  yield takeLatest(postProductProposal.type, postProductsProposalSaga); // Watch for get action and run postProductsProposalSaga
  yield takeLatest(getCooperatives.type, getProductProposalCooperativesSaga); // Watch for get action and run getProductProposalCooperativesSaga
  yield takeLatest(getCdaCooperatives.type, getProductProposalCdaCooperativesSaga); // Watch for get action and run getProductProposalCdaCooperativesSaga
  yield takeLatest(getProductProposalNotarization.type, getProductProposalsNotarizationsSaga); // Watch for get action and run getProductProposalsNotarizationsSaga
  yield takeLatest(getMarketingProductProposals.type, getMarketingProductProposalsSaga); // Watch for get action and run getMarketingProductProposalsSaga
  yield takeLatest(getUnderwritingProductProposalProvisions.type, getUnderwritingProductProposalProvisionsSaga); // Watch for get action and run getProductProposalProvisionsSaga
  yield takeLatest(getClaimsProductProposalProvisions.type, getClaimsProductProposalProvisionsSaga); // Watch for get action and run getProductProposalProvisionsSaga
  yield takeLatest(getProposal.type, getProposalSaga); // Watch for get action and run getProposalSaga
  yield takeLatest(postProposalCommission.type, postProposalCommissionSaga); // Watch for get action and run postProposalCommission
  yield takeLatest(putProductProposalCommission.type, putProductProposalCommissionSaga);
}
