import { takeLatest, call, put } from "redux-saga/effects"; // Importing necessary functions from redux-saga
import { AxiosResponse } from "axios"; // Importing AxiosResponse type from axios
import {
  getCurrentUserFormTransmittalTrail,
  getCurrentUserFormTransmittalTrailFailure,
  getCurrentUserFormTransmittalTrailSuccess,
  getLatestTransmittalForm,
  getLatestTransmittalFormFailure,
  getLatestTransmittalFormSuccess,
  getTransmittalForm,
  getTransmittalFormFailure,
  getTransmittalForms,
  getTransmittalFormsFailure,
  getTransmittalFormsSuccess,
  getTransmittalFormsTrail,
  getTransmittalFormsTrailSuccess,
  getTransmittalFormSuccess,
  getTransmittalFormTrail,
  getTransmittalFormTrailSuccess,
  postTransmittalForm,
  postTransmittalFormFailure,
  postTransmittalFormSuc<PERSON>,
  postFormTransmittalTrail,
  postFormTransmittalTrailSuccess,
  postFormTransmittalTrailFailure,
  getTransmittalTrailActivityLog,
  getTransmittalTrailActivityLogSuccess,
  getTransmittalTrailActivityLogFailure,
  getTransmittalFormToClifsaPrint,
  getTransmittalFormToClifsaPrintSuccess,
  getTransmittalFormToClifsaPrintFailure,
  getCurrentUserOldestFormTransmittalTrail,
  getCurrentUserOldestFormTransmittalTrailSuccess,
  getCurrentUserOldestFormTransmittalTrailFailure,
  postReturnedPads,
  postReturnedPadsSuccess,
  postReturnedPadsFailure,
  getPadAssignments,
  getPadAssignmentsSuccess,
  getPadAssignmentsFailure,
  getUserPadAssignment,
  getUserPadAssignmentSuccess,
  getUserPadAssignmentFailure,
  getUserPadAssignmentById,
  getUserPadAssignmentByIdSuccess,
  getUserPadAssignmentByIdFailure,
  getUserPadSeriesDetails,
  getUserPadSeriesDetailsSuccess,
  getUserPadSeriesDetailsFailure,
  putCancelReceipt,
  putCancelReceiptSuccess,
  putCancelReceiptFailure,
  putIssueReceipt,
  putIssueReceiptSuccess,
  putIssueReceiptFailure,
  getReturnedPadsSuccess,
  getReturnedPadsFailure,
  getReturnedPads,
  getReturnedTransmittalTrail,
  getReturnedTransmittalTrailSuccess,
  getReturnedTransmittalTrailFailure,
  putTransmittalTrail,
  putTransmittalTrailSuccess,
  putTransmittalTrailFailure,
} from "@state/reducer/form-inventory-transmittal";
import {
  getCurrentUserFormTransmittalTrailService,
  getLatestTransmittalTrailService,
  getTransmittalService,
  getTransmittalsService,
  getTransmittalsTrailService,
  getTransmittalTrailService,
  postFormTransmittalService,
  postFormTransmittalTrailService,
  getTransmittalTrailActivityLogService,
  getTransmittalFormToClifsaPrintService,
  getCurrentUserOldestFormTransmittalTrailService,
  postReturnedPadsService,
  getPadsAssignmentsService,
  getUserPadAssignmentService,
  getUserPadAssignmentByIdService,
  getUserPadSeriesDetailsService,
  putCancelReceiptService,
  putIssueReceiptService,
  getReturnedPadsService,
  getReturnedTransmittalTrailService,
  putTransmittalTrailService,
} from "@services/form-inventory-transmittal/form-inventory-transmittal.service";
import { handleServerException } from "@services/utils/utils.service";
import {
  TFormTransmittalOutgoingPayloadPostPut,
  TIFormTransmittalActionPayload,
  TTransmittalFormToClifsaPrintWithIdActionPayload,
  TCreateReturnedPayloadAction,
  TCancelReceiptWithActionPayload,
  TIssueReceiptWithActionPayload,
  TUpdateTransmitalStatusWithActionPayload,
} from "@state/types/form-inventory-transmittal";
import { PayloadAction } from "@reduxjs/toolkit";
import { toast } from "react-toastify";
import { IFormTransmittal, IFormTransmittalOutgoingPayload } from "@interface/form-inventory.interface";
import { IDefaultParams } from "@interface/common.interface";

function* getTransmittalFormsSaga(actions: PayloadAction<IDefaultParams>) {
  try {
    const result: AxiosResponse = yield call(getTransmittalsService, actions.payload);
    yield put(getTransmittalFormsSuccess(result));
  } catch (error) {
    yield call(handleServerException, error, getTransmittalFormsFailure.type, true);
  }
}
function* getTransmittalFormSaga(actions: PayloadAction<{ id: number }>) {
  try {
    const result: AxiosResponse = yield call(getTransmittalService, actions.payload.id);
    yield put(getTransmittalFormSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getTransmittalFormFailure.type, true);
  }
}
function* getTransmittalFormsTrailSaga(actions: PayloadAction<IDefaultParams>) {
  try {
    const result: AxiosResponse = yield call(getTransmittalsTrailService, actions.payload);
    yield put(getTransmittalFormsTrailSuccess(result));
  } catch (error) {
    yield call(handleServerException, error, getTransmittalFormsFailure.type, true);
  }
}
function* getTransmittalFormTrailSaga(actions: PayloadAction<IDefaultParams>) {
  try {
    const result: AxiosResponse = yield call(getTransmittalTrailService, Number(actions.payload.id));

    yield put(getTransmittalFormTrailSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getTransmittalFormFailure.type, true);
  }
}
// Saga for handling post
function* postTransmittalFormSaga({ payload }: TIFormTransmittalActionPayload) {
  try {
    const { data }: AxiosResponse<IFormTransmittal> = yield call(postFormTransmittalService, payload);
    yield put(postTransmittalFormSuccess(data));
    toast.success("Successfully created new Transmittal Form");
  } catch (error) {
    yield call(handleServerException, error, postTransmittalFormFailure.type, true);
  }
}

function* getCurrentUserFormTransmittalTrailSaga(actions: PayloadAction<IDefaultParams>) {
  try {
    const result: AxiosResponse = yield call(getCurrentUserFormTransmittalTrailService, actions.payload);

    yield put(getCurrentUserFormTransmittalTrailSuccess(result));
  } catch (error) {
    yield call(handleServerException, error, getCurrentUserFormTransmittalTrailFailure.type, true);
  }
}

function* getCurrentUserOldestFormTransmittalTrailSaga(actions: PayloadAction<IDefaultParams>) {
  try {
    const result: AxiosResponse = yield call(getCurrentUserOldestFormTransmittalTrailService, actions.payload);
    yield put(getCurrentUserOldestFormTransmittalTrailSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getCurrentUserOldestFormTransmittalTrailFailure.type, true);
  }
}

//Latest trail
function* getLatestTransmittalFormSaga(actions: PayloadAction<IDefaultParams>) {
  try {
    const result: AxiosResponse = yield call(getLatestTransmittalTrailService, actions.payload);
    yield put(getLatestTransmittalFormSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getLatestTransmittalFormFailure.type, true);
  }
}
function* postFormTransmittalTrailSaga(actions: TFormTransmittalOutgoingPayloadPostPut) {
  try {
    const result: AxiosResponse<IFormTransmittalOutgoingPayload> = yield call(postFormTransmittalTrailService, actions.payload.id, actions.payload);
    yield put(postFormTransmittalTrailSuccess(result.data));
    toast.success("Successfully created new Transmittal Form Trail");
  } catch (error) {
    yield call(handleServerException, error, postFormTransmittalTrailFailure.type, true);
  }
}
function* getTransmittalTrailActivityLogSaga(actions: PayloadAction<IDefaultParams>) {
  try {
    const result: AxiosResponse = yield call(getTransmittalTrailActivityLogService, Number(actions.payload.id));
    yield put(getTransmittalTrailActivityLogSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getTransmittalTrailActivityLogFailure.type, true);
  }
}
function* getTransmittalFormToClifsaPrintSaga(actions: TTransmittalFormToClifsaPrintWithIdActionPayload): Generator<any, void, any> {
  try {
    const response: any = yield call(getTransmittalFormToClifsaPrintService, actions.payload.formTransmittalId);
    const pdfBlob = new Blob([response], { type: "application/pdf" });
    const url = window.URL.createObjectURL(pdfBlob);
    yield put(getTransmittalFormToClifsaPrintSuccess(url));
  } catch (error) {
    console.error(error);
    yield put(getTransmittalFormToClifsaPrintFailure());
  }
}

function* postReturnedPadsSaga(actions: TCreateReturnedPayloadAction) {
  try {
    const result: AxiosResponse<IFormTransmittalOutgoingPayload> = yield call(postReturnedPadsService, actions.payload);
    yield put(postReturnedPadsSuccess(result.data));
    toast.success("Successfully Returned Pad");
  } catch (error) {
    yield call(handleServerException, error, postReturnedPadsFailure.type, true);
  }
}

function* getPadAssignmentsSaga(actions: PayloadAction<IDefaultParams>): Generator<any, void, any> {
  try {
    const result: AxiosResponse = yield call(getPadsAssignmentsService, actions.payload);
    yield put(getPadAssignmentsSuccess(result));
  } catch (error) {
    yield call(handleServerException, error, getPadAssignmentsFailure.type, true);
  }
}

function* getUserPadAssignmentSaga(actions: PayloadAction<IDefaultParams>): Generator<any, void, any> {
  try {
    const result: AxiosResponse = yield call(getUserPadAssignmentService, actions.payload);
    yield put(getUserPadAssignmentSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getUserPadAssignmentFailure.type, true);
  }
}

function* getUserPadAssignmentByIdSaga({ payload }: PayloadAction<{ params: IDefaultParams }>): Generator<any, void, any> {
  try {
    const result: AxiosResponse = yield call(getUserPadAssignmentByIdService, payload.params);
    yield put(getUserPadAssignmentByIdSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getUserPadAssignmentByIdFailure.type, true);
  }
}

function* getUserPadSeriesDetailsSaga({ payload }: PayloadAction<{ id: number }>): Generator<any, void, any> {
  try {
    const result: AxiosResponse = yield call(getUserPadSeriesDetailsService, payload.id);
    yield put(getUserPadSeriesDetailsSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getUserPadSeriesDetailsFailure.type, true);
  }
}

function* putCancelReceiptSaga(action: TCancelReceiptWithActionPayload): Generator<any, void, any> {
  try {
    const result: AxiosResponse = yield call(putCancelReceiptService, action.payload);
    yield put(putCancelReceiptSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, putCancelReceiptFailure.type, true);
  }
}

function* putIssueReceiptSaga(action: TIssueReceiptWithActionPayload): Generator<any, void, any> {
  try {
    const result: AxiosResponse = yield call(putIssueReceiptService, action.payload);
    yield put(putIssueReceiptSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, putIssueReceiptFailure.type, true);
  }
}

function* getReturnedPadsSaga(actions: PayloadAction<IDefaultParams>) {
  try {
    const result: AxiosResponse = yield call(getReturnedPadsService, actions.payload);
    yield put(getReturnedPadsSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getReturnedPadsFailure.type, true);
  }
}

function* getReturnedTransmittalTrailSaga(actions: PayloadAction<IDefaultParams>) {
  try {
    const result: AxiosResponse = yield call(getReturnedTransmittalTrailService, Number(actions.payload.id));

    yield put(getReturnedTransmittalTrailSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, getReturnedTransmittalTrailFailure.type, true);
  }
}

function* putTransmittalTrailSaga(action: TUpdateTransmitalStatusWithActionPayload): Generator<any, void, any> {
  try {
    const result: AxiosResponse = yield call(putTransmittalTrailService, action.payload);
    yield put(putTransmittalTrailSuccess(result.data));
  } catch (error) {
    yield call(handleServerException, error, putTransmittalTrailFailure.type, true);
  }
}

export function* rootSaga() {
  yield takeLatest(getTransmittalForms.type, getTransmittalFormsSaga);
  yield takeLatest(getTransmittalForm.type, getTransmittalFormSaga);
  yield takeLatest(postTransmittalForm.type, postTransmittalFormSaga);
  yield takeLatest(getTransmittalFormTrail.type, getTransmittalFormTrailSaga);
  yield takeLatest(getTransmittalFormsTrail.type, getTransmittalFormsTrailSaga);
  yield takeLatest(getCurrentUserFormTransmittalTrail.type, getCurrentUserFormTransmittalTrailSaga);
  yield takeLatest(getLatestTransmittalForm.type, getLatestTransmittalFormSaga);
  yield takeLatest(postFormTransmittalTrail.type, postFormTransmittalTrailSaga);
  yield takeLatest(getTransmittalTrailActivityLog.type, getTransmittalTrailActivityLogSaga);
  yield takeLatest(getTransmittalFormToClifsaPrint.type, getTransmittalFormToClifsaPrintSaga);
  yield takeLatest(getCurrentUserOldestFormTransmittalTrail.type, getCurrentUserOldestFormTransmittalTrailSaga);
  yield takeLatest(postReturnedPads.type, postReturnedPadsSaga);
  yield takeLatest(getTransmittalTrailActivityLog.type, getTransmittalTrailActivityLogSaga);
  yield takeLatest(getTransmittalFormToClifsaPrint.type, getTransmittalFormToClifsaPrintSaga);
  yield takeLatest(getPadAssignments.type, getPadAssignmentsSaga);
  yield takeLatest(getUserPadAssignment.type, getUserPadAssignmentSaga);
  yield takeLatest(getUserPadAssignmentById.type, getUserPadAssignmentByIdSaga);
  yield takeLatest(getUserPadSeriesDetails.type, getUserPadSeriesDetailsSaga);
  yield takeLatest(putCancelReceipt.type, putCancelReceiptSaga);
  yield takeLatest(putIssueReceipt.type, putIssueReceiptSaga);
  yield takeLatest(getReturnedPads.type, getReturnedPadsSaga);
  yield takeLatest(getReturnedTransmittalTrail.type, getReturnedTransmittalTrailSaga);
  yield takeLatest(putTransmittalTrail.type, putTransmittalTrailSaga);
}
