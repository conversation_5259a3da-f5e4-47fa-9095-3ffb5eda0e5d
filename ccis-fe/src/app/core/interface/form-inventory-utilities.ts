import { IUser } from "./user.interface";
import { IUserArea } from "./utilities.interface";

export interface IDivision {
  id: number;
  divisionCode: string;
  divisionName: string;
  description: string;
}

export interface IFormType {
  id: number;
  formTypeCode: string;
  formTypeName: string;
  description: string;
}

export interface IPaymentMethod {
  id: number;
  paymentMethodCode: string;
  paymentMethodName: string;
  description: string;
}

export interface IBank {
  id: number;
  bankCode: string;
  bankName: string;
  bankTypeId: number;
  description: string;
  bankAccounts?: IBankAccount[];
  bankType: IBankType;
}

export interface IBankAccount {
  id: number;
  bankId: number; //name
  bankAccountNumber: string;
  bankAccountName: string;
  description: string;
  areaId?: number;
  divisionId?: number;
  bank?: IBank;
  area?: IUserArea;
  division?: IDivision;
}
export interface IMarketArea {
  id: number;
  marketAreaCode: string;
  marketAreaName: string;
  description: string;
}

export interface IBankType {
  id: number;
  bankTypeCode: string;
  bankTypeName: string;
  description: string;
}

export interface IReleasedMethod {
  id: number;
  releasedMethodCode: string;
  releasedMethodName: string;
  description: string;
}

export interface IReleaseTransmittal {
  releasedArea?: number;
  releasedTo?: IUser;
  releasedMethod?: IReleasedMethod;
  releasedMethodId?: number;
  courierService?: string;
  releasedBy?: string;
  trackingNo?: string;
  status?: string;
  releasedDate?: string;
  releasedMethodName?: number;
}

export interface ILocation {
  id: number;
  locationName: string;
  area: string;
  users: IUser[];
}
