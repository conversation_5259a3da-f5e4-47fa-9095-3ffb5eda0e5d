import { IDefaultPaginatedLinks, IMeta, IAttachments } from "./common.interface";
import { IRole } from "./roles.interface";
import { IUser } from "./user.interface";
import { IUtilitiesDepartment } from "./utilities.interface";

//For the Request Type
export interface IRequestTypeInterface {
  id?: number;
  name: string;
  description: string;
  departmentId: string;
}

//For the Application of said request
export interface IApplicationInterface {
  id?: number;
  code?: string;
  applicationName: string;
  description?: string;
}

//For the Device System of said requestor
export interface IDeviceSystemInterface {
  id?: number;
  code?: string;
  deviceSystemName: string;
  description?: string;
}

//For the Operating System of said requestor
export interface IOperatingSystemInterface {
  id?: number;
  code?: string;
  operatingSystemName: string;
  description?: string;
}

//The main Ticketing payload system
export interface ITicketInterface {
  id: number;
  subject: string;
  description: string;
  createdAt?: string;
  createdBy: IUser;
  assignedToId?: number;
  fromDepartmentId: number;
  toDepartmentId: number;
  requestTypeId: number;
  issueType: string;
  coopId?: number;
  deviceId?: number;
  operatingSystemId?: number;
  applicationId?: number;
  isUrgent: boolean;
  priorityLevel?: string;
  closureStatus?: string;
  status?: string;
  startDate?: string;
  completionDate?: string;
  expectedCompletionDate?: string;
  extensionDate?: string;
  extensionRemarks?: string;
  formType?: string;
  attachmentsDetails?: IAttachments[];
  hasTopManApproval?: boolean;
  ticketAssigneesDetails?: ITicketAssigneeTemplate[];
  ticketCommentsDetails?: ITicketCommentTemplate[];
  requestTypes?: IRequestTypeInterface[];
  latestTicketAssignee?: ITicketAssigneeTemplate;
}
export interface ITicketCommentTemplate {
  id: number;
  ticketId: number;
  userId: number;
  comment: string;
  type: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  user: IUser;
}

//For the Filter Button in the list of tickets
export interface FilterCriteria {
  departmentId?: number;
  status?: string;
  fromDate?: Date | null;
  toDate?: Date | null;
}

//The Due Ticketing payload For Due Dates
export interface IDueTicketInterface {
  id: number;
  subject: string;
  description: string;
  createdAt: string;
  createdBy: IUser;
  assignedToId?: number;
  startDate?: string;
  completionDate?: string;
  expectedCompletionDate: string;
  extensionDate?: string;
  extensionRemarks?: string;
  attachmentsDetails?: IAttachments[];
  ticketAssigneesDetails?: ITicketAssigneeTemplate[];
  fromDepartment?: IUtilitiesDepartment;
}

//The Graph Ticketing payload system
export interface IGraphTicketInterface {
  id: number;
  subject: string;
  description: string;
  createdAt?: string;
  createdBy: IUser;
  assignedToId?: number;
  fromDepartmentId: number;
  toDepartmentId: number;
  requestTypeId: number;
  issueType: string;
  closureStatus?: string;
  status?: string;
  startDate?: string;
  ticketAssigneesDetails?: ITicketAssigneeTemplate[];
  toDepartment?: IUtilitiesDepartment;
}

export interface ITicketAssigneeTemplate {
  id?: number;
  ticketId: number;
  userId: number;
  role: string;
  status: string;
  user: IUser;
}
// For the new Get system, primarily for robustness

//The main Ticketing payload system
export interface ITicket {
  id?: number;
  subject: string;
  description: string;
  createdAt?: string;
  createdBy: IUser;
  assignedToId?: number;
  fromDepartmentId: number;
  toDepartmentId: number;
  requestTypeId: number;
  issueType: string;
  coopId?: number;
  deviceId?: number;
  operatingSystemId?: number;
  applicationId?: number;
  isUrgent: boolean;
  priorityLevel?: string;
  closureStatus?: string;
  status?: string;
  startDate?: string;
  completionDate?: string;
  expectedCompletionDate?: string;
  extensionDate?: string;
  extensionRemarks?: string;
  formType?: string;
  attachments?: IAttachments;
  hasTopManApproval?: boolean;
  ticketAssignees?: ITicketAssigneeTemplate;
  ticketComments?: ITicketCommentTemplate;
  requestType?: IRequestTypeInterface;
  latestTicketAssignee?: ITicketAssigneeTemplate;
}
export interface TicketResponse {
  data: ITicket[];
  links?: IDefaultPaginatedLinks;
  meta?: IMeta;
}
export interface IDueTicket {
  id?: number;
  subject: string;
  description: string;
  createdAt: string;
  createdBy: IUser;
  assignedToId?: number;
  startDate?: string;
  completionDate?: string;
  expectedCompletionDate: string;
  extensionDate?: string;
  extensionRemarks?: string;
  attachmentsDetails?: IAttachments[];
  ticketAssigneesDetails?: ITicketAssigneeTemplate[];
  fromDepartment?: IUtilitiesDepartment;
}
export interface DueTicketResponse {
  data: IDueTicket[];
  links?: IDefaultPaginatedLinks;
  meta?: IMeta;
}
export interface IGraphTicket {
  id?: number;
  subject: string;
  description: string;
  createdAt?: string;
  createdBy: IUser;
  assignedToId?: number;
  fromDepartmentId: number;
  toDepartmentId: number;
  requestTypeId: number;
  issueType: string;
  closureStatus?: string;
  status?: string;
  startDate?: string;
  ticketAssigneesDetails?: ITicketAssigneeTemplate[];
  toDepartment?: IUtilitiesDepartment;
}
export interface GraphTicketResponse {
  data: IGraphTicket[];
  links?: IDefaultPaginatedLinks;
  meta?: IMeta;
}

export interface ITicketUserAssignees {}

export interface IDepartment {
  id: number;
  departmentName: string;
  departmentCode: string;
  description: string;
  createdBy: number | null;
  updatedBy: number | null;
  deletedBy: number | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface ITicketDepartmentUsers {
  id: number;
  title: string | null;
  suffix: string | null;
  gender: string | null;
  address: string | null;
  birthDate: string | null;
  firstname: string;
  middlename: string;
  lastname: string;
  contactNumber: string;
  email: string;
  companyId: string | null;
  positionId: number;
  departmentId: number;
  emailVerifiedAt: string | null;
  rememberToken: string | null;
  createdAt: string;
  updatedAt: string;
  status: string;
  signaturePath: string;
  profilePicturePath: string;
  roles: IRole[];
  department: IDepartment;
  areaId: number;
}

export interface ITicketInfoInterface {
  id: number;
  ticketNumber: string | null;
  subject: string;
  description: string;
  assignedToId: number | null;
  fromDepartmentId: number;
  toDepartmentId: number;
  requestTypeId: number;
  issueType: string | null;
  coopId: string | null;
  device: string | null;
  deviceId: string | null;
  operatingSystem: string | null;
  operatingSystemId: string | null;
  applicationName: string | null;
  applicationId: string | null;
  isUrgent: boolean | null;
  priorityLevel: string;
  closureStatus: string;
  status: string;
  startDate: string | null;
  completionDate: string | null;
  expectedCompletionDate: string;
  extensionDate: string;
  extensionRemarks: string;
  formType: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  createdBy: ITicketDetails;
  assignedTo: IAssignedTo | null;
  fromDepartment: IDepartment;
  toDepartment: IDepartment;
  requestType: IRequestType;
  attachments: IAttachment[];
  ticketAssignees: ITicketAssignee[];
  ticketComments: ITicketComment[];
}
export interface ITicketInfo {
  id?: number;
  ticketNumber: string | null;
  subject: string;
  description: string;
  assignedToId: number | null;
  fromDepartmentId: number;
  toDepartmentId: number;
  requestTypeId: number;
  issueType: string | null;
  coopId: string | null;
  device: string | null;
  deviceId: string | null;
  operatingSystem: string | null;
  operatingSystemId: string | null;
  applicationName: string | null;
  applicationId: string | null;
  isUrgent: boolean | null;
  priorityLevel: string;
  closureStatus: string;
  status: string;
  startDate: string | null;
  completionDate: string | null;
  expectedCompletionDate: string;
  extensionDate: string;
  extensionRemarks: string;
  formType: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  createdBy: ITicketDetails;
  assignedTo: IAssignedTo | null;
  fromDepartment: IDepartment;
  toDepartment: IDepartment;
  requestType: IRequestType;
  attachments: IAttachment[];
  ticketAssignees: ITicketAssignee[];
  ticketComments: ITicketComment[];
  latestTicketAssignee?: ITicketAssignee;
}

export interface TicketAssigneeResponse {
  data: ITicketAssignee[];
  links?: IDefaultPaginatedLinks;
  meta?: IMeta;
}

export interface ITicketDetails {
  id: number;
  title: string | null;
  suffix: string | null;
  gender: string | null;
  address: string | null;
  birthDate: string | null;
  firstname: string;
  middlename: string;
  lastname: string;
  contactNumber: string;
  email: string;
  companyId: string | null;
  positionId: number;
  departmentId: number | null;
  emailVerifiedAt: string | null;
  rememberToken: string | null;
  createdAt: string;
  updatedAt: string;
  status: string;
  signaturePath: string | null;
  profilePicturePath: string | null;
  areaId: number | null;
  position: IPosition;
  ticketComments?: ITicketComment[];
}

export interface IAssignedTo {
  id: number;
  title: string | null;
  suffix: string | null;
  gender: string | null;
  address: string | null;
  birthDate: string | null;
  firstname: string;
  middlename: string;
  lastname: string;
  contactNumber: string;
  email: string;
  companyId: number | null;
  positionId: number;
  departmentId: number | null;
  emailVerifiedAt: string | null;
  rememberToken: string | null;
  createdAt: string;
  updatedAt: string;
  status: string;
  signaturePath: string | null;
  profilePicturePath: string | null;
  areaId: number | null;
}

export interface IRequestType {
  id: number;
  name: string;
  description: string;
  departmentId: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface ITicketAssignee {
  id: number;
  ticketId: number;
  userId: number;
  role: string;
  status: string;
  remarks: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  user?: ITicketDetails;
}

export interface IAttachment {
  id: number;
  label: string;
  description: string;
  filename: string;
  filepath: string;
  mimeType: string;
  tag: string | null;
  size: number;
  createdAt: string;
  updatedAt: string;
}

export interface IPosition {
  id: number;
  positionName: string;
  positionCode: string;
  description: string;
  createdBy: number;
  updatedBy: number;
  deletedBy: number | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface ITicketComment {
  id: number;
  ticketId: number;
  userId: number;
  comment: string;
  type: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  user: ITicketDetails;
}

export interface TITicketExtendDatePayload {
  ticketId: number | string; // Ensure this is a number or string based on your API requirements
  extendedDate: string;
  extensionRemarks: string;
}

export interface IForwardTicketPayload {
  ticketId: number | string;
  departmentId: number;
  remarks?: string;
}

export interface ITicketUpdateStatusPayload {
  status: string;
  remarks?: string;
}

export interface ITicketStatusUpdateResponse {
  status: string;
  message: string | null;
  data: string;
}

export interface ITicketCommentPayload {
  id: number;
  comment: string;
}

export interface IAttachmentResponse {
  id?: string;
  label?: string;
  description?: string;
  tag?: string;
  filename?: string;
  filepath?: string;
  size?: string | number;
  mimeType?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface ISaveAttachmentsPayload {
  attachableType: string;
  attachableId: string | number;
  files: File[];
}

//This is for the Export Report Feature
export interface IExportReportPayload {
  departmentId: number | string;
  status: string;
  CreatedDateFrom: string;
  CreatedDateTo: string;
  fileType: string;
}
