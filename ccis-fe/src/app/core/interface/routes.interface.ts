export type RouteItem = {
  name: string;
  id: string;
  path: string;
  component: (props: any) => any;
  guard?: any;
  role?: any;
  index?: boolean;
  roles?: UserRoles[];
  permissions?: PermissionType[];
  icon?: React.ElementType | null;
  iconActive?: any;
  hidden?: boolean;
  current?: boolean;
  isSecondaryItem?: boolean;
  props?: any;
  children?: RouteItem[];
  isSidebar?: boolean;
};

export enum UserTypes {
  admin = "user-admin",
  user = "user",
}

export enum UserRoles {
  admin = "super-admin",
  uatadmin = "uat-admin",
  user = "user",
  marketing = "marketing",
  actuary = "actuary",
  sea = "sales-executive-assistant",
  cashier = "cashier",
  treasury = "treasury-officer",
  sales = "sales",
  incomingCashier = "incoming-cashier",
  outgoingCashier = "outgoing-cashier",
  outgoingAdmin = "outgoing-admin",
  chiefCashier = "chief-cashier",
  clifsaAdmin = "clifsa-admin",
  gam = "gam",
  ioc = "Incoming and Outgoing Cashier",
  compliance = "compliance",
  underwriting = "underwriting",
  claims = "claims",
  incomingAdmin = "incoming-admin",
  actuaryAnalyst1 = "actuary-analyst-1",
  actuaryAssistant1 = "actuary-assistant-1",
  actuaryManager = "actuary-manager",
  avpLifeNonLife = "avp-for-life-and-nonlife",
  rnd = "research-and-development",
  claimsManager = "claims-manager",
  vicePresidentSales = "vice-president-for-sales",
  vicePresidentOperations = "vice-president-for-operation",
  presidentCeo = "president-and-ceo",
  areaSalesManager = "area-sales-manager",
  infraOfficer = "infrastructure-officer",
  accounting = "accounting",
}

export enum PermissionType {
  TEST_PERMISSION = "test_permission",
  SHARES_CREATE = "shares.create",
  SHARES_EDIT = "shares.update",
  SHARES_DELETE = "shares.delete",
  SHARES_VIEW = "shares.view",
  SHARES_PAYMENT_CREATE = "share_payments.create",
  SHARES_PAYMENT_EDIT = "share_payments.update",
  SHARES_PAYMENT_DELETE = "shares_payments.delete",
  SHARES_PAYMENT_VIEW = "shares_payments.view",
  PRODUCT_TYPE_VIEW = "product_types.view",
  PRODUCT_TYPE_CREATE = "product_types.create",
  PRODUCT_TYPE_UPDATE = "product_types.update",
  PRODUCT_TYPE_DELETE = "product_types.delete",
  //requirement Permission
  REQUIREMENT_TEMPLATE_VIEW = "requirement_templates.view",
  REQUIREMENT_TEMPLATE_CREATE = "requirement_templates.create",
  REQUIREMENT_TEMPLATE_UPDATE = "requirement_templates.update",
  REQUIREMENT_TEMPLATE_DELETE = "requirement_templates.delete",
  REQUIREMENT_CREATE = "requirement.create",
  REQUIREMENT_UPDATE = "requirement.update",
  REQUIREMENT_DELETE = "requirement.delete",
  REQUIREMENT_VIEW = "requirement.view",
  //cooperatives types Permission
  COOPERATIVES_TYPE_CREATE = "cooperative_types.create",
  COOPERATIVES_TYPE_UPDATE = "cooperative_types.update",
  COOPERATIVES_TYPE_DELETE = "cooperative_types.delete",
  COOPERATIVES_TYPE_VIEW = "cooperative_types.view",

  //cooperative Membership type Permission
  COOPERATIVES_MEMBERSHIP_TYPE_VIEW = "cooperative_membership_types.view",
  COOPERATIVES_MEMBERSHIP_TYPE_CREATE = "cooperative_membership_types.create",
  COOPERATIVES_MEMBERSHIP_TYPE_UPDATE = "cooperative_membership_types.update",
  COOPERATIVES_MEMBERSHIP_TYPE_DELETE = "cooperative_membership_types.delete",

  PRODUCT_GUIDELINES_SUBMIT_FOR_APPROVAL = "product.submit_for_approval",
  PRODUCT_REVISION_GUIDELINES_SUBMIT_FOR_APPROVAL = "productRevision.submit_for_approval",

  //Product Proposal
  PRODUCT_PROPOSAL_VIEW = "product_proposal.view",
  PRODUCT_PROPOSAL_CREATE = "product_proposal.create",
  PRODUCT_PROPOSAL_UPDATE = "product_proposal.update",
  PRODUCT_PROPOSAL_DELETE = "product_proposal.delete",
}
