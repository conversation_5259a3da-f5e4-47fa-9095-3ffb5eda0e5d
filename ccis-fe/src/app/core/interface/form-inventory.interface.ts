import { IAttachments, IDefaultPaginatedLinks, IMeta } from "./common.interface";
import { IBankAccount, IDivision, IFormType, IPaymentMethod, IReleaseTransmittal } from "./form-inventory-utilities";
import { IPosition } from "./position.interface";
import { ICooperative } from "./product-proposal.interface";
import { IProduct, ISignatories } from "./products.interface";
import { IUser } from "./user.interface";
import { IUserArea } from "./utilities.interface";

//FORM INVENTORY INCOMING RECEIVED FORM
export interface IIncomingReceivedForm {
  id: number;
  divisionId: number;
  formTypeId: number;
  areaId: number;
  receivedDate?: string;
  seriesFrom: number;
  seriesTo: number;
  atpNumber: number;
  status?: string;
  signatories?: ISignatories[];
  padAssignments?: IPadAssignments[];
  noPads: number;
  createdAt?: string;
  createdBy?: IUser;
  attachments: IAttachments[];
  division: IDivision;
  formType: IFormType;
  area: IUserArea;
}

export interface IPadAssignments {
  id: number;
  seriesFrom: number;
  seriesTo: number;
  assignedUser?: string | number;
  padNumber: number;
  formId: number;
  formTransmittalId?: number;
  formTransmittal?: IFormTransmittal;
  transmittalNumber?: number;
  form?: IIncomingReceivedForm;
  status: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: IUser;
  division?: IDivision;
  type?: string;
  padSeriesDetails?: IPadSeriesDetails[];
  returnedAt?: string;
}

export interface IPadSeriesDetails {
  id: number;
  amount?: number;
  orDate?: string;
  orNumber?: string;
  padAssignmentId?: number;
  productId?: number;
  releasedAt?: string;
  remarks?: string;
  seriesNo?: number;
  status?: string;
  cancelledAt?: string;
  createdAt?: string;
  paymentDetailId?: number;
  cooperative?: ICooperative;
  product?: IProduct;
  paymentDetail?: PaymentDetails;
  padAssignment?: IPadAssignments;
  issuedBy?: IUser;
  remitTo: IUser;
  attachments: IAttachments[];
}

export interface PaymentDetails {
  id: number;
  seriesNo: string;
  paymentMethodId?: number;
  amount?: number;
  dateDeposit?: string;
  chequeNumber?: string;
  coopName?: string;
  remarks: string;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
  attachments: IAttachments;
  paymentMethod: IPaymentMethod;
  bankAccount: IBankAccount;
}

export interface IFormTransmittal {
  id?: number;
  transmittalNumber?: number;
  releasedAreaId?: number;
  releasedToId?: number;
  releasedTo?: IUser;
  releaseTransmittal?: IReleaseTransmittal;
  withBirAttachment?: number;
  padAssignments?: IPadAssignments[];
  remarks?: string;
  status?: string;
  areaId?: number;
  approval?: IApproval;
  createdAt?: string;
  createdBy?: IUser;
  updatedAt?: string;
  formTransmittalTrails?: IFormTransmittalTrail[];
  latestFormTransmittalTrail?: IFormTransmittalTrail;
  oldestFormTransmittalTrail?: IFormTransmittalTrail;
  returnedPads?: IPadAssignments[];
  releasedArea?: IUserArea;
  deliveredBy?: string;
}

export interface IFormTransmittalTrail {
  id?: number;
  formTransmittalId?: number;
  status?: string;
  trackingNo?: string;
  deliveredBy?: string;
  releasedMethodId?: number;
  releaseMethod?: IReleaseTransmittal;
  receivedAt?: string;
  releasedArea?: IUserArea;
  createdAt?: string;
  createdBy?: IUser;
  releasedTo?: IUser;
  releasedMethod?: IReleaseTransmittal;
}

//Signees tracking
export interface IApproval {
  id: number;
  approvableType: string;
  approvableId: number;
  status: string;
  remarks?: string;
  approvedAt?: string;
  rejectedAt?: string;
  signatoryTemplateId: number;
  createdBy?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface IFormActivityLogs {
  id: number;
  description: string;
  properties?: [];
  causer: IUser;
  createdAt: string;
  createdBy: number;
  UpdatedAt: string;
}
export interface IFormTransmittalOutgoingPayload {
  id: number;
  releasedArea?: number;
  releasedTo?: number;
  status?: string;
  releasedMethodId?: number;
  deliveredBy?: string;
  trackingNo?: string;
}

export interface IFormsResponse {
  data: IIncomingReceivedForm[];
  links?: IDefaultPaginatedLinks;
  meta?: IMeta;
}

export interface IFormTransmittalResponse {
  data: IFormTransmittal[];
  links?: IDefaultPaginatedLinks;
  meta?: IMeta;
}
export interface ICauser {
  id: number;
  title: string | null;
  suffix: string | null;
  gender: string | null;
  address: string | null;
  birthDate: string | null;
  firstname: string;
  middlename: string;
  lastname: string;
  contactNumber: string;
  email: string;
  companyId: number | null;
  positionId: number;
  departmentId: number | null;
  emailVerifiedAt: string | null;
  rememberToken: string | null;
  createdAt: string;
  updatedAt: string;
  status: string;
  signaturePath: string | null;
  profilePicturePath: string | null;
  position: IPosition;
  areaId: number;
}

export interface ITransmittalTrailStatus {
  id: number;
  description: string;
  event: string;
  // properties: any[]; // Adjust type if properties have a specific structure
  created_at: string;
  updated_at: string;
  causer: ICauser;
}

export interface IPadRequest {
  id?: number;
  divisionId: number;
  formTypeId: number;
  areaId: number;
  numberOfPads: number;
  seriesFrom: number;
  seriesTo: number;
  releasedTo: IUser;
  padAssignments?: IPadAssignments[];
}

export interface IPadAssignmentsWithDetails {
  id: number;
  padNumber: number;
  seriesFrom: number;
  seriesTo: number;
  padSeriesDetails?: IPadSeriesDetails[];
  assignedUser?: string;
  formId: number;
  formTransmittalId?: number;
  status: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number;
}

// export interface IPadSeriesDetails {
//   id: number;
//   seriesNo?: string;
//   padAssignmentId?: number;
//   cooperativeId?: number;
//   productId?: number;
//   amount?: number;
//   orNumber?: number;
//   orDate?: string;
//   status?: string;
//   remarks?: string;
//   cooperative?: ICooperative;
//   product?: string;
//   userRemitTo?: IUser;
//   createdAt?: string;
//   updatedAt?: string;
//   cancelledAt?: string;
// }

export interface IPRIssuance {
  id?: number;
  seriesNO: number;
  padAssignmentId: number;
  CooperativeId: number;
  productId: number;
  status?: string;
  remarks?: string;
  remitTo: string;
  createdAt: string;
  releasedAt: string;
  IPaymentDetails: IPaymentDetails;
}
export interface IPaymentDetails {
  paymentMethodId: number;
  amount?: number;
  dateDeposit?: string;
  bankAccountId?: number;
  accountNumber?: string;
  chequeNumber?: string;
  coopName?: string;
  remarks?: string;
  attachements?: IAttachments[];
}
