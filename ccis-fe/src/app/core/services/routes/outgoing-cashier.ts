import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import IncomingOutgoingCashierDashboard from "@modules/dashboard/IncomingOutgoingCashierDashboard.tsx";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
// import { PiCertificate } from "react-icons/pi";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.OUTGOINGCASHIER.outgoingCashierDashboard.key,
  path: ROUTES.OUTGOINGCASHIER.outgoingCashierDashboard.key,
  component: IncomingOutgoingCashierDashboard,
  guard: AuthGuard,
  roles: [UserRoles.incomingCashier],
  icon: MdDashboard,
  isSidebar: true,
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.OUTGOINGCASHIER.notification.key,
  path: ROUTES.OUTGOINGCASHIER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.outgoingCashier],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.OUTGOINGCASHIER.profile.key,
  path: ROUTES.OUTGOINGCASHIER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.outgoingCashier],
};

export const outgoingCashierRoutes = [overview, notification, profile];
