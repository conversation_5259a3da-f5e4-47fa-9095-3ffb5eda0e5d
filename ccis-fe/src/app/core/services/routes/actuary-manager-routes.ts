import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import RNDDashboard from "@modules/actuary/dashboard";
import { FaFileCircleCheck } from "react-icons/fa6";
import { QuotationClppQuotationView } from "@modules/sales/view-aer/clpp";
import { PiList } from "react-icons/pi";
import { QuotationGyrtQuotationView } from "@modules/sales/view-aer/gyrt";
import { QuotationClspQuotationView } from "@modules/sales/view-aer/clsp";
import QuotationFipAerView from "@modules/sales/view-aer/fipViewPreview";
import AerApprovalTable from "@modules/admin/approval-aer/table/AerApprovalTable";
import MyApprovals from "@modules/admin/approval-aer";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard"
import { HiChevronRight } from "react-icons/hi2";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.ACTUARYMANAGER.dashboard.key,
  path: ROUTES.ACTUARYMANAGER.dashboard.key,
  component: RNDDashboard,
  guard: AuthGuard,
  roles: [UserRoles.actuaryManager],
  icon: MdDashboard,
  isSidebar: true,
};

export const aerTableApproval: RouteItem = {
  name: "My Approval",
  id: ROUTES.ACTUARYMANAGER.aerApproval.key,
  path: ROUTES.ACTUARYMANAGER.aerApproval.key,
  component: AerApprovalTable,
  guard: AuthGuard,
  roles: [UserRoles.actuaryManager],
  icon: HiChevronRight,
  isSidebar: false,
};
export const quotationFipAerView: RouteItem = {
  name: "Quotation FIP Aer View",
  id: ROUTES.ACTUARYMANAGER.quotationFipAerView.key,
  path: ROUTES.ACTUARYMANAGER.quotationFipAerView.key,
  component: QuotationFipAerView,
  guard: AuthGuard,
  roles: [UserRoles.actuaryManager],
  icon: PiList,
  isSidebar: false,
};
export const quotationClspQuotationView: RouteItem = {
  name: "Quotation CLSP Quotation View",
  id: ROUTES.ACTUARYMANAGER.quotationClspQuotationView.key,
  path: ROUTES.ACTUARYMANAGER.quotationClspQuotationView.key,
  component: QuotationClspQuotationView,
  guard: AuthGuard,
  roles: [UserRoles.actuaryManager],
  icon: PiList,
  isSidebar: false,
};
export const quotationClppQuotationView: RouteItem = {
  name: "Quotation CLPP Quotation View",
  id: ROUTES.ACTUARYMANAGER.quotationClppQuotationView.key,
  path: ROUTES.ACTUARYMANAGER.quotationClppQuotationView.key,
  component: QuotationClppQuotationView,
  guard: AuthGuard,
  roles: [UserRoles.actuaryManager],
  icon: PiList,
  isSidebar: false,
};
export const quotationGyrtQuotationView: RouteItem = {
  name: "Quotation GYRT Quotation View",
  id: ROUTES.ACTUARYMANAGER.quotationGyrtQuotationView.key,
  path: ROUTES.ACTUARYMANAGER.quotationGyrtQuotationView.key,
  component: QuotationGyrtQuotationView,
  guard: AuthGuard,
  roles: [UserRoles.actuaryManager],
  icon: PiList,
  isSidebar: false,
};
export const myApprovals: RouteItem = {
  name: "AER Approvals",
  id: ROUTES.ACTUARYMANAGER.myApprovals.key,
  path: ROUTES.ACTUARYMANAGER.myApprovals.key,
  component: MyApprovals,
  guard: AuthGuard,
  roles: [UserRoles.actuaryManager],
  icon: FaFileCircleCheck,
  isSidebar: true,
  children: [aerTableApproval],
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.ACTUARYMANAGER.requestDashboard.key,
  path: ROUTES.ACTUARYMANAGER.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.actuaryManager],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.ACTUARYMANAGER.requestForm.key,
  path: ROUTES.ACTUARYMANAGER.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.actuaryManager],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.ACTUARYMANAGER.viewRequestForm.key,
  path: ROUTES.ACTUARYMANAGER.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.actuaryManager],
  isSidebar: false,
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.ACTUARYMANAGER.notification.key,
  path: ROUTES.ACTUARYMANAGER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.actuaryManager],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.ACTUARYMANAGER.profile.key,
  path: ROUTES.ACTUARYMANAGER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.actuaryManager],
};




export const actuaryManagerRoutes = [
  overview,
  aerTableApproval,
  quotationFipAerView,
  quotationClspQuotationView,
  quotationClppQuotationView,
  quotationGyrtQuotationView,
  myApprovals,
  requestDashboard,
  requestForm,
  viewRequest,
  notification,
  profile
];
