import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";

import AERPage from "@modules/actuary/AER/GYRT";
import <PERSON>reateAER from "@modules/actuary/AER/GYRT/Forms/CreateGYRT";

import ViewQoutationRequest from "@modules/actuary/AER/GYRT/Forms/ViewQuotationRequest";
import RNDDashboard from "@modules/actuary/dashboard";
import { PiFiles } from "react-icons/pi";
import Utilities from "@modules/actuary/utilities";
import { LuFileCog } from "react-icons/lu";
import { HiChevronRight } from "react-icons/hi2";
import MortalityRate from "@modules/actuary/utilities/mortalityRate/components";
import AdminExpense from "@modules/actuary/utilities/adminExpense/components";
import RiskPremiumRate from "@modules/actuary/utilities/riskPremiumRate/components";
import DefaultNumberOfClaims from "@modules/actuary/utilities/defaultNumberOfClaims/components";
import SetCurrentMortalityRate from "@modules/actuary/utilities/mortalityRate/components/forms/setCurrentMortalityRate";
import SetCurrentPICMortalityRate from "@modules/actuary/utilities/mortalityRate/components/forms/setCurrentPICRates";
import BenefitRate from "@modules/actuary/utilities/benefitRate";
import CLSPPage from "@modules/actuary/AER/CLSP";
import viewQuotationRequestCLSP from "@modules/actuary/AER/CLSP/components/Forms/viewQRForm";
import CLSPQuotation from "@modules/actuary/AER/CLSP/components/Forms/createAerCLSP";
import viewClspAER from "@modules/actuary/AER/CLSP/components/Forms/ViewActuaryEvaluationReport";
import CreateAERFIP from "@modules/actuary/AER/FIP/Forms";
import AERPageFIP from "@modules/actuary/AER/FIP";
import ViewQoutationRequestFIP from "@modules/actuary/AER/FIP/Forms/ViewQuotationRequest";
import CreateAERSignatoryFIP from "@modules/actuary/AER/FIP/Forms/Signatory";
import ReviewAERFIP from "@modules/actuary/AER/FIP/Forms/Review";
import CreateAERSignatoryGYRT from "@modules/actuary/AER/GYRT/Forms/Signatory";
import ReviewAERGYRT from "@modules/actuary/AER/GYRT/Forms/Review";
import UtilitiesBenefits from "@modules/admin/product-utilities/benefits/index";
import CLSPAerSignatory from "@modules/actuary/AER/CLSP/components/Forms/ClspSignatories";
import ClspReview from "@modules/actuary/AER/CLSP/components/Forms/ClspReview";
import AERS from "@modules/actuary/AER";
import ClppRate from "@modules/actuary/utilities/clppRate";
import CLPPPage from "@modules/actuary/AER/CLPP";
// import ViewQuotationRequest from "@modules/actuary/AER/CLPP/Components/ViewQuotationRequest";
// import CreateAERCLPPForm from "@modules/actuary/AER/CLPP/Components/Forms/CreateAERCLPPForm";
import { FaGears } from "react-icons/fa6";
import NotificationPage from "@modules/shared/notification";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import CreateAERCLPPForm from "@modules/actuary/AER/CLPP/components/Forms/CreateAERCLPPForm";
import ViewQuotationRequest from "@modules/actuary/AER/CLPP/components/ViewQuotationRequest";
import CLPPAerSignatory from "@modules/actuary/AER/CLPP/components/ClppAerSignatory";
import ClppReview from "@modules/actuary/AER/CLPP/components/Forms/CLPPReview";
import Profile from "@modules/shared/profile";
import { GiNotebook } from "react-icons/gi";
import Approval from "@modules/sales/product-proposal/Approval";
import ProductProposal from "@modules/sales/product-proposal";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.ACTUARY.actuaryDashboard.key,
  path: ROUTES.ACTUARY.actuaryDashboard.key,
  component: RNDDashboard,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  icon: MdDashboard,
  isSidebar: true,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.ACTUARY.notification.key,
  path: ROUTES.ACTUARY.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.user],
  icon: MdDashboard,
  //   isSidebar: true,
};

export const clppRates: RouteItem = {
  name: "CLPP Rate",
  id: ROUTES.ACTUARY.clppRate.key,
  path: ROUTES.ACTUARY.clppRate.key,
  component: ClppRate,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  icon: HiChevronRight,
  isSidebar: false,
};

export const GYRT: RouteItem = {
  name: "GYRT",
  id: ROUTES.ACTUARY.GYRT.key,
  path: ROUTES.ACTUARY.GYRT.key,
  component: AERPage,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  icon: PiFiles,
  isSidebar: false,
};

export const createAERGYRT: RouteItem = {
  name: "Create AER",
  id: ROUTES.ACTUARY.createAerGYRT.key,
  path: ROUTES.ACTUARY.createAerGYRT.key,
  component: CreateAER,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  //icon: PiFiles,
  //isSidebar: true,
};

export const reviewAERGYRT: RouteItem = {
  name: "Review AER",
  id: ROUTES.ACTUARY.reviewAerGYRT.key,
  path: ROUTES.ACTUARY.reviewAerGYRT.key,
  component: ReviewAERGYRT,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  //icon: PiFiles,
  //isSidebar: true,
};

export const viewQoutationRequestGYRT: RouteItem = {
  name: "View Qoutation Request",
  id: ROUTES.ACTUARY.viewQoutationRequestGYRT.key,
  path: ROUTES.ACTUARY.viewQoutationRequestGYRT.key,
  component: ViewQoutationRequest,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  //icon: PiFiles,
  //isSidebar: true,
};
export const createAERSignatoryGYRT: RouteItem = {
  name: "Create AER",
  id: ROUTES.ACTUARY.signatoryGYRT.key,
  path: ROUTES.ACTUARY.signatoryGYRT.key,
  component: CreateAERSignatoryGYRT,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  //icon: PiFiles,
  //isSidebar: true,
};
export const productUtilitiesBenefits: RouteItem = {
  name: "Benefits",
  id: ROUTES.ACTUARY.benefits.key,
  path: ROUTES.ACTUARY.benefits.key,
  component: UtilitiesBenefits,
  guard: AuthGuard,
  roles: [UserRoles.admin],
  isSidebar: true,
  icon: FaGears,
};

//FIP
export const FIP: RouteItem = {
  name: "FIP",
  id: ROUTES.ACTUARY.FIP.key,
  path: ROUTES.ACTUARY.FIP.key,
  component: AERPageFIP,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  icon: PiFiles,
};

export const createAERFIP: RouteItem = {
  name: "Create AER",
  id: ROUTES.ACTUARY.createAerFIP.key,
  path: ROUTES.ACTUARY.createAerFIP.key,
  component: CreateAERFIP,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  //icon: PiFiles,
  //isSidebar: true,
};

export const reviewAERFIP: RouteItem = {
  name: "Review AER",
  id: ROUTES.ACTUARY.reviewAerFIP.key,
  path: ROUTES.ACTUARY.reviewAerFIP.key,
  component: ReviewAERFIP,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  //icon: PiFiles,
  //isSidebar: true,
};

export const createAERSignatoryFIP: RouteItem = {
  name: "Create AER",
  id: ROUTES.ACTUARY.signatoryFIP.key,
  path: ROUTES.ACTUARY.signatoryFIP.key,
  component: CreateAERSignatoryFIP,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  //icon: PiFiles,
  //isSidebar: true,
};

export const viewQoutationRequestFIP: RouteItem = {
  name: "View Qoutation Request",
  id: ROUTES.ACTUARY.viewQoutationRequestFIP.key,
  path: ROUTES.ACTUARY.viewQoutationRequestFIP.key,
  component: ViewQoutationRequestFIP,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  //icon: PiFiles,
  //isSidebar: true,
};

//CLSP
export const CLSP: RouteItem = {
  name: "CLSP",
  id: ROUTES.ACTUARY.CLSP.key,
  path: ROUTES.ACTUARY.CLSP.key,
  component: CLSPPage,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  icon: PiFiles,
  isSidebar: false,
};

export const createCLSP: RouteItem = {
  name: "Create CLSP",
  id: ROUTES.ACTUARY.createCLSPAER.key,
  path: ROUTES.ACTUARY.createCLSPAER.key,
  component: CLSPQuotation,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
};
export const createCLSPSignatory: RouteItem = {
  name: "Create CLSP Signatory",
  id: ROUTES.ACTUARY.createCLSPSignatory.key,
  path: ROUTES.ACTUARY.createCLSPSignatory.key,
  component: CLSPAerSignatory,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
};

export const viewquotationRequestCLSP: RouteItem = {
  id: ROUTES.ACTUARY.viewQuotationRequestCLSP.key,
  name: "View Quotation Request",
  path: ROUTES.ACTUARY.viewQuotationRequestCLSP.key,
  component: viewQuotationRequestCLSP,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
};

export const viewCLSPAer: RouteItem = {
  id: ROUTES.ACTUARY.viewCLSPAER.key,
  name: "View CLSP AER",
  path: ROUTES.ACTUARY.viewCLSPAER.key,
  component: viewClspAER,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
};

export const reviewCLSP: RouteItem = {
  id: ROUTES.ACTUARY.reviewCLSP.key,
  name: "Review CLSP",
  path: ROUTES.ACTUARY.reviewCLSP.key,
  component: ClspReview,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
};
export const mortalityRate: RouteItem = {
  name: "Morality Rate",
  id: ROUTES.ACTUARY.mortalityRate.key,
  path: ROUTES.ACTUARY.mortalityRate.key,
  component: MortalityRate,
  guard: AuthGuard,
  icon: HiChevronRight,
};

export const adminExpense: RouteItem = {
  name: "Admin Expense",
  id: ROUTES.ACTUARY.adminExpense.key,
  path: ROUTES.ACTUARY.adminExpense.key,
  component: AdminExpense,
  guard: AuthGuard,
  icon: HiChevronRight,
};

export const riskPremiumRate: RouteItem = {
  name: "Risk Premium Rate",
  id: ROUTES.ACTUARY.riskPremiumRate.key,
  path: ROUTES.ACTUARY.riskPremiumRate.key,
  component: RiskPremiumRate,
  guard: AuthGuard,
  icon: HiChevronRight,
};

export const defaultNumberOfClaims: RouteItem = {
  name: "Default Number of Claims",
  id: ROUTES.ACTUARY.defaultNumberOfClaims.key,
  path: ROUTES.ACTUARY.defaultNumberOfClaims.key,
  component: DefaultNumberOfClaims,
  guard: AuthGuard,
  icon: HiChevronRight,
};

export const benefitRate: RouteItem = {
  name: "Benefit Rate",
  id: ROUTES.ACTUARY.benefitRate.key,
  path: ROUTES.ACTUARY.benefitRate.key,
  component: BenefitRate,
  guard: AuthGuard,
  icon: HiChevronRight,
};

export const GYRTMortalities: RouteItem = {
  name: "GYRT Mortality Rate",
  id: ROUTES.ACTUARY.GYRTMortalities.key,
  path: ROUTES.ACTUARY.GYRTMortalities.key,
  component: SetCurrentMortalityRate,
  guard: AuthGuard,
};

export const CLSPMortalities: RouteItem = {
  name: "CLSP Mortality Rate",
  id: ROUTES.ACTUARY.CLSPMortalies.key,
  path: ROUTES.ACTUARY.CLSPMortalies.key,
  component: SetCurrentMortalityRate,
  guard: AuthGuard,
};

export const PICMortalities: RouteItem = {
  name: "PIC Mortality Rate",
  id: ROUTES.ACTUARY.PICMortalities.key,
  path: ROUTES.ACTUARY.PICMortalities.key,
  component: SetCurrentPICMortalityRate,
  guard: AuthGuard,
};

export const utilities: RouteItem = {
  name: "Utilities",
  id: ROUTES.ACTUARY.utilities.key,
  path: ROUTES.ACTUARY.utilities.key,
  component: Utilities,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  icon: LuFileCog,
  isSidebar: true,
  children: [mortalityRate, adminExpense, riskPremiumRate, defaultNumberOfClaims, benefitRate, clppRates],
};

export const CLPP: RouteItem = {
  name: "CLPP",
  id: ROUTES.ACTUARY.CLPP.key,
  path: ROUTES.ACTUARY.CLPP.key,
  component: CLPPPage,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  icon: PiFiles,
  isSidebar: false,
};

export const viewquotationRequestCLPP: RouteItem = {
  id: ROUTES.ACTUARY.viewQuotationRequestCLPP.key,
  name: "View CLPP Quotation Request",
  path: ROUTES.ACTUARY.viewQuotationRequestCLPP.key,
  component: ViewQuotationRequest,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
};

export const createAERCLPP: RouteItem = {
  name: "Create CLPP",
  id: ROUTES.ACTUARY.createClppAER.key,
  path: ROUTES.ACTUARY.createClppAER.key,
  component: CreateAERCLPPForm,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
};

export const createCLPPSignatory: RouteItem = {
  name: "Create CLPP Signatory",
  id: ROUTES.ACTUARY.createCLPPSignatory.key,
  path: ROUTES.ACTUARY.createCLPPSignatory.key,
  component: CLPPAerSignatory,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
};

export const clppReview: RouteItem = {
  name: "Review CLPP",
  id: ROUTES.ACTUARY.reviewCLPP.key,
  path: ROUTES.ACTUARY.reviewCLPP.key,
  component: ClppReview,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
};

export const AER: RouteItem = {
  name: "AER",
  id: ROUTES.ACTUARY.AER.key,
  path: ROUTES.ACTUARY.AER.key,
  component: AERS,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  icon: LuFileCog,
  isSidebar: true,
  children: [GYRT, CLPP, CLSP, FIP],
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.ACTUARY.requestDashboard.key,
  path: ROUTES.ACTUARY.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.ACTUARY.requestForm.key,
  path: ROUTES.ACTUARY.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.ACTUARY.viewRequestForm.key,
  path: ROUTES.ACTUARY.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  isSidebar: false,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.ACTUARY.profile.key,
  path: ROUTES.ACTUARY.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
};
export const proposal: RouteItem = {
  name: "Proposal",
  id: ROUTES.ACTUARY.productProposal.key,
  path: ROUTES.ACTUARY.productProposal.key,
  component: ProductProposal,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  icon: GiNotebook,
  isSidebar: true,
};
export const viewProposal: RouteItem = {
  name: "View Proposal",
  id: ROUTES.ACTUARY.viewProductProposal.key,
  path: ROUTES.ACTUARY.viewProductProposal.key,
  component: Approval,
  guard: AuthGuard,
  roles: [UserRoles.actuary],
  icon: GiNotebook,
  isSidebar: false,
};

export const actuaryRoutes = [
  overview,

  GYRT,
  CLSP,
  FIP,
  AER,
  createAERGYRT,
  createAERSignatoryGYRT,
  reviewAERGYRT,
  viewQoutationRequestGYRT,
  utilities,
  mortalityRate,
  adminExpense,
  riskPremiumRate,
  benefitRate,
  defaultNumberOfClaims,
  GYRTMortalities,
  PICMortalities,
  CLSPMortalities,
  GYRT,
  CLPP,
  viewquotationRequestCLSP,
  createCLSP,
  viewCLSPAer,
  createCLSPSignatory,
  createAERFIP,
  reviewAERFIP,
  createAERSignatoryFIP,
  viewQoutationRequestFIP,
  reviewCLSP,
  clppRates,
  viewquotationRequestCLPP,
  createAERCLPP,
  productUtilitiesBenefits,
  notification,
  requestDashboard,
  requestForm,
  viewRequest,
  createCLPPSignatory,
  clppReview,
  profile,
  proposal,
  viewProposal,
];
