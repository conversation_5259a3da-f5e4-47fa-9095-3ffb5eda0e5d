import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import IncomingOutgoingCashierDashboard from "@modules/dashboard/IncomingOutgoingCashierDashboard.tsx";
import SeriesAssignment from "@modules/admin/form-inventory-and-tracking/series-assignment";
import SeriesAssignmentForm from "@modules/admin/form-inventory-and-tracking/series-assignment/SeriesAssignmentForm";
import ViewSeriesAssignment from "@modules/admin/form-inventory-and-tracking/series-assignment/ViewSeriesAssignment";
import StatusTracking from "@modules/admin/form-inventory-and-tracking/status-tracking";
import StatusTrackingView from "@modules/admin/form-inventory-and-tracking/status-tracking/statusTrackingView";
import { FaUserEdit, FaSearchLocation, FaFile } from "react-icons/fa";
import { FaFileLines, FaListCheck } from "react-icons/fa6";
import VerificationList from "@modules/admin/form-inventory-and-tracking/verification-list";
import VerificationListForm from "@modules/admin/form-inventory-and-tracking/verification-list/verificationListForm";
import ViewTransmittal from "@modules/admin/form-inventory-and-tracking/new-forms/outgoing/components/transmittalList";
import { HiChevronRight } from "react-icons/hi2";
import ViewTransmittalForm from "@modules/admin/form-inventory-and-tracking/new-forms/outgoing/components/ViewTransmittalForm";
import Inventory from "@modules/admin/form-inventory-and-tracking/inventory";
import InventoryNewForms from "@modules/admin/form-inventory-and-tracking/inventory/newForms";
import InventoryUsedForms from "@modules/admin/form-inventory-and-tracking/inventory/usedForms";
import InventoryVerifiedList from "@modules/admin/form-inventory-and-tracking/inventory/verifiedList";
import NotificationPage from "@modules/shared/notification";
// import HeadCashierUsedForms from "@modules/admin/form-inventory-and-tracking/used-forms";
import RequestPads from "@modules/chief-cashier/request-pads";
import { TbMessageForward } from "react-icons/tb";
import ViewRequestPadForm from "@modules/chief-cashier/components/form/view-request-pad-form";
import HeadCashierUsedForms from "@modules/chief-cashier/used-forms";
import Profile from "@modules/shared/profile";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.CHIEFCASHIER.chiefCashierDashboard.key,
  path: ROUTES.CHIEFCASHIER.chiefCashierDashboard.key,
  component: IncomingOutgoingCashierDashboard,
  guard: AuthGuard,
  roles: [UserRoles.chiefCashier],
  icon: MdDashboard,
  isSidebar: true,
};
export const verificationList: RouteItem = {
  name: "Verification List",
  id: ROUTES.CHIEFCASHIER.verificationList.key,
  path: ROUTES.CHIEFCASHIER.verificationList.key,
  component: VerificationList,
  guard: AuthGuard,
  roles: [UserRoles.chiefCashier],
  icon: FaListCheck,
  isSidebar: true,
};
export const verificationListForm: RouteItem = {
  name: "Verification List Form",
  id: ROUTES.CHIEFCASHIER.verificationListForm.key,
  path: ROUTES.CHIEFCASHIER.verificationListForm.key,
  component: VerificationListForm,
  guard: AuthGuard,
  roles: [UserRoles.chiefCashier],
  isSidebar: false,
};
export const seriesAssigment: RouteItem = {
  name: "Series Assignment",
  id: ROUTES.CHIEFCASHIER.seriesAssignment.key,
  path: ROUTES.CHIEFCASHIER.seriesAssignment.key,
  component: SeriesAssignment,
  guard: AuthGuard,
  roles: [UserRoles.chiefCashier],
  icon: FaUserEdit,
  isSidebar: true,
};
export const seriesAssigmentForm: RouteItem = {
  name: "Series Assignment Form",
  id: ROUTES.CHIEFCASHIER.seriesAssignmentForm.key,
  path: ROUTES.CHIEFCASHIER.seriesAssignmentForm.key,
  component: SeriesAssignmentForm,
  guard: AuthGuard,
  roles: [UserRoles.chiefCashier],
  isSidebar: false,
};
export const viewSeriesAssignment: RouteItem = {
  name: "View Series Assignment",
  id: ROUTES.CHIEFCASHIER.viewSeriesAssignment.key,
  path: ROUTES.CHIEFCASHIER.viewSeriesAssignment.key,
  component: ViewSeriesAssignment,
  guard: AuthGuard,
  roles: [UserRoles.chiefCashier],
  isSidebar: false,
};
export const statusTracking: RouteItem = {
  name: "Status Tracking",
  id: ROUTES.CHIEFCASHIER.statusTracking.key,
  path: ROUTES.CHIEFCASHIER.statusTracking.key,
  component: StatusTracking,
  guard: AuthGuard,
  roles: [UserRoles.chiefCashier],
  icon: FaSearchLocation,
  isSidebar: true,
};
export const statusTrackingView: RouteItem = {
  name: "Status Tracking View",
  id: ROUTES.CHIEFCASHIER.statusTrackingView.key,
  path: ROUTES.CHIEFCASHIER.statusTrackingView.key,
  component: StatusTrackingView,
  guard: AuthGuard,
  roles: [UserRoles.chiefCashier],
  isSidebar: false,
};

export const viewTransmittal: RouteItem = {
  name: "View Transmittal",
  id: ROUTES.CHIEFCASHIER.viewNewTransmittal.key,
  path: ROUTES.CHIEFCASHIER.viewNewTransmittal.key,
  component: ViewTransmittal,
  guard: AuthGuard,
  roles: [UserRoles.chiefCashier],
  icon: HiChevronRight,
};
export const viewNewTransmittalForm: RouteItem = {
  name: "View Transmittal Form",
  id: ROUTES.CHIEFCASHIER.viewNewTransmittalForm.key,
  path: ROUTES.CHIEFCASHIER.viewNewTransmittalForm.key,
  component: ViewTransmittalForm,
  guard: AuthGuard,
  roles: [UserRoles.chiefCashier],
  icon: HiChevronRight,
};

//iNVENTORY
export const verifiedList: RouteItem = {
  name: "Verified List",
  id: ROUTES.CHIEFCASHIER.inventoryVerifiedList.key,
  path: ROUTES.CHIEFCASHIER.inventoryVerifiedList.key,
  component: InventoryVerifiedList,
  guard: AuthGuard,
  roles: [UserRoles.chiefCashier],
  icon: HiChevronRight,
};
export const inventoryNewForms: RouteItem = {
  name: "New Forms",
  id: ROUTES.CHIEFCASHIER.inventoryNewForms.key,
  path: ROUTES.CHIEFCASHIER.inventoryNewForms.key,
  component: InventoryNewForms,
  guard: AuthGuard,
  roles: [UserRoles.chiefCashier],
  icon: HiChevronRight,
};
export const inventoryUsedForms: RouteItem = {
  name: "Used Forms",
  id: ROUTES.CHIEFCASHIER.inventoryUsedForms.key,
  path: ROUTES.CHIEFCASHIER.inventoryUsedForms.key,
  component: InventoryUsedForms,
  guard: AuthGuard,
  roles: [UserRoles.chiefCashier],
  icon: HiChevronRight,
};
export const inventory: RouteItem = {
  name: "Inventory",
  id: ROUTES.CHIEFCASHIER.inventory.key,
  path: ROUTES.CHIEFCASHIER.inventory.key,
  component: Inventory,
  guard: AuthGuard,
  roles: [UserRoles.chiefCashier],
  icon: FaFileLines,
  isSidebar: true,
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.CHIEFCASHIER.notification.key,
  path: ROUTES.CHIEFCASHIER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.chiefCashier],
  icon: MdDashboard,
  //   isSidebar: true,
};

export const usedForms: RouteItem = {
  name: "Used Forms",
  id: ROUTES.CHIEFCASHIER.usedForms.key,
  path: ROUTES.CHIEFCASHIER.usedForms.key,
  component: HeadCashierUsedForms,
  guard: AuthGuard,
  roles: [UserRoles.treasury],
  icon: FaFile,
  isSidebar: true,
};

export const recieveReturnForms: RouteItem = {
  name: "Receive Return Forms",
  id: ROUTES.CHIEFCASHIER.forReturnedReceivingForm.key,
  path: ROUTES.CHIEFCASHIER.forReturnedReceivingForm.key,
  component: HeadCashierUsedForms,
  guard: AuthGuard,
  roles: [UserRoles.chiefCashier],
  icon: FaFile,
  isSidebar: false,
};

export const requestPads: RouteItem = {
  name: "Request Pads",
  id: ROUTES.CHIEFCASHIER.requestPads.key,
  path: ROUTES.CHIEFCASHIER.requestPads.key,
  component: RequestPads,
  guard: AuthGuard,
  roles: [UserRoles.chiefCashier],
  icon: TbMessageForward,
  isSidebar: true,
};

export const viewRequestPadForm: RouteItem = {
  name: "View Request Pads",
  id: ROUTES.CHIEFCASHIER.viewRequestPadForm.key,
  path: ROUTES.CHIEFCASHIER.viewRequestPadForm.key,
  component: ViewRequestPadForm,
  guard: AuthGuard,
  roles: [UserRoles.chiefCashier],
  isSidebar: false,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.CHIEFCASHIER.profile.key,
  path: ROUTES.CHIEFCASHIER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.chiefCashier],
};
export const chiefCashierRoutes = [
  overview,
  verificationList,
  verificationListForm,
  seriesAssigment,
  seriesAssigmentForm,
  viewSeriesAssignment,
  statusTracking,
  statusTrackingView,
  viewTransmittal,
  viewNewTransmittalForm,
  inventory,
  inventoryNewForms,
  inventoryUsedForms,
  notification,
  usedForms,
  requestPads,
  viewRequestPadForm,
  profile,
];
