import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import UsersDashboard from "@modules/users/dashboard";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.ACCOUNTING.dashboard.key,
  path: ROUTES.ACCOUNTING.dashboard.key,
  component: UsersDashboard,
  guard: AuthGuard,
  roles: [UserRoles.accounting],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.ACCOUNTING.requestDashboard.key,
  path: ROUTES.ACCOUNTING.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.accounting],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.ACCOUNTING.requestForm.key,
  path: ROUTES.ACCOUNTING.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.accounting],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.ACCOUNTING.viewRequestForm.key,
  path: ROUTES.ACCOUNTING.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.claimsManager],
  isSidebar: false,
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.ACCOUNTING.notification.key,
  path: ROUTES.ACCOUNTING.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.accounting],
  icon: MdDashboard,
  //   isSidebar: true,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.ACCOUNTING.profile.key,
  path: ROUTES.ACCOUNTING.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.accounting],
};

export const accountingRoutes = [overview, requestDashboard, requestForm, viewRequest, notification, profile];
