import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles} from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import ProductProposalApproval from "@modules/admin/product-proposal-approval-underwriting";
import ViewProductProposal from "@modules/admin/product-proposal-approval-underwriting/Components/viewForm";
import UnderwritingDashboard from "@modules/dashboard/UnderwritingDashboard";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import { LuFileCheck } from "react-icons/lu";
import { MdDashboard } from "react-icons/md";


export const overview: RouteItem = {
    name: "Dashboard",
    id: ROUTES.UNDERWRITING.underwritingDashboard.key,
    path: ROUTES.UNDERWRITING.underwritingDashboard.key,
    component: UnderwritingDashboard,
    guard: AuthGuard,
    roles: [UserRoles.underwriting],
    icon: MdDashboard,
    isSidebar: true,
  };
  


export const productProposalApproval: RouteItem = {
    name: "My Approvals",
    id: ROUTES.UNDERWRITING.UnderwritingProductProposalApproval.key,
    path: ROUTES.UNDERWRITING.UnderwritingProductProposalApproval.key,
    component: ProductProposalApproval,
    guard: AuthGuard,
    isSidebar: true,
    icon: LuFileCheck,
    roles: [UserRoles.underwriting],
  };
  
  export const viewProductProposalApproval: RouteItem = {
    name: "My Approvals",
    id: ROUTES.UNDERWRITING.viewUnderwritingProductProposalApproval.key,
    path: ROUTES.UNDERWRITING.viewUnderwritingProductProposalApproval.key,
    component: ViewProductProposal,
    guard: AuthGuard,
    isSidebar: false,
    icon: LuFileCheck,
    roles: [UserRoles.underwriting],
  };
  export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.UNDERWRITING.notification.key,
  path: ROUTES.UNDERWRITING.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.underwriting],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.UNDERWRITING.profile.key,
  path: ROUTES.UNDERWRITING.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.underwriting],
};


export const underwritingRoutes = [
   overview,
   productProposalApproval,
   viewProductProposalApproval,
   notification,
   profile,
];


