import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import Dashboard from "@modules/dashboard";
import { HiChevronRight } from "react-icons/hi2";
import CommissionAndRequirements from "@modules/marketing/validation/commission-and-requirements";
import MyApprovals from "@modules/admin/approval-aer";
import { FaFileCircleCheck } from "react-icons/fa6";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import Approval from "@modules/marketing/validation/commission-and-requirements/Approval";
export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.AREASALESMANAGER.dashboard.key,
  path: ROUTES.AREASALESMANAGER.dashboard.key,
  component: Dashboard,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
  icon: MdDashboard,
  isSidebar: true,
};
export const commissionAndRequirements: RouteItem = {
  name: "Commission and Requirements",
  id: ROUTES.AREASALESMANAGER.commissionAndRequirements.key,
  path: ROUTES.AREASALESMANAGER.commissionAndRequirements.key,
  component: CommissionAndRequirements,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
  icon: HiChevronRight,
};

export const myApprovals: RouteItem = {
  name: "My Approvals",
  id: ROUTES.AREASALESMANAGER.myApprovals.key,
  path: ROUTES.AREASALESMANAGER.myApprovals.key,
  component: MyApprovals,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
  icon: FaFileCircleCheck,
  isSidebar: true,
  children: [commissionAndRequirements],
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.AREASALESMANAGER.notification.key,
  path: ROUTES.AREASALESMANAGER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.AREASALESMANAGER.profile.key,
  path: ROUTES.AREASALESMANAGER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
};

export const approval: RouteItem = {
  name: "Approval",
  id: ROUTES.AREASALESMANAGER.viewProductProposalSignatory.key,
  path: ROUTES.AREASALESMANAGER.viewProductProposalSignatory.key,
  component: Approval,
  guard: AuthGuard,
  roles: [UserRoles.areaSalesManager],
  isSidebar: false,
};

export const areaSalesManagerRoutes = [overview, myApprovals, commissionAndRequirements, notification, approval, profile];
