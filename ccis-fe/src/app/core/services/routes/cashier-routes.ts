import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import RNDDashboard from "@modules/actuary/dashboard";
import { PiCertificate } from "react-icons/pi";
// import SetCurrentPICMortalityRate from "@modules/actuary/utilities/mortalityRate/components/forms/setCurrentPICRates";
import Shares from "@modules/admin/shares";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.CASHIER.actuaryDashboard.key,
  path: ROUTES.CASHIER.actuaryDashboard.key,
  component: RNDDashboard,
  guard: AuthGuard,
  roles: [UserRoles.cashier],
  icon: MdDashboard,
  isSidebar: true,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.CASHIER.notification.key,
  path: ROUTES.CASHIER.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.user],
  icon: MdDashboard,
  //   isSidebar: true,
};

export const shares: RouteItem = {
  name: "Shares",
  id: ROUTES.CASHIER.shares.key,
  path: ROUTES.CASHIER.shares.key,
  component: Shares,
  guard: AuthGuard,
  roles: [UserRoles.cashier],
  icon: PiCertificate,
  isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.CASHIER.profile.key,
  path: ROUTES.CASHIER.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.cashier],
};

export const cashierRoutes = [overview, shares, notification, profile];
