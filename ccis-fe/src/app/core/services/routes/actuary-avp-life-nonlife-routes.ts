import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
// import RNDDashboard from "@modules/actuary/dashboard";
import RNDDashboard from "@modules/dashboard";
import { FaFileCircleCheck } from "react-icons/fa6";
import { QuotationClppQuotationView } from "@modules/sales/view-aer/clpp";
import { PiList } from "react-icons/pi";
import { QuotationGyrtQuotationView } from "@modules/sales/view-aer/gyrt";
import { QuotationClspQuotationView } from "@modules/sales/view-aer/clsp";
import QuotationFipAerView from "@modules/sales/view-aer/fipViewPreview";
import AerApprovalTable from "@modules/admin/approval-aer/table/AerApprovalTable";
import MyApprovals from "@modules/admin/approval-aer";
import { HiChevronRight } from "react-icons/hi2";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.AVPFORLIFEANDNONLIFE.dashboard.key,
  path: ROUTES.AVPFORLIFEANDNONLIFE.dashboard.key,
  component: RNDDashboard,
  guard: AuthGuard,
  roles: [UserRoles.avpLifeNonLife],
  icon: MdDashboard,
  isSidebar: true,
};
export const aerTableApproval: RouteItem = {
  name: "My Approval",
  id: ROUTES.AVPFORLIFEANDNONLIFE.aerApproval.key,
  path: ROUTES.AVPFORLIFEANDNONLIFE.aerApproval.key,
  component: AerApprovalTable,
  guard: AuthGuard,
  roles: [UserRoles.avpLifeNonLife],
  icon: HiChevronRight,
  isSidebar: false,
};
export const quotationFipAerView: RouteItem = {
  name: "Quotation FIP Aer View",
  id: ROUTES.AVPFORLIFEANDNONLIFE.quotationFipAerView.key,
  path: ROUTES.AVPFORLIFEANDNONLIFE.quotationFipAerView.key,
  component: QuotationFipAerView,
  guard: AuthGuard,
  roles: [UserRoles.avpLifeNonLife],
  icon: PiList,
  isSidebar: false,
};
export const quotationClspQuotationView: RouteItem = {
  name: "Quotation CLSP Quotation View",
  id: ROUTES.AVPFORLIFEANDNONLIFE.quotationClspQuotationView.key,
  path: ROUTES.AVPFORLIFEANDNONLIFE.quotationClspQuotationView.key,
  component: QuotationClspQuotationView,
  guard: AuthGuard,
  roles: [UserRoles.avpLifeNonLife],
  icon: PiList,
  isSidebar: false,
};
export const quotationClppQuotationView: RouteItem = {
  name: "Quotation CLPP Quotation View",
  id: ROUTES.AVPFORLIFEANDNONLIFE.quotationClppQuotationView.key,
  path: ROUTES.AVPFORLIFEANDNONLIFE.quotationClppQuotationView.key,
  component: QuotationClppQuotationView,
  guard: AuthGuard,
  roles: [UserRoles.avpLifeNonLife],
  icon: PiList,
  isSidebar: false,
};
export const quotationGyrtQuotationView: RouteItem = {
  name: "Quotation GYRT Quotation View",
  id: ROUTES.AVPFORLIFEANDNONLIFE.quotationGyrtQuotationView.key,
  path: ROUTES.AVPFORLIFEANDNONLIFE.quotationGyrtQuotationView.key,
  component: QuotationGyrtQuotationView,
  guard: AuthGuard,
  roles: [UserRoles.avpLifeNonLife],
  icon: PiList,
  isSidebar: false,
};
export const myApprovals: RouteItem = {
  name: "AER Approvals",
  id: ROUTES.AVPFORLIFEANDNONLIFE.myApprovals.key,
  path: ROUTES.AVPFORLIFEANDNONLIFE.myApprovals.key,
  component: MyApprovals,
  guard: AuthGuard,
  roles: [UserRoles.avpLifeNonLife],
  icon: FaFileCircleCheck,
  isSidebar: true,
  children: [aerTableApproval],
};
export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.AVPFORLIFEANDNONLIFE.requestDashboard.key,
  path: ROUTES.AVPFORLIFEANDNONLIFE.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.avpLifeNonLife],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.AVPFORLIFEANDNONLIFE.requestForm.key,
  path: ROUTES.AVPFORLIFEANDNONLIFE.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.avpLifeNonLife],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.AVPFORLIFEANDNONLIFE.viewRequestForm.key,
  path: ROUTES.AVPFORLIFEANDNONLIFE.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.avpLifeNonLife],
  isSidebar: false,
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.AVPFORLIFEANDNONLIFE.notification.key,
  path: ROUTES.AVPFORLIFEANDNONLIFE.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.avpLifeNonLife],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.AVPFORLIFEANDNONLIFE.profile.key,
  path: ROUTES.AVPFORLIFEANDNONLIFE.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.avpLifeNonLife],
};

export const avplifenonlifeRoutes = [
  overview,
  aerTableApproval,
  quotationFipAerView,
  quotationClspQuotationView,
  quotationClppQuotationView,
  quotationGyrtQuotationView,
  myApprovals,
  requestDashboard,
  requestForm,
  viewRequest,
  notification,
  profile,
];
