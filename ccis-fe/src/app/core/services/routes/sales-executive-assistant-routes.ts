import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import SalesExecutiveAssistantDashboard from "@modules/dashboard/SalesExecutiveAssistantDashboard";
import ProductNotarization from "@modules/admin/product-notarization";
import PartnershipAgreementNotary from "@modules/admin/product-notarization/Form/PartnershipAgreementForm";
import { LuStamp } from "react-icons/lu";
import { MdDashboard } from "react-icons/md";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.SALESEXECUTIVEASSISTANT.salesExecutiveAssistantDashboard.key,
  path: ROUTES.SALESEXECUTIVEASSISTANT.salesExecutiveAssistantDashboard.key,
  component: SalesExecutiveAssistantDashboard,
  guard: AuthGuard,
  roles: [UserRoles.sea],
  icon: MdDashboard,
  isSidebar: true,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.SALESEXECUTIVEASSISTANT.notification.key,
  path: ROUTES.SALESEXECUTIVEASSISTANT.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.user],
  icon: MdDashboard,
  //   isSidebar: true,
};

export const notarization: RouteItem = {
  name: "Notarization",
  id: ROUTES.SALESEXECUTIVEASSISTANT.notarization.key,
  path: ROUTES.SALESEXECUTIVEASSISTANT.notarization.key,
  component: ProductNotarization,
  guard: AuthGuard,
  roles: [UserRoles.sea],
  icon: LuStamp,
  isSidebar: true,
};

export const partnershipAgreement: RouteItem = {
  name: "Partnership Agreement",
  id: ROUTES.SALESEXECUTIVEASSISTANT.partnershipAgreementNotary.key,
  path: ROUTES.SALESEXECUTIVEASSISTANT.partnershipAgreementNotary.key,
  component: PartnershipAgreementNotary,
  guard: AuthGuard,
  roles: [UserRoles.sea],
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.SALESEXECUTIVEASSISTANT.profile.key,
  path: ROUTES.SALESEXECUTIVEASSISTANT.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.sea],
};

export const salesExecutiveAssistantRoutes = [overview, notarization, partnershipAgreement, notification, profile];
