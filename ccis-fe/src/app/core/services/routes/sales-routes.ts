import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import SalesDashboard from "@modules/dashboard/SalesDashboard";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, PiList } from "react-icons/pi";
// import SetCurrentPICMortalityRate from "@modules/actuary/utilities/mortalityRate/components/forms/setCurrentPICRates";
import Shares from "@modules/admin/shares";
import Quotations from "@modules/sales/quotations";
import GYRTQuotation from "@modules/sales/gyrt-quotations";
import { QuotationGyrtAerView, QuotationGyrtQuotationView } from "@modules/sales/view-aer/gyrt";
import { QuotationClppAerView, QuotationClppQuotationView } from "@modules/sales/view-aer/clpp";
import CLPPQuotation from "@modules/sales/clpp-quotations";
import CLSPQuotation from "@modules/sales/clsp-quotations";
import FIPQuotation from "@modules/sales/fip-quotations";
import { QuotationClspAerView, QuotationClspQuotationView } from "@modules/sales/view-aer/clsp";
import ProductProposal from "@modules/sales/product-proposal";
import { GiNotebook } from "react-icons/gi";
import ProductProposalForm from "@modules/sales/product-proposal/Components/Forms/ProductProposalForm";
import QuotationFipAerView from "@modules/sales/view-aer/fipViewPreview";
import Approval from "@modules/sales/product-proposal/Approval";
import NotificationPage from "@modules/shared/notification";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import Profile from "@modules/shared/profile";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.SALES.salesDashboard.key,
  path: ROUTES.SALES.salesDashboard.key,
  component: SalesDashboard,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  icon: MdDashboard,
  isSidebar: true,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.SALES.notification.key,
  path: ROUTES.SALES.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.user],
  icon: MdDashboard,
  //   isSidebar: true,
};

export const shares: RouteItem = {
  name: "Shares",
  id: ROUTES.SALES.shares.key,
  path: ROUTES.SALES.shares.key,
  component: Shares,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  icon: PiCertificate,
  isSidebar: true,
};

export const quotations: RouteItem = {
  name: "Quotations",
  id: ROUTES.SALES.quotations.key,
  path: ROUTES.SALES.quotations.key,
  component: Quotations,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  icon: PiList,
  isSidebar: true,
};

export const quotationGyrtAerView: RouteItem = {
  name: "Quotation GYRT Aer View",
  id: ROUTES.SALES.quotationGyrtAerView.key,
  path: ROUTES.SALES.quotationGyrtAerView.key,
  component: QuotationGyrtAerView,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  icon: PiList,
  isSidebar: false,
};

export const quotationGyrtQuotationView: RouteItem = {
  name: "Quotation GYRT Quotation View",
  id: ROUTES.SALES.quotationGyrtQuotationView.key,
  path: ROUTES.SALES.quotationGyrtQuotationView.key,
  component: QuotationGyrtQuotationView,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  icon: PiList,
  isSidebar: false,
};

export const quotationClppAerView: RouteItem = {
  name: "Quotation CLPP Aer View",
  id: ROUTES.SALES.quotationClppAerView.key,
  path: ROUTES.SALES.quotationClppAerView.key,
  component: QuotationClppAerView,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  icon: PiList,
  isSidebar: false,
};

export const quotationClppQuotationView: RouteItem = {
  name: "Quotation CLPP Quotation View",
  id: ROUTES.SALES.quotationClppQuotationView.key,
  path: ROUTES.SALES.quotationClppQuotationView.key,
  component: QuotationClppQuotationView,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  icon: PiList,
  isSidebar: false,
};

export const quotationClspAerView: RouteItem = {
  name: "Quotation CLSP Aer View",
  id: ROUTES.SALES.quotationClspAerView.key,
  path: ROUTES.SALES.quotationClspAerView.key,
  component: QuotationClspAerView,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  icon: PiList,
  isSidebar: false,
};

export const quotationClspQuotationView: RouteItem = {
  name: "Quotation CLSP Quotation View",
  id: ROUTES.SALES.quotationClspQuotationView.key,
  path: ROUTES.SALES.quotationClspQuotationView.key,
  component: QuotationClspQuotationView,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  icon: PiList,
  isSidebar: false,
};

export const GYRTQuotations: RouteItem = {
  name: "GYRT Quotations",
  id: ROUTES.SALES.gyrtQuotation.key,
  path: ROUTES.SALES.gyrtQuotation.key,
  component: GYRTQuotation,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  icon: PiList,
  isSidebar: false,
};

export const CLPPQuotations: RouteItem = {
  name: "CLPP Quotations",
  id: ROUTES.SALES.clppQuotation.key,
  path: ROUTES.SALES.clppQuotation.key,
  component: CLPPQuotation,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  icon: PiList,
  isSidebar: false,
};

export const FIPQuotations: RouteItem = {
  name: "FIP Quotations",
  id: ROUTES.SALES.fipQuotation.key,
  path: ROUTES.SALES.fipQuotation.key,
  component: FIPQuotation,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  icon: PiList,
  isSidebar: false,
};

export const CLSPQuotations: RouteItem = {
  name: "CLSP Quotations",
  id: ROUTES.SALES.clspQuotation.key,
  path: ROUTES.SALES.clspQuotation.key,
  component: CLSPQuotation,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  icon: PiList,
  isSidebar: false,
};

export const proposal: RouteItem = {
  name: "Proposal",
  id: ROUTES.SALES.productProposal.key,
  path: ROUTES.SALES.productProposal.key,
  component: ProductProposal,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  icon: GiNotebook,
  isSidebar: true,
};

export const createProductProposal: RouteItem = {
  name: "Create Product Proposal",
  id: ROUTES.SALES.createProductProposal.key,
  path: ROUTES.SALES.createProductProposal.key,
  component: ProductProposalForm,
  guard: AuthGuard,
  roles: [UserRoles.sales],
};
export const editProductProposal: RouteItem = {
  name: "Edit Product Proposal",
  id: ROUTES.SALES.editProductProposal.key,
  path: ROUTES.SALES.editProductProposal.key,
  component: ProductProposalForm,
  guard: AuthGuard,
  roles: [UserRoles.sales],
};
export const quotationFipAerView: RouteItem = {
  name: "Quotation FIP Aer View",
  id: ROUTES.SALES.quotationFipAerView.key,
  path: ROUTES.SALES.quotationFipAerView.key,
  component: QuotationFipAerView,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  icon: PiList,
  isSidebar: false,
};
export const viewProposal: RouteItem = {
  name: "View Proposal",
  id: ROUTES.SALES.viewProductProposal.key,
  path: ROUTES.SALES.viewProductProposal.key,
  component: Approval,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  icon: GiNotebook,
  isSidebar: false,
};
export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.SALES.requestDashboard.key,
  path: ROUTES.SALES.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.SALES.requestForm.key,
  path: ROUTES.SALES.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.SALES.viewRequestForm.key,
  path: ROUTES.SALES.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.sales],
  isSidebar: false,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.SALES.profile.key,
  path: ROUTES.SALES.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.sales],
};

export const salesRoutes = [
  overview,
  shares,
  proposal,
  quotations,
  quotationGyrtAerView,
  quotationGyrtQuotationView,
  quotationClppAerView,
  quotationClppQuotationView,
  quotationClspAerView,
  quotationClspQuotationView,
  GYRTQuotations,
  CLPPQuotations,
  CLSPQuotations,
  FIPQuotations,
  createProductProposal,
  editProductProposal,
  quotationFipAerView,
  viewProposal,
  notification,
  requestDashboard,
  requestForm,
  viewRequest,
  profile,
];
