import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import { HiChevronRight } from "react-icons/hi";
import { MdOutlineFactCheck } from "react-icons/md";
import MarketingDashboard from "@modules/marketing/dashboard";
import Validation from "@modules/marketing/validation";
import CommissionAndRequirements from "@modules/marketing/validation/commission-and-requirements";
import Approval from "@modules/marketing/validation/commission-and-requirements/Approval";
import Shares from "@modules/admin/shares";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import ProductProposal from "@modules/sales/product-proposal";
import { GiNotebook } from "react-icons/gi";
import ProposalView from "@modules/sales/product-proposal/Approval";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.MARKETING.marketingDashboard.key,
  path: ROUTES.MARKETING.marketingDashboard.key,
  component: MarketingDashboard,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
  icon: MdDashboard,
  isSidebar: true,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.MARKETING.notification.key,
  path: ROUTES.MARKETING.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.user],
  icon: MdDashboard,
  //   isSidebar: true,
};

export const commissionAndRequirements: RouteItem = {
  name: "Commission and Requirements",
  id: ROUTES.MARKETING.commissionAndRequirements.key,
  path: ROUTES.MARKETING.commissionAndRequirements.key,
  component: CommissionAndRequirements,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
  icon: HiChevronRight,
};

function capitalizeFirstLetter(str: string): string {
  return str.slice(1).replace(/\b\w/g, (char) => char.toUpperCase());
}
const pageLocation = capitalizeFirstLetter(`${ROUTES.MARKETING.shares.key}`);
export const shares: RouteItem = {
  name: "Shares",
  id: ROUTES.MARKETING.shares.key,
  path: ROUTES.MARKETING.shares.key,
  component: Shares,
  props: { pageLocation },

  guard: AuthGuard,
  roles: [UserRoles.marketing],
  icon: HiChevronRight,
};

export const validation: RouteItem = {
  name: "Validation",
  id: ROUTES.MARKETING.validation.key,
  path: ROUTES.MARKETING.validation.key,
  component: Validation,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
  icon: MdOutlineFactCheck,
  isSidebar: true,
  children: [commissionAndRequirements, shares],
};

export const approval: RouteItem = {
  name: "Approval",
  id: ROUTES.MARKETING.viewProductProposal.key,
  path: ROUTES.MARKETING.viewProductProposal.key,
  component: Approval,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
  isSidebar: false,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.MARKETING.profile.key,
  path: ROUTES.MARKETING.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
};

export const proposal: RouteItem = {
  name: "Proposal",
  id: ROUTES.MARKETING.productProposal.key,
  path: ROUTES.MARKETING.productProposal.key,
  component: ProductProposal,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
  icon: GiNotebook,
  isSidebar: true,
};
export const viewProductProposalSales: RouteItem = {
  name: "View Proposal",
  id: ROUTES.MARKETING.viewProductProposalSales.key,
  path: ROUTES.MARKETING.viewProductProposalSales.key,
  component: ProposalView,
  guard: AuthGuard,
  roles: [UserRoles.marketing],
  icon: GiNotebook,
  isSidebar: false,
};

export const marketingRoutes = [overview, validation, commissionAndRequirements, approval, shares, notification, profile, proposal, viewProductProposalSales];
