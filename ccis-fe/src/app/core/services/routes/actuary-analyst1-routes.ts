import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import RNDDashboard from "@modules/actuary/dashboard";
import { FaFileCircleCheck } from "react-icons/fa6";
import { QuotationClppQuotationView } from "@modules/sales/view-aer/clpp";
import { PiList } from "react-icons/pi";
import { QuotationGyrtQuotationView } from "@modules/sales/view-aer/gyrt";
import { QuotationClspQuotationView } from "@modules/sales/view-aer/clsp";
import QuotationFipAerView from "@modules/sales/view-aer/fipViewPreview";
import AerApprovalTable from "@modules/admin/approval-aer/table/AerApprovalTable";
import MyApprovals from "@modules/admin/approval-aer";
import { HiChevronRight } from "react-icons/hi2";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.ACTUARYANALYST1.dashboard.key,
  path: ROUTES.ACTUARYANALYST1.dashboard.key,
  component: RNDDashboard,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAnalyst1],
  icon: MdDashboard,
  isSidebar: true,
};

export const aerTableApproval: RouteItem = {
  name: "AER Approvals",
  id: ROUTES.ACTUARYANALYST1.aerApproval.key,
  path: ROUTES.ACTUARYANALYST1.aerApproval.key,
  component: AerApprovalTable,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAssistant1],
  icon: HiChevronRight,
  isSidebar: false,
};
export const quotationFipAerView: RouteItem = {
  name: "Quotation FIP Aer View",
  id: ROUTES.ACTUARYANALYST1.quotationFipAerView.key,
  path: ROUTES.ACTUARYANALYST1.quotationFipAerView.key,
  component: QuotationFipAerView,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAnalyst1],
  icon: PiList,
  isSidebar: false,
};
export const quotationClspQuotationView: RouteItem = {
  name: "Quotation CLSP Quotation View",
  id: ROUTES.ACTUARYANALYST1.quotationClspQuotationView.key,
  path: ROUTES.ACTUARYANALYST1.quotationClspQuotationView.key,
  component: QuotationClspQuotationView,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAnalyst1],
  icon: PiList,
  isSidebar: false,
};
export const quotationClppQuotationView: RouteItem = {
  name: "Quotation CLPP Quotation View",
  id: ROUTES.ACTUARYANALYST1.quotationClppQuotationView.key,
  path: ROUTES.ACTUARYANALYST1.quotationClppQuotationView.key,
  component: QuotationClppQuotationView,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAnalyst1],
  icon: PiList,
  isSidebar: false,
};
export const quotationGyrtQuotationView: RouteItem = {
  name: "Quotation GYRT Quotation View",
  id: ROUTES.ACTUARYANALYST1.quotationGyrtQuotationView.key,
  path: ROUTES.ACTUARYANALYST1.quotationGyrtQuotationView.key,
  component: QuotationGyrtQuotationView,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAnalyst1],
  icon: PiList,
  isSidebar: false,
};
export const myApprovals: RouteItem = {
  name: "My Approvals",
  id: ROUTES.ACTUARYANALYST1.myApprovals.key,
  path: ROUTES.ACTUARYANALYST1.myApprovals.key,
  component: MyApprovals,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAnalyst1],
  icon: FaFileCircleCheck,
  isSidebar: true,
  children: [
    aerTableApproval
  ],
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.ACTUARYANALYST1.notification.key,
  path: ROUTES.ACTUARYANALYST1.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAnalyst1],
  icon: MdDashboard,
  //   isSidebar: true,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.ACTUARYANALYST1.profile.key,
  path: ROUTES.ACTUARYANALYST1.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.actuaryAnalyst1],
};

export const actuaryAnalyst1Routes = [
  overview,
  aerTableApproval,
  quotationFipAerView,
  quotationClspQuotationView,
  quotationClppQuotationView,
  quotationGyrtQuotationView,
  myApprovals,
  notification,
  profile
];


