import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import IncomingOutgoingCashierDashboard from "@modules/dashboard/IncomingOutgoingCashierDashboard.tsx";
import NewForms from "@modules/admin-outgoing/fis/new-forms/index.tsx";
import { RiFile2Line } from "react-icons/ri";
import { HiChevronRight } from "react-icons/hi2";
// import IncomingForm from "@modules/admin-outgoing/fis/new-forms/tabs/ForReceiving";
import OutgoingForm from "@modules/admin-outgoing/fis/new-forms/index";
import ViewFormReceivingAdminOutgoing from "@modules/admin-outgoing/fis/components/ViewOutgoingAdminReceiveForm";
import ViewOutgoingAdminTransmittalForm from "@modules/admin-outgoing/fis/components/ViewOutgoingAdminTransmittalForm";
import ViewOutgoingTransmittalTrail from "@modules/admin-outgoing/fis/components/ViewOutgoingAdminTransmittalTrail";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.OUTGOINGADMIN.outgoingAdminDashboard.key,
  path: ROUTES.OUTGOINGADMIN.outgoingAdminDashboard.key,
  component: IncomingOutgoingCashierDashboard,
  guard: AuthGuard,
  roles: [UserRoles.outgoingAdmin],
  icon: MdDashboard,
  isSidebar: true,
};
// export const newFormsIncoming: RouteItem = {
//   name: "Incoming",
//   id: ROUTES.INCOMINGOUTGOINGCASHIER.newFormsIncoming.key,
//   path: ROUTES.INCOMINGOUTGOINGCASHIER.newFormsIncoming.key,
//   component: IncomingForm,
//   guard: AuthGuard,
//   roles: [UserRoles.outgoingAdmin],
//   icon: HiChevronRight,
//   // isSidebar: true,
// };
export const newFormsOutgoing: RouteItem = {
  name: "Outgoing",
  id: ROUTES.OUTGOINGADMIN.outgoingAdminNewFormsOutgoing.key,
  path: ROUTES.OUTGOINGADMIN.outgoingAdminNewFormsOutgoing.key,
  component: OutgoingForm,
  guard: AuthGuard,
  roles: [UserRoles.outgoingAdmin],
  icon: HiChevronRight,
  // isSidebar: true,
};
export const newForms: RouteItem = {
  name: "New Forms",
  id: ROUTES.OUTGOINGADMIN.outgoingAdminNewForms.key,
  path: ROUTES.OUTGOINGADMIN.outgoingAdminNewForms.key,
  component: NewForms,
  guard: AuthGuard,
  roles: [UserRoles.outgoingAdmin],
  icon: RiFile2Line,
  children: [newFormsOutgoing],
  isSidebar: true,
};
export const forReceivingFormAdminOutgoing: RouteItem = {
  name: "For Receiving Form",
  id: ROUTES.OUTGOINGADMIN.forOutgoingAdminReceivingForm.key,
  path: ROUTES.OUTGOINGADMIN.forOutgoingAdminReceivingForm.key,
  component: ViewFormReceivingAdminOutgoing,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  isSidebar: false,
};
export const viewOutgoingAdminTransmittal: RouteItem = {
  name: "View New Transmittal Form",
  id: ROUTES.OUTGOINGADMIN.viewOutgoingAdminTransmittal.key,
  path: ROUTES.OUTGOINGADMIN.viewOutgoingAdminTransmittal.key,
  component: ViewOutgoingAdminTransmittalForm,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  isSidebar: false,
};
export const viewOutgoingAdminTransmittalTrail: RouteItem = {
  name: "View New Transmittal Trail",
  id: ROUTES.OUTGOINGADMIN.viewOutgoingAdminTransmittalTrail.key,
  path: ROUTES.OUTGOINGADMIN.viewOutgoingAdminTransmittalTrail.key,
  component: ViewOutgoingTransmittalTrail,
  guard: AuthGuard,
  roles: [UserRoles.ioc],
  isSidebar: false,
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.OUTGOINGADMIN.notification.key,
  path: ROUTES.OUTGOINGADMIN.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.outgoingAdmin],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.OUTGOINGADMIN.profile.key,
  path: ROUTES.OUTGOINGADMIN.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.outgoingAdmin],
};

export const outgoingAdminRoutes = [
  overview,
  newForms,
  newFormsOutgoing,
  forReceivingFormAdminOutgoing,
  viewOutgoingAdminTransmittal,
  viewOutgoingAdminTransmittalTrail,
  notification,
  profile
];
