import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import ProductProposalApproval from "@modules/admin/product-proposal-approval-claims";
import ViewProductProposal from "@modules/admin/product-proposal-approval-claims/Components/viewForm";
import ClaimsDashboard from "@modules/dashboard/ClaimsDashboard";
import RequestorDashboard from "@modules/dashboard/RequestorDashboard";
import RequestForm from "@modules/dashboard/RequestorDashboard/components/RequestForm";
import ViewRequestForm from "@modules/dashboard/RequestorDashboard/components/ViewRequestForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import { LuFileCheck } from "react-icons/lu";
import { MdDashboard } from "react-icons/md";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.CLAIMS.claimsDashboard.key,
  path: ROUTES.CLAIMS.claimsDashboard.key,
  component: ClaimsDashboard,
  guard: AuthGuard,
  roles: [UserRoles.claims],
  icon: MdDashboard,
  isSidebar: true,
};

export const productProposalApproval: RouteItem = {
  name: "My Approvals",
  id: ROUTES.CLAIMS.claimsProductProposalApproval.key,
  path: ROUTES.CLAIMS.claimsProductProposalApproval.key,
  component: ProductProposalApproval,
  guard: AuthGuard,
  isSidebar: true,
  icon: LuFileCheck,
  roles: [UserRoles.claims],
};

export const viewProductProposalApproval: RouteItem = {
  name: "My Approvals",
  id: ROUTES.CLAIMS.viewClaimsProductProposalApproval.key,
  path: ROUTES.CLAIMS.viewClaimsProductProposalApproval.key,
  component: ViewProductProposal,
  guard: AuthGuard,
  isSidebar: false,
  icon: LuFileCheck,
  roles: [UserRoles.claims],
};

export const requestDashboard: RouteItem = {
  name: "Request Dashboard",
  id: ROUTES.CLAIMS.requestDashboard.key,
  path: ROUTES.CLAIMS.requestDashboard.key,
  component: RequestorDashboard,
  guard: AuthGuard,
  roles: [UserRoles.claims],
  icon: MdDashboard,
  isSidebar: true,
};

export const requestForm: RouteItem = {
  name: "Request Form",
  id: ROUTES.CLAIMS.requestForm.key,
  path: ROUTES.CLAIMS.requestForm.key,
  component: RequestForm,
  guard: AuthGuard,
  roles: [UserRoles.claims],
  isSidebar: false,
};

export const viewRequest: RouteItem = {
  name: "View Request",
  id: ROUTES.CLAIMS.viewRequestForm.key,
  path: ROUTES.CLAIMS.viewRequestForm.key,
  component: ViewRequestForm,
  guard: AuthGuard,
  roles: [UserRoles.claims],
  isSidebar: false,
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.CLAIMS.notification.key,
  path: ROUTES.CLAIMS.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.claims],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.CLAIMS.profile.key,
  path: ROUTES.CLAIMS.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.claims],
};

export const claimsRoutes = [overview, productProposalApproval, viewProductProposalApproval, requestDashboard, requestForm, viewRequest, notification, profile];
