import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles} from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import UATGuard from "@layouts/AuthGuard";
import ParentComponent from "@modules/admin/user-acceptance-test/uat-users/Components/parent";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";
import { MdDashboard } from "react-icons/md";

export const uatUser: RouteItem = {
    name: "uatUser",
    id: ROUTES.UATUSERS.uatUser.key,
    path: ROUTES.UATUSERS.uatUser.key,
    component: ParentComponent,
    guard: UATGuard,
};
export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.TREASURY.notification.key,
  path: ROUTES.TREASURY.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.user],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.TREASURY.profile.key,
  path: ROUTES.TREASURY.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.user],
};

export const uatUserRoutes = [uatUser, notification, profile];
