import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { LuFileCheck2 } from "react-icons/lu";
import { MdDashboard } from "react-icons/md";
import Compliance from "@modules/admin/compliance";
import ComplianceDashboard from "@modules/dashboard/ComplianceDashboard";
import Details from "@modules/admin/compliance/Details";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.COMPLIANCE.complianceDashboard.key,
  path: ROUTES.COMPLIANCE.complianceDashboard.key,
  component: ComplianceDashboard,
  guard: AuthGuard,
  roles: [UserRoles.compliance],
  icon: MdDashboard,
  isSidebar: true,
};

export const compliance: RouteItem = {
    name: "Compliance",
    id: ROUTES.COMPLIANCE.compliance.key,
    path: ROUTES.COMPLIANCE.compliance.key,
    component: Compliance,
    guard: AuthGuard,
    roles: [UserRoles.admin],
    icon: LuFileCheck2,
    isSidebar: true,
  };
  
  export const viewCompliance: RouteItem = {
    name: "Compliance",
    id: ROUTES.COMPLIANCE.viewCompliance.key,
    path: ROUTES.COMPLIANCE.viewCompliance.key,
    component: Details,
    guard: AuthGuard,
    roles: [UserRoles.admin],
    icon: LuFileCheck2,
    isSidebar: false,
  };
  export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.COMPLIANCE.notification.key,
  path: ROUTES.COMPLIANCE.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.compliance],
  icon: MdDashboard,
  //   isSidebar: true,
};
export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.COMPLIANCE.profile.key,
  path: ROUTES.COMPLIANCE.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.compliance],
};


export const complianceRoutes = [
  overview,
  compliance,
  viewCompliance,
  notification,
  profile
];
