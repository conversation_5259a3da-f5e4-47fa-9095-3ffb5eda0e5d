import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import IncomingOutgoingCashierDashboard from "@modules/dashboard/IncomingOutgoingCashierDashboard.tsx";
import { PiCertificate, PiFile } from "react-icons/pi";
import Shares from "@modules/admin/shares";
import TreasuryNewForms from "@modules/treasury/treasury-new-forms";
import ViewForm from "@modules/treasury/treasury-new-forms/forms/ViewForm";
import TreasuryRequestForms from "@modules/treasury/request-pads";
import { FaFileCirclePlus } from "react-icons/fa6";
import RequestPadForm from "@modules/treasury/request-pads/form/RequestPadForm";
import NotificationPage from "@modules/shared/notification";
import Profile from "@modules/shared/profile";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.TREASURY.treasuryDashboard.key,
  path: ROUTES.TREASURY.treasuryDashboard.key,
  component: IncomingOutgoingCashierDashboard,
  guard: AuthGuard,
  roles: [UserRoles.treasury],
  icon: MdDashboard,
  isSidebar: true,
};
export const shares: RouteItem = {
  name: "Shares",
  id: ROUTES.TREASURY.shares.key,
  path: ROUTES.TREASURY.shares.key,
  component: Shares,
  guard: AuthGuard,
  roles: [UserRoles.treasury],
  icon: PiCertificate,
  isSidebar: true,
};
export const newForms: RouteItem = {
  name: "New Forms",
  id: ROUTES.TREASURY.newForms.key,
  path: ROUTES.TREASURY.newForms.key,
  component: TreasuryNewForms,
  guard: AuthGuard,
  roles: [UserRoles.treasury],
  icon: PiFile,
  isSidebar: true,
};
export const viewForm: RouteItem = {
  name: "View Form",
  id: ROUTES.TREASURY.viewForm.key,
  path: ROUTES.TREASURY.viewForm.key,
  component: ViewForm,
  guard: AuthGuard,
  roles: [UserRoles.treasury],
};
export const requestsPads: RouteItem = {
  name: "Requests Pads",
  id: ROUTES.TREASURY.requestPads.key,
  path: ROUTES.TREASURY.requestPads.key,
  component: TreasuryRequestForms,
  guard: AuthGuard,
  roles: [UserRoles.treasury],
  isSidebar: true,
  icon: FaFileCirclePlus,
};
export const requestPadForm: RouteItem = {
  name: "View Request Pad Form",
  id: ROUTES.TREASURY.requestPadForm.key,
  path: ROUTES.TREASURY.requestPadForm.key,
  component: RequestPadForm,
  guard: AuthGuard,
  roles: [UserRoles.treasury],
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.TREASURY.notification.key,
  path: ROUTES.TREASURY.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.treasury],
  icon: MdDashboard,
  //   isSidebar: true,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.TREASURY.profile.key,
  path: ROUTES.TREASURY.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.treasury],
};
export const treasuryRoutes = [overview, shares, newForms, viewForm, requestsPads, requestPadForm, notification, profile];
