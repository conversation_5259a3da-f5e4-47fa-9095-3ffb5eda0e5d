import { ROUTES } from "@constants/routes";
import { RouteItem, UserRoles } from "@interface/routes.interface";
import AuthGuard from "@layouts/AuthGuard";
import { MdDashboard } from "react-icons/md";
import IncomingOutgoingCashierDashboard from "@modules/dashboard/IncomingOutgoingCashierDashboard.tsx";
import { FaFile, FaFileAlt } from "react-icons/fa";
import ClifsaNewForms from "@modules/clifsa/form-inventory/new-forms";
import TransmittalTab from "@modules/clifsa/form-inventory/new-forms/components/TransmittalTab";
import ViewFormReceiving from "@modules/clifsa/form-inventory/new-forms/components/Forms/ForReceivingView";
import RequestPadForm from "@modules/clifsa/form-inventory/request-pads/components/Forms/RequestPadForm";
import ViewTransmittalForm from "@modules/clifsa/form-inventory/new-forms/components/Forms/TransmittalForm";
import ViewReleasedForms from "@modules/clifsa/form-inventory/new-forms/components/ViewReleasedForm";
import ClifsaRequestForms from "@modules/clifsa/form-inventory/request-pads";
import ClifsaUsedForms from "@modules/clifsa/form-inventory/used-forms";
import { FaFileCirclePlus } from "react-icons/fa6";
import NotificationPage from "@modules/shared/notification";
import TransmittalReturnedForm from "@modules/clifsa/form-inventory/used-forms/components/Forms/ForReceivingView";
import ViewReturnTransmittalForm from "@modules/clifsa/form-inventory/used-forms/components/Forms/TransmittalForm";
import ViewReturnedForm from "@modules/clifsa/form-inventory/used-forms/components/ViewReturnedForm";
import Profile from "@modules/shared/profile";

export const overview: RouteItem = {
  name: "Dashboard",
  id: ROUTES.CLIFSAADMIN.clifsaAdminDashboard.key,
  path: ROUTES.CLIFSAADMIN.clifsaAdminDashboard.key,
  component: IncomingOutgoingCashierDashboard,
  guard: AuthGuard,
  roles: [UserRoles.clifsaAdmin],
  icon: MdDashboard,
  isSidebar: true,
};

export const notification: RouteItem = {
  name: "Notifications",
  id: ROUTES.CLIFSAADMIN.notification.key,
  path: ROUTES.CLIFSAADMIN.notification.key,
  component: NotificationPage,
  guard: AuthGuard,
  roles: [UserRoles.user],
  icon: MdDashboard,
  //   isSidebar: true,
};

export const forReceivingForms: RouteItem = {
  name: "For Receiving Forms",
  id: ROUTES.CLIFSAADMIN.clifsaAdminForReceivingForms.key,
  path: ROUTES.CLIFSAADMIN.clifsaAdminForReceivingForms.key,
  component: TransmittalTab,
  guard: AuthGuard,
  roles: [UserRoles.clifsaAdmin],
  isSidebar: false,
};

export const newForm: RouteItem = {
  name: "New Form",
  id: ROUTES.CLIFSAADMIN.clifsaAdminNewForm.key,
  path: ROUTES.CLIFSAADMIN.clifsaAdminNewForm.key,
  component: ClifsaNewForms,
  guard: AuthGuard,
  roles: [UserRoles.clifsaAdmin],
  icon: FaFile,
  isSidebar: true,
};

export const viewForReceivingForm: RouteItem = {
  name: "View For Receiving Form",
  id: ROUTES.CLIFSAADMIN.viewForReceivingForm.key,
  path: ROUTES.CLIFSAADMIN.viewForReceivingForm.key,
  component: ViewFormReceiving,
  guard: AuthGuard,
  roles: [UserRoles.clifsaAdmin],
  isSidebar: false,
};

export const transmittalViewForm: RouteItem = {
  name: "View Transmittal Form",
  id: ROUTES.CLIFSAADMIN.viewTransmittalForm.key,
  path: ROUTES.CLIFSAADMIN.viewTransmittalForm.key,
  component: ViewTransmittalForm,
  guard: AuthGuard,
  roles: [UserRoles.clifsaAdmin],
  isSidebar: false,
};

export const viewReleasedForm: RouteItem = {
  name: "View Released Form",
  id: ROUTES.CLIFSAADMIN.viewReleasedForm.key,
  path: ROUTES.CLIFSAADMIN.viewReleasedForm.key,
  component: ViewReleasedForms,
  guard: AuthGuard,
  roles: [UserRoles.clifsaAdmin],
  isSidebar: false,
};

export const usedForms: RouteItem = {
  name: "Used Forms",
  id: ROUTES.CLIFSAADMIN.usedForms.key,
  path: ROUTES.CLIFSAADMIN.usedForms.key,
  component: ClifsaUsedForms,
  guard: AuthGuard,
  roles: [UserRoles.clifsaAdmin],
  isSidebar: true,
  icon: FaFileAlt,
};

export const viewReturnedReceivingForm: RouteItem = {
  name: "View Returned Receiving Form",
  id: ROUTES.CLIFSAADMIN.viewReturnedReceivingForm.key,
  path: ROUTES.CLIFSAADMIN.viewReturnedReceivingForm.key,
  component: TransmittalReturnedForm,
  guard: AuthGuard,
  roles: [UserRoles.clifsaAdmin],
  isSidebar: false,
};

export const returnTransmittalViewForm: RouteItem = {
  name: "View Return Transmittal Form",
  id: ROUTES.CLIFSAADMIN.viewReturnTransmittalForm.key,
  path: ROUTES.CLIFSAADMIN.viewReturnTransmittalForm.key,
  component: ViewReturnTransmittalForm,
  guard: AuthGuard,
  roles: [UserRoles.clifsaAdmin],
  isSidebar: false,
};


export const viewReturnedForm: RouteItem = {
  name: "View Returned Form",
  id: ROUTES.CLIFSAADMIN.viewReturnedForm.key,
  path: ROUTES.CLIFSAADMIN.viewReturnedForm.key,
  component: ViewReturnedForm,
  guard: AuthGuard,
  roles: [UserRoles.clifsaAdmin],
  isSidebar: false,
};

export const requestsPads: RouteItem = {
  name: "Requests Pads",
  id: ROUTES.CLIFSAADMIN.requestPads.key,
  path: ROUTES.CLIFSAADMIN.requestPads.key,
  component: ClifsaRequestForms,
  guard: AuthGuard,
  roles: [UserRoles.clifsaAdmin],
  isSidebar: true,
  icon: FaFileCirclePlus,
};

export const requestPadForm: RouteItem = {
  name: "View Request Pad Form",
  id: ROUTES.CLIFSAADMIN.requestPadForm.key,
  path: ROUTES.CLIFSAADMIN.requestPadForm.key,
  component: RequestPadForm,
  guard: AuthGuard,
  roles: [UserRoles.clifsaAdmin],
  isSidebar: false,
};

export const profile: RouteItem = {
  name: "User Profile",
  id: ROUTES.CLIFSAADMIN.profile.key,
  path: ROUTES.CLIFSAADMIN.profile.key,
  component: Profile,
  guard: AuthGuard,
  roles: [UserRoles.clifsaAdmin],
};


export const clifsaAdminRoutes = [overview, newForm, viewForReceivingForm, transmittalViewForm, viewReleasedForm, usedForms, requestsPads, notification, requestPadForm, viewReturnedReceivingForm, returnTransmittalViewForm, viewReturnedForm, profile];
