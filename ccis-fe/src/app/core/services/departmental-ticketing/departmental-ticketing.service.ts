import httpClient from "@clients/httpClient";
import { TAssignTicketUserPayload, TDepartmentalTicketPayload, TDepartmentalTicketWithIDAndIndexPayload, TTicketDepartmentUsersPayload } from "@state/types/departmental-ticketing";

//For new GET
import { IDefaultParams } from "@interface/common.interface";
import { AxiosResponse } from "axios";
import { IExportReportPayload, IForwardTicketPayload, ISaveAttachmentsPayload, ITicketCommentPayload, ITicketUpdateStatusPayload, TITicketExtendDatePayload } from "@interface/departmental-ticketing-interface";
import { PostForwardTicketSchema } from "./departmental-ticketing.schema";
import { UpdateTicketApprovalPayload } from "@interface/update-ticket-approval";


//For grabbing the current type being used
const apiResource = "ticket";
const apiTicketAssigneeResource = "ticket-assignee";
const departmentResource = "departments";

export const getTicketService = async (id?: number) => {
  return httpClient.get(`${apiResource}/${id}`);
};

export const getTicketsService = async (params: IDefaultParams) => {
  let queryParams = ``;

  if (params.relations) {
    queryParams += `relations=${params.relations}`;
  }

  if (params.id) {
    queryParams += `id[eq]=${params.id}`;
  }

  //For pagination
  if (params.page) {
    queryParams += `&pageSize=${params.page}`;
  }
  if (params.pageSize) {
    queryParams += `&pageSize=${params.pageSize}`;
  }
  //Filters:
  //For filtering the ticket name
  if (params.filter) {
    queryParams += `&name[like]=${params.filter}`;
  }
  if (params.statusFilter) {
    queryParams += `&status[${params.eq ? "eq" : "neq"}]=${params.statusFilter}`;
    queryParams += `&page=${(params.page ?? 1).toString()}`;
    queryParams += `&pageSize=${(params.rows ?? 10).toString()}`;
    queryParams += `&sort=${params.sort ?? "id,desc"}`;
  }
  if (params.condition) {
    queryParams += `&${params.condition}`;
  }
  return httpClient.get(`${apiResource}?${queryParams}`);
};

export const getPriorityTicketsService = getTicketsService;
export const getDueTicketsService = getTicketsService;
export const getGraphTicketsService = getTicketsService;

export const postTicketService = async (payload: TDepartmentalTicketPayload) => {
  return httpClient.post(`${apiResource}`, payload);
};

export const putTicketService = async (payload: TDepartmentalTicketPayload) => {
  return httpClient.put(`${apiResource}/${payload.id}`, payload);
};

export const destroyTicketService = async (payload: TDepartmentalTicketWithIDAndIndexPayload) => {
  return httpClient.delete(`${apiResource}/${payload.id}`);
};

export const getTicketDepartmentUsersService = async (payload: TTicketDepartmentUsersPayload): Promise<AxiosResponse> => {
  return httpClient.get(`/users?departmentId[eq]=${payload.departmentId}&relations=${payload.relations || ""}`);
};

export const postAssignTicketUserService = async (payload: TAssignTicketUserPayload): Promise<AxiosResponse> => {
  return httpClient.post(`/ticket-assignee/assign/ticket/${payload.ticketId}/user/${payload.userId}`);
};

export const getTicketDetailsByIdService = async (params: IDefaultParams): Promise<AxiosResponse> => {
  let query = `${apiTicketAssigneeResource}/me?`;

  // Default relations if not provided

  // Add relations
  if (params.relations) {
    query += `relations=${params.relations}`;
  }

  // Add ID filter (required)
  if (params.id) {
    query += `&id[eq]=${params.id}`;
  }

  // Add pagination if provided
  if (params.page) {
    query += `&page=${params.page}`;
  }
  if (params.pageSize) {
    query += `&pageSize=${params.pageSize}`;
  }

  // Add sorting if provided
  if (params.sort) {
    query += `&sort=${params.sort}`;
  }

  // Add any additional filters
  if (params.filter) {
    query += `&name[like]=${params.filter}`;
  }
  if (params.statusFilter) {
    query += `&status[eq]=${params.statusFilter}`;
  }
  if (params.dateFrom && params.dateTo) {
    query += `&createdAt[between]=${params.dateFrom},${params.dateTo}`;
  }

  return httpClient.get(query);
};

export const getTicketsInfoService = async (params: IDefaultParams) => {
  let queryParams = ``;

  if (params.relations) {
    queryParams += `relations=${params.relations}`;
  }

  if (params.id) {
    queryParams += `id[eq]=${params.id}`;
  }

  //For pagination
  if (params.page) {
    queryParams += `&pageSize=${params.page}`;
  }
  if (params.pageSize) {
    queryParams += `&pageSize=${params.pageSize}`;
  }
  //Filters:
  //For filtering the ticket name
  if (params.filter) {
    queryParams += `&name[like]=${params.filter}`;
  }
  if (params.statusFilter) {
    queryParams += `&status[${params.eq ? "eq" : "neq"}]=${params.statusFilter}`;
    queryParams += `&page=${(params.page ?? 1).toString()}`;
    queryParams += `&pageSize=${(params.rows ?? 10).toString()}`;
    queryParams += `&sort=${params.sort ?? "id,desc"}`;
  }
  if (params.condition) {
    queryParams += `&${params.condition}`;
  }
  return httpClient.get(`${apiTicketAssigneeResource}/me?${queryParams}`);
};

export const putTicketExtendDateService = async (payload: TITicketExtendDatePayload): Promise<AxiosResponse> => {
  return httpClient.post(`/ticket/${payload.ticketId}/setExtendedDate`, {
    extendedDate: payload.extendedDate,
    extensionRemarks: payload.extensionRemarks,
  });
};

export const postForwardTicketService = (payload: IForwardTicketPayload) => {
  PostForwardTicketSchema.validateSync(payload);
  return httpClient.post("/ticket-assignee/forward", payload);
};

export const getDepartmentsService = () => {
  return httpClient.get(departmentResource);
};

export const updateTicketStatusService = async (ticketId: number, payload: ITicketUpdateStatusPayload) => {
  return httpClient.post(`/ticket/${ticketId}/status`, payload);
};

export const updateTicketApprovalService = async (ticketId: number, payload: UpdateTicketApprovalPayload) => {
  return httpClient.post(`/ticket-assignee/approval/${ticketId}`, payload);
};

export const postTicketCommentService = async (payload: ITicketCommentPayload) => {
  return httpClient.post(`/ticket-comment`, payload);
};

export const postTicketCommentAttachmentService = async (payload: ISaveAttachmentsPayload) => {
  const formData = new FormData();
  formData.append("attachableType", payload.attachableType);
  formData.append("attachableId", payload.attachableId.toString());

  payload.files.forEach((file, index) => {
    formData.append(`attachments[${index}][file]`, file);
  });

  return httpClient.post(`/shared/attachments`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

export const postClosureStatusService = async (ticketId: number, payload: ITicketUpdateStatusPayload) => {
  return httpClient.post(`/ticket/${ticketId}/closure/status`, payload);
}

export const getExportReportService = async (payload: IExportReportPayload) => {
  const queryParams = new URLSearchParams({
    departmentId: payload.departmentId.toString(),
    status: payload.status,
    dateFrom: payload.CreatedDateFrom,
    dateTo: payload.CreatedDateTo,
    fileType: payload.fileType,
  }).toString();

  return httpClient.get(`/ticket/generate-report?${queryParams}`, {
    responseType: 'blob',
  });
}


