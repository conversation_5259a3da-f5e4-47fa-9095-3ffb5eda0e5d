@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: "Regular";
  src: url("/assets/font/Poppins-Regular.ttf");
}

@font-face {
  font-family: "Medium";
  src: url("/assets/font/Poppins-Medium.ttf");
}

@font-face {
  font-family: "SemiBold";
  src: url("/assets/font/Poppins-SemiBold.ttf");
}

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
  /* Show scrollbar */
  .scrollbar::-webkit-scrollbar {
    display: block;
  }
  .scrollbar {
    -ms-overflow-style: auto; /* IE and Edge */
    scrollbar-width: auto; /* Firefox */
  }
}

/* CSS for the WYSIWIG */
ol {
  list-style: number;
}

ol > li {
  margin: 1rem 0;
  margin-left: 2rem;
}

ul {
  list-style: disc;
}

ul > li {
  margin: 0.5rem 0;
  margin-left: 1rem;
}

.glass {
  background: url("/assets/sidebar-bg-dark.png");
  background-position: bottom left;
  background-size: cover;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(0, 0, 0, 0.2);
}
